# Test info

- Name: Advanced Features E2E Tests >> Export Functionality >> should open export dialog with keyboard shortcut
- Location: /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:68:5

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('[data-testid="export-dialog"]')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('[data-testid="export-dialog"]')

    at /Users/<USER>/Desktop/EMS/frontend/e2e/advanced-features.e2e.test.ts:73:67
```

# Page snapshot

```yaml
- heading "مرحباً بك في نمو" [level=1]
- paragraph: نظام إدارة المؤسسات المتكامل
- text: تسجيل الدخول أدخل بياناتك للوصول إلى النظام اسم المستخدم
- textbox "اسم المستخدم"
- text: كلمة المرور
- textbox "كلمة المرور"
- button
- checkbox "تذكرني"
- text: تذكرني
- button "نسيت كلمة المرور؟"
- button "تسجيل الدخول" [disabled]
- heading "حسابات تجريبية" [level=3]
- text: "مدير النظام الرئيسي:"
- code: superadmin / superadmin123
- text: "Admin:"
- code: admin / admin123
- text: "مدير الموارد البشرية:"
- code: hrmanager / password123
- text: "مدير المالية:"
- code: financemanager / password123
- text: "موظف:"
- code: employee1 / password123
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test'
   2 |
   3 | test.describe('Advanced Features E2E Tests', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to the application
   6 |     await page.goto('/')
   7 |     
   8 |     // Wait for the application to load
   9 |     await page.waitForLoadState('networkidle')
   10 |   })
   11 |
   12 |   test.describe('Global Search', () => {
   13 |     test('should open search with keyboard shortcut', async ({ page }) => {
   14 |       // Press Ctrl+K to open search
   15 |       await page.keyboard.press('Control+k')
   16 |       
   17 |       // Verify search dialog is open
   18 |       await expect(page.locator('[data-testid="global-search"]')).toBeVisible()
   19 |       
   20 |       // Verify search input is focused
   21 |       await expect(page.locator('[data-testid="search-input"]')).toBeFocused()
   22 |     })
   23 |
   24 |     test('should perform search and show results', async ({ page }) => {
   25 |       // Open search
   26 |       await page.keyboard.press('Control+k')
   27 |       
   28 |       // Type search query
   29 |       await page.fill('[data-testid="search-input"]', 'employee')
   30 |       
   31 |       // Wait for search results
   32 |       await page.waitForSelector('[data-testid="search-results"]')
   33 |       
   34 |       // Verify results are displayed
   35 |       await expect(page.locator('[data-testid="search-result-item"]')).toHaveCount.greaterThan(0)
   36 |     })
   37 |
   38 |     test('should show search suggestions', async ({ page }) => {
   39 |       // Open search
   40 |       await page.keyboard.press('Control+k')
   41 |       
   42 |       // Type partial query
   43 |       await page.fill('[data-testid="search-input"]', 'emp')
   44 |       
   45 |       // Wait for suggestions
   46 |       await page.waitForSelector('[data-testid="search-suggestions"]')
   47 |       
   48 |       // Verify suggestions are displayed
   49 |       await expect(page.locator('[data-testid="suggestion-item"]')).toHaveCount.greaterThan(0)
   50 |     })
   51 |
   52 |     test('should close search with Escape key', async ({ page }) => {
   53 |       // Open search
   54 |       await page.keyboard.press('Control+k')
   55 |       
   56 |       // Verify search is open
   57 |       await expect(page.locator('[data-testid="global-search"]')).toBeVisible()
   58 |       
   59 |       // Press Escape to close
   60 |       await page.keyboard.press('Escape')
   61 |       
   62 |       // Verify search is closed
   63 |       await expect(page.locator('[data-testid="global-search"]')).not.toBeVisible()
   64 |     })
   65 |   })
   66 |
   67 |   test.describe('Export Functionality', () => {
   68 |     test('should open export dialog with keyboard shortcut', async ({ page }) => {
   69 |       // Press Ctrl+E to open export
   70 |       await page.keyboard.press('Control+e')
   71 |       
   72 |       // Verify export dialog is open
>  73 |       await expect(page.locator('[data-testid="export-dialog"]')).toBeVisible()
      |                                                                   ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   74 |     })
   75 |
   76 |     test('should select export format', async ({ page }) => {
   77 |       // Open export dialog
   78 |       await page.keyboard.press('Control+e')
   79 |       
   80 |       // Select PDF format
   81 |       await page.click('[data-testid="format-pdf"]')
   82 |       
   83 |       // Verify PDF format is selected
   84 |       await expect(page.locator('[data-testid="format-pdf"]')).toHaveClass(/selected|active/)
   85 |     })
   86 |
   87 |     test('should configure export columns', async ({ page }) => {
   88 |       // Open export dialog
   89 |       await page.keyboard.press('Control+e')
   90 |       
   91 |       // Uncheck a column
   92 |       await page.uncheck('[data-testid="column-checkbox"]:first-child')
   93 |       
   94 |       // Verify column is unchecked
   95 |       await expect(page.locator('[data-testid="column-checkbox"]:first-child')).not.toBeChecked()
   96 |     })
   97 |
   98 |     test('should start export process', async ({ page }) => {
   99 |       // Open export dialog
  100 |       await page.keyboard.press('Control+e')
  101 |       
  102 |       // Click export button
  103 |       await page.click('[data-testid="export-button"]')
  104 |       
  105 |       // Verify export progress is shown
  106 |       await expect(page.locator('[data-testid="export-progress"]')).toBeVisible()
  107 |     })
  108 |   })
  109 |
  110 |   test.describe('Notification Center', () => {
  111 |     test('should open notifications with keyboard shortcut', async ({ page }) => {
  112 |       // Press Ctrl+N to open notifications
  113 |       await page.keyboard.press('Control+n')
  114 |       
  115 |       // Verify notification center is open
  116 |       await expect(page.locator('[data-testid="notification-center"]')).toBeVisible()
  117 |     })
  118 |
  119 |     test('should filter notifications', async ({ page }) => {
  120 |       // Open notifications
  121 |       await page.keyboard.press('Control+n')
  122 |       
  123 |       // Click unread filter
  124 |       await page.click('[data-testid="filter-unread"]')
  125 |       
  126 |       // Verify filter is applied
  127 |       await expect(page.locator('[data-testid="filter-unread"]')).toHaveClass(/active|selected/)
  128 |     })
  129 |
  130 |     test('should mark notification as read', async ({ page }) => {
  131 |       // Open notifications
  132 |       await page.keyboard.press('Control+n')
  133 |       
  134 |       // Click mark as read button on first notification
  135 |       await page.click('[data-testid="notification-item"]:first-child [data-testid="mark-read"]')
  136 |       
  137 |       // Verify notification is marked as read
  138 |       await expect(page.locator('[data-testid="notification-item"]:first-child')).toHaveClass(/read/)
  139 |     })
  140 |
  141 |     test('should search notifications', async ({ page }) => {
  142 |       // Open notifications
  143 |       await page.keyboard.press('Control+n')
  144 |       
  145 |       // Type in search input
  146 |       await page.fill('[data-testid="notification-search"]', 'system')
  147 |       
  148 |       // Verify filtered results
  149 |       await expect(page.locator('[data-testid="notification-item"]')).toHaveCount.greaterThan(0)
  150 |     })
  151 |   })
  152 |
  153 |   test.describe('Responsive Design', () => {
  154 |     test('should work on mobile devices', async ({ page }) => {
  155 |       // Set mobile viewport
  156 |       await page.setViewportSize({ width: 375, height: 667 })
  157 |       
  158 |       // Navigate to app
  159 |       await page.goto('/')
  160 |       
  161 |       // Verify mobile layout
  162 |       await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
  163 |     })
  164 |
  165 |     test('should work on tablet devices', async ({ page }) => {
  166 |       // Set tablet viewport
  167 |       await page.setViewportSize({ width: 768, height: 1024 })
  168 |       
  169 |       // Navigate to app
  170 |       await page.goto('/')
  171 |       
  172 |       // Verify tablet layout
  173 |       await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
```