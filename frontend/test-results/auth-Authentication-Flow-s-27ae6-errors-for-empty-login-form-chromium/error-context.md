# Test info

- Name: Authentication Flow >> should show validation errors for empty login form
- Location: /Users/<USER>/Desktop/EMS/frontend/e2e/auth.spec.ts:19:3

# Error details

```
Error: page.click: Test ended.
Call log:
  - waiting for locator('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")')
    - locator resolved to <button disabled type="submit" data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-…>تسجيل الدخول</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    - waiting for element to be visible, enabled and stable
    - element is not enabled
  2 × retrying click action
      - waiting 100ms
      - waiting for element to be visible, enabled and stable
      - element is not stable
  28 × retrying click action
       - waiting 500ms
       - waiting for element to be visible, enabled and stable
       - element is not enabled
  - retrying click action
    - waiting 500ms
    - waiting for element to be visible, enabled and stable

    at /Users/<USER>/Desktop/EMS/frontend/e2e/auth.spec.ts:21:16
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Authentication Flow', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to the application
   6 |     await page.goto('/');
   7 |   });
   8 |
   9 |   test('should display login page for unauthenticated users', async ({ page }) => {
  10 |     // Should redirect to login or show login form
  11 |     await expect(page).toHaveURL(/.*login.*/);
  12 |     
  13 |     // Check for login form elements
  14 |     await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
  15 |     await expect(page.locator('input[type="password"]')).toBeVisible();
  16 |     await expect(page.locator('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")')).toBeVisible();
  17 |   });
  18 |
  19 |   test('should show validation errors for empty login form', async ({ page }) => {
  20 |     // Try to submit empty form
> 21 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
     |                ^ Error: page.click: Test ended.
  22 |     
  23 |     // Should show validation errors
  24 |     await expect(page.locator('text=/required|مطلوب|field|حقل/i')).toBeVisible();
  25 |   });
  26 |
  27 |   test('should show error for invalid credentials', async ({ page }) => {
  28 |     // Fill in invalid credentials
  29 |     await page.fill('input[type="text"], input[type="email"]', '<EMAIL>');
  30 |     await page.fill('input[type="password"]', 'wrongpassword');
  31 |     
  32 |     // Submit form
  33 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  34 |     
  35 |     // Should show error message
  36 |     await expect(page.locator('text=/invalid|خطأ|error|غير صحيح/i')).toBeVisible();
  37 |   });
  38 |
  39 |   test('should successfully login with valid credentials', async ({ page }) => {
  40 |     // Fill in valid credentials (using the superuser we created)
  41 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  42 |     await page.fill('input[type="password"]', 'admin123');
  43 |     
  44 |     // Submit form
  45 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  46 |     
  47 |     // Should redirect to dashboard or main app
  48 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  49 |     
  50 |     // Should show authenticated user interface
  51 |     await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  52 |   });
  53 |
  54 |   test('should maintain session after page refresh', async ({ page }) => {
  55 |     // Login first
  56 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  57 |     await page.fill('input[type="password"]', 'admin123');
  58 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  59 |     
  60 |     // Wait for successful login
  61 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  62 |     
  63 |     // Refresh the page
  64 |     await page.reload();
  65 |     
  66 |     // Should still be authenticated
  67 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  68 |     await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  69 |   });
  70 |
  71 |   test('should logout successfully', async ({ page }) => {
  72 |     // Login first
  73 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  74 |     await page.fill('input[type="password"]', 'admin123');
  75 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  76 |     
  77 |     // Wait for successful login
  78 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  79 |     
  80 |     // Find and click logout button
  81 |     await page.click('button:has-text("Logout"), button:has-text("تسجيل الخروج"), [data-testid="logout-button"]');
  82 |     
  83 |     // Should redirect to login page
  84 |     await expect(page).toHaveURL(/.*login.*/);
  85 |     await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
  86 |   });
  87 | });
  88 |
```