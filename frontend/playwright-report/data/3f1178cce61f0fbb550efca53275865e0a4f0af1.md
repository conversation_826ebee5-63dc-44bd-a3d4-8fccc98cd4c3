# Test info

- Name: Dashboard Functionality >> should display dashboard with key metrics
- Location: /Users/<USER>/Desktop/EMS/frontend/e2e/dashboard.spec.ts:20:3

# Error details

```
Error: expect(locator).toHaveURL(expected)

Locator: locator(':root')
Expected pattern: /.*(?:dashboard|home|main).*/
Received string:  "http://localhost:5173/"
Call log:
  - expect.toHaveURL with timeout 5000ms
  - waiting for locator(':root')
    4 × locator resolved to <html lang="ar" dir="rtl" data-css-version="1752659618202">…</html>
      - unexpected value "http://localhost:5173/"

    at /Users/<USER>/Desktop/EMS/frontend/e2e/dashboard.spec.ts:16:26
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Dashboard Functionality', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Login before each test
   6 |     await page.goto('/');
   7 |     
   8 |     // Handle potential redirect to login
   9 |     const currentUrl = page.url();
   10 |     if (currentUrl.includes('login') || !currentUrl.includes('dashboard')) {
   11 |       await page.fill('input[type="text"], input[type="email"]', 'admin');
   12 |       await page.fill('input[type="password"]', 'admin123');
   13 |       await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
   14 |       
   15 |       // Wait for successful login
>  16 |       await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
      |                          ^ Error: expect(locator).toHaveURL(expected)
   17 |     }
   18 |   });
   19 |
   20 |   test('should display dashboard with key metrics', async ({ page }) => {
   21 |     // Check for dashboard elements
   22 |     await expect(page.locator('text=/dashboard|لوحة التحكم/i')).toBeVisible();
   23 |     
   24 |     // Look for common dashboard widgets/cards
   25 |     await expect(page.locator('[data-testid*="card"], .card, [class*="card"]')).toHaveCount({ min: 1 });
   26 |     
   27 |     // Check for navigation menu
   28 |     await expect(page.locator('nav, [role="navigation"], .navigation, .sidebar')).toBeVisible();
   29 |   });
   30 |
   31 |   test('should display employee statistics', async ({ page }) => {
   32 |     // Look for employee-related metrics
   33 |     await expect(page.locator('text=/employee|موظف|staff|عدد الموظفين/i')).toBeVisible();
   34 |     
   35 |     // Check for numerical data
   36 |     await expect(page.locator('text=/\\d+/')).toHaveCount({ min: 1 });
   37 |   });
   38 |
   39 |   test('should have working navigation menu', async ({ page }) => {
   40 |     // Test navigation to different sections
   41 |     const navigationItems = [
   42 |       { text: /employee|موظف/i, url: /employee/ },
   43 |       { text: /department|قسم/i, url: /department/ },
   44 |       { text: /report|تقرير/i, url: /report/ },
   45 |       { text: /setting|إعداد/i, url: /setting/ }
   46 |     ];
   47 |
   48 |     for (const item of navigationItems) {
   49 |       const navLink = page.locator(`a:has-text("${item.text.source}"), button:has-text("${item.text.source}")`).first();
   50 |       
   51 |       if (await navLink.isVisible()) {
   52 |         await navLink.click();
   53 |         await page.waitForTimeout(1000); // Wait for navigation
   54 |         
   55 |         // Check if URL changed appropriately
   56 |         const currentUrl = page.url();
   57 |         console.log(`Navigated to: ${currentUrl}`);
   58 |         
   59 |         // Go back to dashboard for next test
   60 |         await page.goto('/dashboard');
   61 |         await page.waitForTimeout(500);
   62 |       }
   63 |     }
   64 |   });
   65 |
   66 |   test('should display charts and visualizations', async ({ page }) => {
   67 |     // Look for chart containers
   68 |     const chartSelectors = [
   69 |       'canvas',
   70 |       '[data-testid*="chart"]',
   71 |       '.recharts-wrapper',
   72 |       '.chart-container',
   73 |       'svg'
   74 |     ];
   75 |
   76 |     let chartsFound = false;
   77 |     for (const selector of chartSelectors) {
   78 |       const charts = page.locator(selector);
   79 |       if (await charts.count() > 0) {
   80 |         chartsFound = true;
   81 |         break;
   82 |       }
   83 |     }
   84 |
   85 |     if (chartsFound) {
   86 |       console.log('Charts found on dashboard');
   87 |     } else {
   88 |       console.log('No charts found - this might be expected for a fresh installation');
   89 |     }
   90 |   });
   91 |
   92 |   test('should be responsive on mobile viewport', async ({ page }) => {
   93 |     // Set mobile viewport
   94 |     await page.setViewportSize({ width: 375, height: 667 });
   95 |     
   96 |     // Dashboard should still be accessible
   97 |     await expect(page.locator('text=/dashboard|لوحة التحكم/i')).toBeVisible();
   98 |     
   99 |     // Check for mobile navigation (hamburger menu, etc.)
  100 |     const mobileNavSelectors = [
  101 |       '[data-testid="mobile-menu"]',
  102 |       '.hamburger',
  103 |       'button[aria-label*="menu"]',
  104 |       'button:has-text("☰")'
  105 |     ];
  106 |
  107 |     let mobileNavFound = false;
  108 |     for (const selector of mobileNavSelectors) {
  109 |       if (await page.locator(selector).isVisible()) {
  110 |         mobileNavFound = true;
  111 |         break;
  112 |       }
  113 |     }
  114 |
  115 |     console.log(`Mobile navigation found: ${mobileNavFound}`);
  116 |   });
```