# Test info

- Name: Authentication Flow >> should display login page for unauthenticated users
- Location: /Users/<USER>/Desktop/EMS/frontend/e2e/auth.spec.ts:9:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

Locator: locator(':root')
Expected pattern: /.*login.*/
Received string:  "http://localhost:5173/"
Call log:
  - expect.toHaveURL with timeout 5000ms
  - waiting for locator(':root')
    9 × locator resolved to <html lang="ar" dir="rtl" data-css-version="1752659603899">…</html>
      - unexpected value "http://localhost:5173/"

    at /Users/<USER>/Desktop/EMS/frontend/e2e/auth.spec.ts:11:24
```

# Page snapshot

```yaml
- heading "مرحباً بك في نمو" [level=1]
- paragraph: نظام إدارة المؤسسات المتكامل
- text: تسجيل الدخول أدخل بياناتك للوصول إلى النظام اسم المستخدم
- textbox "اسم المستخدم"
- text: كلمة المرور
- textbox "كلمة المرور"
- button
- checkbox "تذكرني"
- text: تذكرني
- button "نسيت كلمة المرور؟"
- button "تسجيل الدخول" [disabled]
- heading "حسابات تجريبية" [level=3]
- text: "مدير النظام الرئيسي:"
- code: superadmin / superadmin123
- text: "Admin:"
- code: admin / admin123
- text: "مدير الموارد البشرية:"
- code: hrmanager / password123
- text: "مدير المالية:"
- code: financemanager / password123
- text: "موظف:"
- code: employee1 / password123
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Authentication Flow', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to the application
   6 |     await page.goto('/');
   7 |   });
   8 |
   9 |   test('should display login page for unauthenticated users', async ({ page }) => {
  10 |     // Should redirect to login or show login form
> 11 |     await expect(page).toHaveURL(/.*login.*/);
     |                        ^ Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)
  12 |     
  13 |     // Check for login form elements
  14 |     await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
  15 |     await expect(page.locator('input[type="password"]')).toBeVisible();
  16 |     await expect(page.locator('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")')).toBeVisible();
  17 |   });
  18 |
  19 |   test('should show validation errors for empty login form', async ({ page }) => {
  20 |     // Try to submit empty form
  21 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  22 |     
  23 |     // Should show validation errors
  24 |     await expect(page.locator('text=/required|مطلوب|field|حقل/i')).toBeVisible();
  25 |   });
  26 |
  27 |   test('should show error for invalid credentials', async ({ page }) => {
  28 |     // Fill in invalid credentials
  29 |     await page.fill('input[type="text"], input[type="email"]', '<EMAIL>');
  30 |     await page.fill('input[type="password"]', 'wrongpassword');
  31 |     
  32 |     // Submit form
  33 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  34 |     
  35 |     // Should show error message
  36 |     await expect(page.locator('text=/invalid|خطأ|error|غير صحيح/i')).toBeVisible();
  37 |   });
  38 |
  39 |   test('should successfully login with valid credentials', async ({ page }) => {
  40 |     // Fill in valid credentials (using the superuser we created)
  41 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  42 |     await page.fill('input[type="password"]', 'admin123');
  43 |     
  44 |     // Submit form
  45 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  46 |     
  47 |     // Should redirect to dashboard or main app
  48 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  49 |     
  50 |     // Should show authenticated user interface
  51 |     await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  52 |   });
  53 |
  54 |   test('should maintain session after page refresh', async ({ page }) => {
  55 |     // Login first
  56 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  57 |     await page.fill('input[type="password"]', 'admin123');
  58 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  59 |     
  60 |     // Wait for successful login
  61 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  62 |     
  63 |     // Refresh the page
  64 |     await page.reload();
  65 |     
  66 |     // Should still be authenticated
  67 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  68 |     await expect(page.locator('text=/dashboard|لوحة التحكم|welcome|مرحبا/i')).toBeVisible();
  69 |   });
  70 |
  71 |   test('should logout successfully', async ({ page }) => {
  72 |     // Login first
  73 |     await page.fill('input[type="text"], input[type="email"]', 'admin');
  74 |     await page.fill('input[type="password"]', 'admin123');
  75 |     await page.click('button[type="submit"], button:has-text("Login"), button:has-text("تسجيل الدخول")');
  76 |     
  77 |     // Wait for successful login
  78 |     await expect(page).toHaveURL(/.*(?:dashboard|home|main).*/);
  79 |     
  80 |     // Find and click logout button
  81 |     await page.click('button:has-text("Logout"), button:has-text("تسجيل الخروج"), [data-testid="logout-button"]');
  82 |     
  83 |     // Should redirect to login page
  84 |     await expect(page).toHaveURL(/.*login.*/);
  85 |     await expect(page.locator('input[type="text"], input[type="email"]')).toBeVisible();
  86 |   });
  87 | });
  88 |
```