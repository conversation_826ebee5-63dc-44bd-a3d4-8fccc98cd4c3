/**
 * Centralized Type Definitions for EMS Application
 * Comprehensive domain-specific interfaces and types
 */

// Re-export all domain types
export * from './employee'
export * from './department'
export * from './asset'
export * from './common'
export * from './api'
export * from './forms'
export * from './ui'

// Global utility types
export type ID = string | number
export type Timestamp = string // ISO 8601 format
export type Language = 'ar' | 'en'
export type Status = 'active' | 'inactive' | 'pending' | 'archived'

// Common entity base interface
export interface BaseEntity {
  id: ID
  created_at: Timestamp
  updated_at: Timestamp
  is_active: boolean
}

// Localized content interface
export interface LocalizedContent {
  name: string
  name_ar?: string
  description?: string
  description_ar?: string
}

// Audit trail interface
export interface AuditTrail {
  created_by: ID
  updated_by?: ID
  created_at: Timestamp
  updated_at: Timestamp
}

// Pagination interface
export interface PaginationMeta {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

// Sort configuration
export interface SortConfig {
  field: string
  direction: 'asc' | 'desc'
}

// Filter configuration
export interface FilterConfig {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn'
  value: unknown
}

// Search configuration
export interface SearchConfig {
  query: string
  fields: string[]
  fuzzy?: boolean
}

// Export configuration
export interface ExportConfig {
  format: 'csv' | 'excel' | 'pdf'
  fields?: string[]
  filters?: FilterConfig[]
  filename?: string
}

// Import result
export interface ImportResult {
  success: number
  errors: ImportError[]
  warnings: ImportWarning[]
}

export interface ImportError {
  row: number
  field: string
  message: string
  value: unknown
}

export interface ImportWarning {
  row: number
  field: string
  message: string
  value: unknown
}

// Permission types
export type Permission = 
  | 'read' 
  | 'write' 
  | 'delete' 
  | 'admin' 
  | 'super_admin'

export interface PermissionSet {
  [resource: string]: Permission[]
}

// Notification types
export type NotificationType = 
  | 'info' 
  | 'success' 
  | 'warning' 
  | 'error'

export interface Notification {
  id: ID
  type: NotificationType
  title: string
  message: string
  timestamp: Timestamp
  read: boolean
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  variant?: 'primary' | 'secondary' | 'destructive'
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

// Responsive breakpoints
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

// Loading states
export interface LoadingState {
  [key: string]: boolean
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: Record<string, unknown>
  timestamp: Timestamp
}

// Configuration types
export interface AppConfig {
  apiBaseUrl: string
  environment: 'development' | 'staging' | 'production'
  features: FeatureFlags
  theme: Theme
  language: Language
  timezone: string
}

export interface FeatureFlags {
  [feature: string]: boolean
}

// Analytics types
export interface AnalyticsEvent {
  name: string
  properties: Record<string, unknown>
  timestamp: Timestamp
  userId?: ID
  sessionId: string
}

// File types
export interface FileUpload {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

export interface FileInfo {
  id: ID
  name: string
  size: number
  type: string
  url: string
  uploadedAt: Timestamp
  uploadedBy: ID
}

// Chart/Dashboard types
export interface ChartDataPoint {
  label: string
  value: number
  color?: string
  metadata?: Record<string, unknown>
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area'
  data: ChartDataPoint[]
  options?: Record<string, unknown>
}

// KPI types
export interface KPI {
  id: ID
  name: string
  value: number
  target?: number
  unit?: string
  trend: 'up' | 'down' | 'stable'
  change: number
  changePercent: number
  period: string
}

// Dashboard widget types
export interface DashboardWidget {
  id: ID
  type: 'chart' | 'kpi' | 'table' | 'text' | 'custom'
  title: string
  config: Record<string, unknown>
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  permissions: Permission[]
}

// Report types
export interface Report {
  id: ID
  name: string
  description?: string
  type: 'scheduled' | 'on-demand'
  format: 'pdf' | 'excel' | 'csv'
  schedule?: ReportSchedule
  parameters: Record<string, unknown>
  lastRun?: Timestamp
  nextRun?: Timestamp
  status: 'active' | 'inactive' | 'error'
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  time: string // HH:mm format
  dayOfWeek?: number // 0-6, Sunday = 0
  dayOfMonth?: number // 1-31
  timezone: string
}

// Workflow types
export interface WorkflowStep {
  id: ID
  name: string
  type: 'approval' | 'notification' | 'action' | 'condition'
  config: Record<string, unknown>
  nextSteps: ID[]
  permissions: Permission[]
}

export interface Workflow {
  id: ID
  name: string
  description?: string
  steps: WorkflowStep[]
  triggers: WorkflowTrigger[]
  status: Status
}

export interface WorkflowTrigger {
  event: string
  conditions: FilterConfig[]
  enabled: boolean
}

// Integration types
export interface Integration {
  id: ID
  name: string
  type: 'api' | 'webhook' | 'database' | 'file'
  config: Record<string, unknown>
  status: 'connected' | 'disconnected' | 'error'
  lastSync?: Timestamp
}

// Backup types
export interface BackupInfo {
  id: ID
  name: string
  size: number
  createdAt: Timestamp
  type: 'full' | 'incremental'
  status: 'completed' | 'in-progress' | 'failed'
}
