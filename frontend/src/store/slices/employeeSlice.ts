import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { Employee, CreateEmployeeData, UpdateEmployeeData, EmployeeFilters } from '@/types/employee'
import { employeeAPI } from '@/services/employeeAPI'
import { EmployeeFormData } from '@/types/forms'

interface EmployeeState {
  employees: Employee[]
  currentEmployee: Employee | null
  loading: boolean
  error: string | null
  filters: EmployeeFilters
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

const initialState: EmployeeState = {
  employees: [],
  currentEmployee: null,
  loading: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  }
}

// Async thunks
export const fetchEmployees = createAsyncThunk(
  'employees/fetchEmployees',
  async (filters?: EmployeeFilters) => {
    const response = await employeeAPI.getAll(filters)
    return response
  }
)

export const fetchEmployeeById = createAsyncThunk(
  'employees/fetchEmployeeById',
  async (id: number) => {
    const response = await employeeAPI.getById(id)
    return response
  }
)

export const createEmployee = createAsyncThunk(
  'employees/createEmployee',
  async (employeeData: EmployeeFormData) => {
    const response = await employeeAPI.create(employeeData)
    return response
  }
)

export const updateEmployee = createAsyncThunk(
  'employees/updateEmployee',
  async ({ id, data }: { id: number; data: UpdateEmployeeData }) => {
    const response = await employeeAPI.update(id, data)
    return response
  }
)

export const deleteEmployee = createAsyncThunk(
  'employees/deleteEmployee',
  async (id: number) => {
    await employeeAPI.delete(id)
    return id
  }
)

const employeeSlice = createSlice({
  name: 'employees',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<EmployeeFilters>) => {
      state.filters = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    setCurrentEmployee: (state, action: PayloadAction<Employee | null>) => {
      state.currentEmployee = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch employees
      .addCase(fetchEmployees.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.loading = false
        state.employees = action.payload.data
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: Math.ceil(action.payload.total / action.payload.pageSize)
        }
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch employees'
      })
      
      // Fetch employee by ID
      .addCase(fetchEmployeeById.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchEmployeeById.fulfilled, (state, action) => {
        state.loading = false
        state.currentEmployee = action.payload
      })
      .addCase(fetchEmployeeById.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch employee'
      })
      
      // Create employee
      .addCase(createEmployee.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(createEmployee.fulfilled, (state, action) => {
        state.loading = false
        state.employees.push(action.payload)
      })
      .addCase(createEmployee.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to create employee'
      })
      
      // Update employee
      .addCase(updateEmployee.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(updateEmployee.fulfilled, (state, action) => {
        state.loading = false
        const index = state.employees.findIndex(emp => emp.id === action.payload.id)
        if (index !== -1) {
          state.employees[index] = action.payload
        }
        if (state.currentEmployee?.id === action.payload.id) {
          state.currentEmployee = action.payload
        }
      })
      .addCase(updateEmployee.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to update employee'
      })
      
      // Delete employee
      .addCase(deleteEmployee.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteEmployee.fulfilled, (state, action) => {
        state.loading = false
        state.employees = state.employees.filter(emp => emp.id !== action.payload)
        if (state.currentEmployee?.id === action.payload) {
          state.currentEmployee = null
        }
      })
      .addCase(deleteEmployee.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to delete employee'
      })
  }
})

export const { setFilters, clearError, setCurrentEmployee } = employeeSlice.actions
export default employeeSlice.reducer
