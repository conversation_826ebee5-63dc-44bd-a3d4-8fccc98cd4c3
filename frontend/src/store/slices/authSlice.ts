import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { authService, User, LoginCredentials, handleApiError } from '../../services'

// Re-export User type from services for backward compatibility
export type { User } from '../../services'

export interface UserPreferences {
  language: 'ar' | 'en'
  theme: 'light' | 'dark'
  dashboardLayout: string[]
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  loginAttempts: number
  lastLoginAttempt: number | null
}

const initialState: AuthState = {
  user: null,
  token: authService.getAccessToken(),
  isAuthenticated: !!authService.getAccessToken(), // Set true if token exists
  isLoading: !!authService.getAccessToken(), // Start loading if we have a token to verify
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null,
}

// Async thunks using real API service
export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: LoginCredentials, { rejectWithValue }) => {
    try {
      console.log('🔄 AuthSlice: Starting login process...')
      const authResponse = await authService.login(credentials)
      console.log('🔄 AuthSlice: Login response received:', {
        hasUser: !!authResponse.user,
        hasToken: !!authResponse.access,
        username: authResponse.user?.username
      })

      const result = {
        user: authResponse.user,
        token: authResponse.access
      }

      console.log('🔄 AuthSlice: Returning login result to reducer')
      return result
    } catch (error) {
      console.error('🔄 AuthSlice: Login failed:', error)
      return rejectWithValue(handleApiError(error))
    }
  }
)

export const logoutUser = createAsyncThunk(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout()
      return null
    } catch (error) {
      // Even if logout API fails, we should clear local state
      console.warn('Logout API failed:', error)
      return null
    }
  }
)

export const verifyToken = createAsyncThunk(
  'auth/verifyToken',
  async (_, { rejectWithValue }) => {
    try {
      const user = await authService.verifyToken()
      return user
    } catch (error) {
      return rejectWithValue(handleApiError(error))
    }
  }
)

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const newToken = await authService.refreshToken()
      return newToken
    } catch (error) {
      return rejectWithValue(handleApiError(error))
    }
  }
)

export const updateProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData: Partial<User>, { rejectWithValue }) => {
    try {
      const updatedUser = await authService.updateProfile(profileData)
      return updatedUser
    } catch (error) {
      return rejectWithValue(handleApiError(error))
    }
  }
)

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      if (state.user && state.user.profile) {
        // Update the user profile with preference-like data
        state.user.profile = {
          ...state.user.profile,
          preferred_language: action.payload.language || state.user.profile.preferred_language
        }
      }
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0
      state.lastLoginAttempt = null
    },
    setAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload
    },
    clearInconsistentState: (state) => {
      // Clear state when authentication is inconsistent (has token but no user)
      state.user = null
      state.token = null
      state.isAuthenticated = false
      state.isLoading = false
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        console.log('🔄 AuthSlice: Login fulfilled, updating state...')

        state.isLoading = false
        state.user = action.payload.user
        state.token = action.payload.token
        state.isAuthenticated = true
        state.loginAttempts = 0
        state.lastLoginAttempt = null

        // Ensure token is stored in localStorage
        if (action.payload.token) {
          localStorage.setItem('access_token', action.payload.token)
          localStorage.setItem('token', action.payload.token) // Backup storage
          console.log('🔄 AuthSlice: Tokens stored in localStorage from reducer')
        }

        console.log('🔄 AuthSlice: Auth state updated after login:', {
          hasUser: !!state.user,
          hasToken: !!state.token,
          isAuthenticated: state.isAuthenticated,
          username: state.user?.username
        })

        // Verify localStorage immediately
        setTimeout(() => {
          const storedToken = localStorage.getItem('access_token')
          console.log('🔄 AuthSlice: Post-login token verification:', {
            tokenInStorage: !!storedToken,
            tokenLength: storedToken?.length || 0
          })
        }, 100)
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
        state.loginAttempts += 1
        state.lastLoginAttempt = Date.now()
      })
      // Logout
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.error = null
        state.isLoading = false
      })
      .addCase(logoutUser.rejected, (state) => {
        // Even if logout fails, clear the state
        state.user = null
        state.token = null
        state.isAuthenticated = false
        state.isLoading = false
      })
      // Verify token
      .addCase(verifyToken.pending, (state) => {
        state.isLoading = true
      })
      .addCase(verifyToken.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
        state.isAuthenticated = true
      })
      .addCase(verifyToken.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
      })
      // Refresh token
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false
        state.token = action.payload
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isLoading = false
        state.user = null
        state.token = null
        state.isAuthenticated = false
      })
      // Update profile
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false
        state.user = action.payload
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload as string
      })
  },
})

export const { clearError, updateUserPreferences, resetLoginAttempts, setAuthenticated, clearInconsistentState } = authSlice.actions

// Export aliases for backward compatibility with tests
export const loginStart = loginUser.pending
export const loginSuccess = loginUser.fulfilled
export const loginFailure = loginUser.rejected
export const logout = logoutUser

export default authSlice.reducer
