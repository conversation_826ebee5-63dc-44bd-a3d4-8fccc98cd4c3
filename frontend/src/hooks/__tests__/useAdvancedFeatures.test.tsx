/**
 * useAdvancedFeatures Hook Tests
 * Comprehensive tests for the advanced features hook
 */

import React from 'react'
import { renderHook, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import useAdvancedFeatures from '../useAdvancedFeatures'
import { searchService } from '../../services/search'
import { exportService } from '../../services/export'
import webSocketService from '../../services/websocket'
import authSlice from '../../store/slices/authSlice'
import notificationSlice from '../../store/slices/notificationSlice'
import { createMockSearchResult, createMockNotification } from '../../test-utils/test-utils'

// Mock services
jest.mock('../../services/search')
jest.mock('../../services/export')
jest.mock('../../services/websocket')

const mockedSearchService = searchService as jest.Mocked<typeof searchService>
const mockedExportService = exportService as jest.Mocked<typeof exportService>
const mockedWebSocketService = webSocketService as jest.Mocked<typeof webSocketService>

// Mock keyboard event listeners
const mockAddEventListener = jest.fn()
const mockRemoveEventListener = jest.fn()

Object.defineProperty(document, 'addEventListener', {
  value: mockAddEventListener,
})

Object.defineProperty(document, 'removeEventListener', {
  value: mockRemoveEventListener,
})

describe('useAdvancedFeatures Hook', () => {
  const mockStore = configureStore({
    reducer: {
      auth: authSlice,
      notifications: notificationSlice,
    },
    preloadedState: {
      auth: {
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: { id: 'employee', name: 'Employee', nameAr: 'موظف' },
        },
        token: 'mock-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        loginAttempts: 0,
        lastLoginAttempt: null,
      },
      notifications: {
        notifications: [
          createMockNotification({ id: '1', read: false }),
          createMockNotification({ id: '2', read: true }),
        ],
        unreadCount: 1,
        filter: 'all' as const,
        category: 'all' as const,
        isLoading: false,
        error: null,
        lastFetch: null,
      },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <Provider store={mockStore}>{children}</Provider>
  )

  beforeEach(() => {
    jest.clearAllMocks()
    mockedWebSocketService.isWebSocketConnected.mockReturnValue(true)
    mockedWebSocketService.on.mockImplementation(() => {})
    mockedWebSocketService.off.mockImplementation(() => {})
  })

  describe('Initialization', () => {
    it('should initialize with default options', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      expect(result.current.search.isOpen).toBe(false)
      expect(result.current.export.isOpen).toBe(false)
      expect(result.current.notifications.isOpen).toBe(false)
      expect(result.current.notifications.unreadCount).toBe(1)
    })

    it('should initialize with custom options', () => {
      const { result } = renderHook(
        () =>
          useAdvancedFeatures({
            module: 'employees',
            enableSearch: false,
            enableExport: true,
            enableNotifications: false,
          }),
        { wrapper }
      )

      expect(result.current.utils.isFeatureEnabled('search')).toBe(false)
      expect(result.current.utils.isFeatureEnabled('export')).toBe(true)
      expect(result.current.utils.isFeatureEnabled('notifications')).toBe(false)
    })

    it('should set up keyboard event listeners', () => {
      renderHook(() => useAdvancedFeatures(), { wrapper })

      expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })

    it('should clean up event listeners on unmount', () => {
      const { unmount } = renderHook(() => useAdvancedFeatures(), { wrapper })

      unmount()

      expect(mockRemoveEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
    })
  })

  describe('Search Functionality', () => {
    beforeEach(() => {
      mockedSearchService.globalSearch.mockResolvedValue({
        results: [createMockSearchResult()],
        total: 1,
        page: 1,
        limit: 10,
        executionTime: 45,
      })
      mockedSearchService.moduleSearch.mockResolvedValue({
        results: [createMockSearchResult()],
        total: 1,
        page: 1,
        limit: 10,
        executionTime: 30,
      })
      mockedSearchService.getSuggestions.mockResolvedValue(['suggestion 1', 'suggestion 2'])
    })

    it('should open and close search', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      act(() => {
        result.current.search.open()
      })

      expect(result.current.search.isOpen).toBe(true)

      act(() => {
        result.current.search.close()
      })

      expect(result.current.search.isOpen).toBe(false)
      expect(result.current.search.query).toBe('')
      expect(result.current.search.results).toEqual([])
    })

    it('should perform global search', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.search.perform('test query')
      })

      expect(mockedSearchService.globalSearch).toHaveBeenCalledWith({
        query: 'test query',
        pagination: { page: 1, limit: 10 },
      })
      expect(result.current.search.results).toHaveLength(1)
      expect(result.current.search.isLoading).toBe(false)
    })

    it('should perform module search', async () => {
      const { result } = renderHook(
        () => useAdvancedFeatures({ module: 'employees' }),
        { wrapper }
      )

      await act(async () => {
        await result.current.search.perform('test query')
      })

      expect(mockedSearchService.moduleSearch).toHaveBeenCalledWith('employees', {
        query: 'test query',
        pagination: { page: 1, limit: 10 },
      })
    })

    it('should get search suggestions', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.search.getSuggestions('test')
      })

      expect(mockedSearchService.getSuggestions).toHaveBeenCalledWith('test', undefined)
      expect(result.current.search.suggestions).toEqual(['suggestion 1', 'suggestion 2'])
    })

    it('should handle search errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      mockedSearchService.globalSearch.mockRejectedValue(new Error('Search failed'))

      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.search.perform('test query')
      })

      expect(result.current.search.results).toEqual([])
      expect(result.current.search.isLoading).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('Search error:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should not search when feature is disabled', async () => {
      const { result } = renderHook(
        () => useAdvancedFeatures({ enableSearch: false }),
        { wrapper }
      )

      await act(async () => {
        await result.current.search.perform('test query')
      })

      expect(mockedSearchService.globalSearch).not.toHaveBeenCalled()
    })
  })

  describe('Export Functionality', () => {
    const sampleData = [{ id: 1, name: 'Test' }]
    const sampleColumns = [{ key: 'id', label: 'ID', type: 'number' as const }]

    beforeEach(() => {
      mockedExportService.exportToExcel.mockResolvedValue()
      mockedExportService.exportToPDF.mockResolvedValue()
      mockedExportService.exportToCSV.mockResolvedValue()
      mockedExportService.exportToJSON.mockResolvedValue()
      mockedExportService.exportLargeDataset.mockResolvedValue('download-url')
    })

    it('should open and close export', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      act(() => {
        result.current.export.open()
      })

      expect(result.current.export.isOpen).toBe(true)

      act(() => {
        result.current.export.close()
      })

      expect(result.current.export.isOpen).toBe(false)
      expect(result.current.export.isExporting).toBe(false)
      expect(result.current.export.progress).toBe(0)
    })

    it('should export data to Excel', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns, { format: 'excel' })
      })

      expect(mockedExportService.exportToExcel).toHaveBeenCalledWith(sampleData, {
        format: 'excel',
        columns: sampleColumns,
      })
      expect(result.current.export.progress).toBe(100)
    })

    it('should export data to PDF', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns, { format: 'pdf' })
      })

      expect(mockedExportService.exportToPDF).toHaveBeenCalledWith(sampleData, {
        format: 'pdf',
        columns: sampleColumns,
      })
    })

    it('should export data to CSV', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns, { format: 'csv' })
      })

      expect(mockedExportService.exportToCSV).toHaveBeenCalledWith(sampleData, {
        format: 'csv',
        columns: sampleColumns,
      })
    })

    it('should export data to JSON', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns, { format: 'json' })
      })

      expect(mockedExportService.exportToJSON).toHaveBeenCalledWith(sampleData, {
        format: 'json',
        columns: sampleColumns,
      })
    })

    it('should handle large dataset export', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportLargeDataset('employees', {
          format: 'excel',
          columns: sampleColumns,
        })
      })

      expect(mockedExportService.exportLargeDataset).toHaveBeenCalledWith('employees', {
        format: 'excel',
        columns: sampleColumns,
      })
    })

    it('should handle export errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      mockedExportService.exportToExcel.mockRejectedValue(new Error('Export failed'))

      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns)
      })

      expect(result.current.export.error).toBe('Export failed')
      expect(result.current.export.isExporting).toBe(false)
      expect(consoleSpy).toHaveBeenCalledWith('Export error:', expect.any(Error))

      consoleSpy.mockRestore()
    })

    it('should not export when feature is disabled', async () => {
      const { result } = renderHook(
        () => useAdvancedFeatures({ enableExport: false }),
        { wrapper }
      )

      await act(async () => {
        await result.current.export.exportData(sampleData, sampleColumns)
      })

      expect(mockedExportService.exportToExcel).not.toHaveBeenCalled()
    })
  })

  describe('Notification Functionality', () => {
    beforeEach(() => {
      ;(mockedWebSocketService.constructor as any).requestNotificationPermission = jest
        .fn()
        .mockResolvedValue('granted')
    })

    it('should open and close notifications', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      act(() => {
        result.current.notifications.open()
      })

      expect(result.current.notifications.isOpen).toBe(true)

      act(() => {
        result.current.notifications.close()
      })

      expect(result.current.notifications.isOpen).toBe(false)
    })

    it('should request notification permission', async () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      let permission
      await act(async () => {
        permission = await result.current.notifications.requestPermission()
      })

      expect(permission).toBe('granted')
    })

    it('should track connection status', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      expect(result.current.notifications.connectionStatus).toBe('connected')
    })

    it('should update unread count from Redux', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      expect(result.current.notifications.unreadCount).toBe(1)
    })

    it('should not open notifications when feature is disabled', () => {
      const { result } = renderHook(
        () => useAdvancedFeatures({ enableNotifications: false }),
        { wrapper }
      )

      act(() => {
        result.current.notifications.open()
      })

      expect(result.current.notifications.isOpen).toBe(false)
    })
  })

  describe('Keyboard Shortcuts', () => {
    it('should handle Ctrl+K for search', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      // Simulate Ctrl+K keydown event
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'k',
        ctrlKey: true,
      })

      act(() => {
        document.dispatchEvent(keydownEvent)
      })

      expect(result.current.search.isOpen).toBe(true)
    })

    it('should handle Ctrl+E for export', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      // Simulate Ctrl+E keydown event
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'e',
        ctrlKey: true,
      })

      act(() => {
        document.dispatchEvent(keydownEvent)
      })

      expect(result.current.export.isOpen).toBe(true)
    })

    it('should handle Ctrl+N for notifications', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      // Simulate Ctrl+N keydown event
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'n',
        ctrlKey: true,
      })

      act(() => {
        document.dispatchEvent(keydownEvent)
      })

      expect(result.current.notifications.isOpen).toBe(true)
    })

    it('should handle Escape to close dialogs', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      // Open all dialogs
      act(() => {
        result.current.search.open()
        result.current.export.open()
        result.current.notifications.open()
      })

      // Simulate Escape keydown event
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
      })

      act(() => {
        document.dispatchEvent(keydownEvent)
      })

      expect(result.current.search.isOpen).toBe(false)
      expect(result.current.export.isOpen).toBe(false)
      expect(result.current.notifications.isOpen).toBe(false)
    })

    it('should handle Cmd+K on Mac', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      // Simulate Cmd+K keydown event
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'k',
        metaKey: true,
      })

      act(() => {
        document.dispatchEvent(keydownEvent)
      })

      expect(result.current.search.isOpen).toBe(true)
    })
  })

  describe('Utility Functions', () => {
    it('should check if features are enabled', () => {
      const { result } = renderHook(
        () =>
          useAdvancedFeatures({
            enableSearch: true,
            enableExport: false,
            enableNotifications: true,
          }),
        { wrapper }
      )

      expect(result.current.utils.isFeatureEnabled('search')).toBe(true)
      expect(result.current.utils.isFeatureEnabled('export')).toBe(false)
      expect(result.current.utils.isFeatureEnabled('notifications')).toBe(true)
    })

    it('should return keyboard shortcuts', () => {
      const { result } = renderHook(() => useAdvancedFeatures(), { wrapper })

      const shortcuts = result.current.utils.getKeyboardShortcuts()

      expect(shortcuts).toEqual({
        search: 'Ctrl/Cmd + K',
        export: 'Ctrl/Cmd + E',
        notifications: 'Ctrl/Cmd + N',
        close: 'Escape',
      })
    })
  })

  describe('WebSocket Integration', () => {
    it('should set up WebSocket event listeners', () => {
      renderHook(() => useAdvancedFeatures(), { wrapper })

      expect(mockedWebSocketService.on).toHaveBeenCalledWith('connected', expect.any(Function))
      expect(mockedWebSocketService.on).toHaveBeenCalledWith('disconnected', expect.any(Function))
      expect(mockedWebSocketService.on).toHaveBeenCalledWith('notification', expect.any(Function))
    })

    it('should clean up WebSocket listeners on unmount', () => {
      const { unmount } = renderHook(() => useAdvancedFeatures(), { wrapper })

      unmount()

      expect(mockedWebSocketService.off).toHaveBeenCalledWith('connected', expect.any(Function))
      expect(mockedWebSocketService.off).toHaveBeenCalledWith('disconnected', expect.any(Function))
      expect(mockedWebSocketService.off).toHaveBeenCalledWith('notification', expect.any(Function))
    })
  })

  describe('Error Handling', () => {
    it('should handle missing user gracefully', () => {
      const storeWithoutUser = configureStore({
        reducer: {
          auth: authSlice,
          notifications: notificationSlice,
        },
        preloadedState: {
          auth: {
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
            loginAttempts: 0,
            lastLoginAttempt: null,
          },
          notifications: {
            notifications: [],
            unreadCount: 0,
            filter: 'all' as const,
            category: 'all' as const,
            isLoading: false,
            error: null,
            lastFetch: null,
          },
        },
      })

      const wrapperWithoutUser = ({ children }: { children: React.ReactNode }) => (
        <Provider store={storeWithoutUser}>{children}</Provider>
      )

      const { result } = renderHook(() => useAdvancedFeatures(), {
        wrapper: wrapperWithoutUser,
      })

      expect(result.current.notifications.unreadCount).toBe(0)
    })
  })
})
