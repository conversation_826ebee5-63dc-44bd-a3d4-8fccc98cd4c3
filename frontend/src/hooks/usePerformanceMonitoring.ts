import { useState, useEffect, useCallback, useRef } from 'react'

interface PerformanceMetrics {
  // Page Performance
  loadTime: number
  renderTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number

  // Network Performance
  networkSpeed: 'slow' | 'medium' | 'fast'
  connectionType: string
  isOnline: boolean

  // Memory Usage
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }

  // User Interaction
  interactionCount: number
  averageResponseTime: number
  errorCount: number

  // Device Information
  deviceType: 'mobile' | 'tablet' | 'desktop'
  screenSize: { width: number; height: number }
  pixelRatio: number
  touchSupport: boolean
}

interface PerformanceAlert {
  type: 'warning' | 'error'
  message: string
  metric: string
  value: number
  threshold: number
  timestamp: Date
}

export default function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    cumulativeLayoutShift: 0,
    firstInputDelay: 0,
    networkSpeed: 'medium',
    connectionType: 'unknown',
    isOnline: navigator.onLine,
    memoryUsage: { used: 0, total: 0, percentage: 0 },
    interactionCount: 0,
    averageResponseTime: 0,
    errorCount: 0,
    deviceType: 'desktop',
    screenSize: { width: window.innerWidth, height: window.innerHeight },
    pixelRatio: window.devicePixelRatio || 1,
    touchSupport: 'ontouchstart' in window
  })

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [isMonitoring, setIsMonitoring] = useState(false)
  const interactionTimes = useRef<number[]>([])
  const errorCount = useRef(0)
  const startTime = useRef(Date.now())

  // Performance thresholds
  const thresholds = {
    loadTime: 3000, // 3 seconds
    renderTime: 100, // 100ms
    firstContentfulPaint: 1800, // 1.8 seconds
    largestContentfulPaint: 2500, // 2.5 seconds
    cumulativeLayoutShift: 0.1,
    firstInputDelay: 100, // 100ms
    memoryUsage: 80, // 80%
    averageResponseTime: 200 // 200ms
  }

  // Detect device type
  const detectDeviceType = useCallback((): 'mobile' | 'tablet' | 'desktop' => {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }, [])

  // Detect network speed
  const detectNetworkSpeed = useCallback((): 'slow' | 'medium' | 'fast' => {
    const connection = (navigator as Navigator & {
      connection?: { effectiveType?: string; downlink?: number }
      mozConnection?: { effectiveType?: string; downlink?: number }
      webkitConnection?: { effectiveType?: string; downlink?: number }
    }).connection || (navigator as Navigator & { mozConnection?: { effectiveType?: string; downlink?: number } }).mozConnection || (navigator as Navigator & { webkitConnection?: { effectiveType?: string; downlink?: number } }).webkitConnection
    
    if (!connection) return 'medium'
    
    const { effectiveType, downlink } = connection
    
    if (effectiveType === 'slow-2g' || effectiveType === '2g' || (downlink && downlink < 1)) {
      return 'slow'
    } else if (effectiveType === '3g' || (downlink && downlink < 5)) {
      return 'medium'
    } else {
      return 'fast'
    }
  }, [])

  // Get memory usage
  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as Performance & {
        memory?: {
          usedJSHeapSize: number
          totalJSHeapSize: number
          jsHeapSizeLimit: number
        }
      }).memory
      const used = memory?.usedJSHeapSize || 0
      const total = memory?.totalJSHeapSize || 0
      const percentage = (used / total) * 100
      
      return { used, total, percentage }
    }
    return { used: 0, total: 0, percentage: 0 }
  }, [])

  // Measure Web Vitals
  const measureWebVitals = useCallback(() => {
    // First Contentful Paint
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    const fcp = fcpEntry ? fcpEntry.startTime : 0

    // Largest Contentful Paint
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      if (lastEntry) {
        setMetrics(prev => ({
          ...prev,
          largestContentfulPaint: lastEntry.startTime
        }))
      }
    })
    
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      // LCP not supported
    }

    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      for (const entry of list.getEntries()) {
        if (!(entry as PerformanceEntry & { hadRecentInput?: boolean }).hadRecentInput) {
          clsValue += (entry as PerformanceEntry & { value: number }).value
        }
      }
      setMetrics(prev => ({
        ...prev,
        cumulativeLayoutShift: clsValue
      }))
    })

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (e) {
      // CLS not supported
    }

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        setMetrics(prev => ({
          ...prev,
          firstInputDelay: (entry as PerformanceEntry & { processingStart: number }).processingStart - entry.startTime
        }))
      }
    })

    try {
      fidObserver.observe({ entryTypes: ['first-input'] })
    } catch (e) {
      // FID not supported
    }

    return {
      firstContentfulPaint: fcp,
      cleanup: () => {
        observer.disconnect()
        clsObserver.disconnect()
        fidObserver.disconnect()
      }
    }
  }, [])

  // Track user interactions
  const trackInteraction = useCallback((startTime: number) => {
    const endTime = Date.now()
    const responseTime = endTime - startTime
    
    interactionTimes.current.push(responseTime)
    
    // Keep only last 100 interactions
    if (interactionTimes.current.length > 100) {
      interactionTimes.current = interactionTimes.current.slice(-100)
    }
    
    const averageResponseTime = interactionTimes.current.reduce((a, b) => a + b, 0) / interactionTimes.current.length
    
    setMetrics(prev => ({
      ...prev,
      interactionCount: prev.interactionCount + 1,
      averageResponseTime
    }))

    // Check for performance alerts
    if (responseTime > thresholds.averageResponseTime) {
      addAlert('warning', 'Slow interaction detected', 'averageResponseTime', responseTime, thresholds.averageResponseTime)
    }
  }, [])

  // Add performance alert
  const addAlert = useCallback((type: 'warning' | 'error', message: string, metric: string, value: number, threshold: number) => {
    const alert: PerformanceAlert = {
      type,
      message,
      metric,
      value,
      threshold,
      timestamp: new Date()
    }
    
    setAlerts(prev => [alert, ...prev.slice(0, 9)]) // Keep only last 10 alerts
  }, [])

  // Monitor performance continuously
  useEffect(() => {
    if (!isMonitoring) return

    const interval = setInterval(() => {
      const memoryUsage = getMemoryUsage()
      const networkSpeed = detectNetworkSpeed()
      const deviceType = detectDeviceType()
      
      setMetrics(prev => ({
        ...prev,
        memoryUsage,
        networkSpeed,
        deviceType,
        isOnline: navigator.onLine,
        screenSize: { width: window.innerWidth, height: window.innerHeight },
        errorCount: errorCount.current
      }))

      // Check for alerts
      if (memoryUsage.percentage > thresholds.memoryUsage) {
        addAlert('warning', 'High memory usage detected', 'memoryUsage', memoryUsage.percentage, thresholds.memoryUsage)
      }
    }, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [isMonitoring, getMemoryUsage, detectNetworkSpeed, detectDeviceType, addAlert])

  // Initialize performance monitoring
  useEffect(() => {
    const { firstContentfulPaint, cleanup } = measureWebVitals()
    
    // Measure initial load time
    const loadTime = Date.now() - startTime.current
    
    setMetrics(prev => ({
      ...prev,
      loadTime,
      firstContentfulPaint,
      deviceType: detectDeviceType(),
      networkSpeed: detectNetworkSpeed(),
      memoryUsage: getMemoryUsage()
    }))

    // Track errors
    const errorHandler = (event: ErrorEvent) => {
      errorCount.current++
      addAlert('error', `JavaScript error: ${event.message}`, 'errorCount', errorCount.current, 0)
    }

    const unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
      errorCount.current++
      addAlert('error', `Unhandled promise rejection: ${event.reason}`, 'errorCount', errorCount.current, 0)
    }

    window.addEventListener('error', errorHandler)
    window.addEventListener('unhandledrejection', unhandledRejectionHandler)

    // Track network status
    const onlineHandler = () => setMetrics(prev => ({ ...prev, isOnline: true }))
    const offlineHandler = () => setMetrics(prev => ({ ...prev, isOnline: false }))
    
    window.addEventListener('online', onlineHandler)
    window.addEventListener('offline', offlineHandler)

    // Track window resize
    const resizeHandler = () => {
      setMetrics(prev => ({
        ...prev,
        screenSize: { width: window.innerWidth, height: window.innerHeight },
        deviceType: detectDeviceType()
      }))
    }
    
    window.addEventListener('resize', resizeHandler)

    return () => {
      cleanup()
      window.removeEventListener('error', errorHandler)
      window.removeEventListener('unhandledrejection', unhandledRejectionHandler)
      window.removeEventListener('online', onlineHandler)
      window.removeEventListener('offline', offlineHandler)
      window.removeEventListener('resize', resizeHandler)
    }
  }, [measureWebVitals, detectDeviceType, detectNetworkSpeed, getMemoryUsage, addAlert])

  // Start/stop monitoring
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true)
  }, [])

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false)
  }, [])

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setAlerts([])
  }, [])

  // Get performance score (0-100)
  const getPerformanceScore = useCallback(() => {
    let score = 100
    
    // Deduct points for poor metrics
    if (metrics.loadTime > thresholds.loadTime) score -= 20
    if (metrics.firstContentfulPaint > thresholds.firstContentfulPaint) score -= 15
    if (metrics.largestContentfulPaint > thresholds.largestContentfulPaint) score -= 15
    if (metrics.cumulativeLayoutShift > thresholds.cumulativeLayoutShift) score -= 10
    if (metrics.firstInputDelay > thresholds.firstInputDelay) score -= 10
    if (metrics.memoryUsage.percentage > thresholds.memoryUsage) score -= 15
    if (metrics.averageResponseTime > thresholds.averageResponseTime) score -= 10
    if (metrics.errorCount > 0) score -= metrics.errorCount * 5
    if (metrics.networkSpeed === 'slow') score -= 5

    return Math.max(0, score)
  }, [metrics])

  return {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    trackInteraction,
    clearAlerts,
    getPerformanceScore,
    thresholds
  }
}
