/**
 * KPI Filters Hook
 * Custom hook for managing KPI filtering and date range selection
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { KPI, KPICategory } from '@/services/kpiService'
import kpiService from '@/services/kpiService'
import { KPIFilterValue } from '@/components/kpi/KPIFilterPanel'
import { DateRange } from '@/components/common/DateRangePicker'

export interface UseKPIFiltersOptions {
  initialFilters?: KPIFilterValue
  autoApply?: boolean
  debounceMs?: number
}

export interface UseKPIFiltersReturn {
  // Filter state
  filters: KPIFilterValue
  setFilters: (filters: KPIFilterValue) => void
  updateFilter: <K extends keyof KPIFilterValue>(key: K, value: KPIFilterValue[K]) => void
  resetFilters: () => void
  
  // Filtered data
  filteredKPIs: KPI[]
  loading: boolean
  error: string | null
  
  // Filter actions
  applyFilters: () => void
  clearFilter: (key: keyof KPIFilterValue) => void
  
  // Date range helpers
  setDateRange: (range: DateRange) => void
  clearDateRange: () => void
  
  // Search helpers
  setSearchQuery: (query: string) => void
  clearSearch: () => void
  
  // Category helpers
  toggleCategory: (categoryId: string) => void
  selectAllCategories: (categories: KPICategory[]) => void
  clearCategories: () => void
  
  // Filter statistics
  activeFilterCount: number
  hasActiveFilters: boolean
  
  // Data refresh
  refresh: () => Promise<void>
}

export function useKPIFilters(options: UseKPIFiltersOptions = {}): UseKPIFiltersReturn {
  const {
    initialFilters = {},
    autoApply = true,
    debounceMs = 300
  } = options

  const [filters, setFilters] = useState<KPIFilterValue>(initialFilters)
  const [allKPIs, setAllKPIs] = useState<KPI[]>([])
  const [filteredKPIs, setFilteredKPIs] = useState<KPI[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null)

  // Load KPIs from API
  const loadKPIs = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const kpis = await kpiService.getKPIs()
      setAllKPIs(kpis)
    } catch (err: unknown) {
      console.error('Error loading KPIs:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load KPIs'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  // Initial load
  useEffect(() => {
    loadKPIs()
  }, [loadKPIs])

  // Filter KPIs based on current filters
  const applyFiltersToData = useCallback((kpis: KPI[], currentFilters: KPIFilterValue): KPI[] => {
    let filtered = [...kpis]

    // Search filter
    if (currentFilters.search) {
      const searchTerm = currentFilters.search.toLowerCase()
      filtered = filtered.filter(kpi =>
        kpi.name.toLowerCase().includes(searchTerm) ||
        kpi.name_ar?.toLowerCase().includes(searchTerm) ||
        kpi.description?.toLowerCase().includes(searchTerm) ||
        kpi.description_ar?.toLowerCase().includes(searchTerm) ||
        kpi.category_name?.toLowerCase().includes(searchTerm)
      )
    }

    // Category filter
    if (currentFilters.categories && currentFilters.categories.length > 0) {
      filtered = filtered.filter(kpi => 
        currentFilters.categories!.includes(kpi.category)
      )
    }

    // Status filter
    if (currentFilters.status) {
      filtered = filtered.filter(kpi => kpi.status === currentFilters.status)
    }

    // Frequency filter
    if (currentFilters.frequency) {
      filtered = filtered.filter(kpi => kpi.frequency === currentFilters.frequency)
    }

    // Measurement type filter
    if (currentFilters.measurementType) {
      filtered = filtered.filter(kpi => kpi.measurement_type === currentFilters.measurementType)
    }

    // Trend direction filter
    if (currentFilters.trendDirection) {
      filtered = filtered.filter(kpi => kpi.trend?.direction === currentFilters.trendDirection)
    }

    // Target achievement filter
    if (currentFilters.targetAchievement) {
      const [min, max] = currentFilters.targetAchievement
      filtered = filtered.filter(kpi => {
        const achievement = kpi.target_achievement || 0
        return achievement >= min && achievement <= max
      })
    }

    // Date range filter (filter by last updated or created date)
    if (currentFilters.dateRange) {
      const { from, to } = currentFilters.dateRange
      filtered = filtered.filter(kpi => {
        const kpiDate = new Date(kpi.updated_at || kpi.created_at)
        return kpiDate >= from && kpiDate <= to
      })
    }

    return filtered
  }, [])

  // Apply filters with debouncing
  const applyFilters = useCallback(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }

    const timer = setTimeout(() => {
      const filtered = applyFiltersToData(allKPIs, filters)
      setFilteredKPIs(filtered)
    }, debounceMs)

    setDebounceTimer(timer)
  }, [allKPIs, filters, applyFiltersToData, debounceMs, debounceTimer])

  // Auto-apply filters when they change
  useEffect(() => {
    if (autoApply) {
      applyFilters()
    }
  }, [filters, autoApply, applyFilters])

  // Apply filters when KPIs change
  useEffect(() => {
    const filtered = applyFiltersToData(allKPIs, filters)
    setFilteredKPIs(filtered)
  }, [allKPIs, applyFiltersToData, filters])

  // Update a specific filter
  const updateFilter = useCallback(<K extends keyof KPIFilterValue>(key: K, value: KPIFilterValue[K]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  // Reset all filters
  const resetFilters = useCallback(() => {
    setFilters({})
  }, [])

  // Clear a specific filter
  const clearFilter = useCallback((key: keyof KPIFilterValue) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }, [])

  // Date range helpers
  const setDateRange = useCallback((range: DateRange) => {
    updateFilter('dateRange', range)
  }, [updateFilter])

  const clearDateRange = useCallback(() => {
    clearFilter('dateRange')
  }, [clearFilter])

  // Search helpers
  const setSearchQuery = useCallback((query: string) => {
    updateFilter('search', query || undefined)
  }, [updateFilter])

  const clearSearch = useCallback(() => {
    clearFilter('search')
  }, [clearFilter])

  // Category helpers
  const toggleCategory = useCallback((categoryId: string) => {
    setFilters(prev => {
      const currentCategories = prev.categories || []
      const isSelected = currentCategories.includes(categoryId)
      
      let newCategories: string[]
      if (isSelected) {
        newCategories = currentCategories.filter(id => id !== categoryId)
      } else {
        newCategories = [...currentCategories, categoryId]
      }
      
      return {
        ...prev,
        categories: newCategories.length > 0 ? newCategories : undefined
      }
    })
  }, [])

  const selectAllCategories = useCallback((categories: KPICategory[]) => {
    const allCategoryIds = categories.map(cat => cat.id)
    updateFilter('categories', allCategoryIds)
  }, [updateFilter])

  const clearCategories = useCallback(() => {
    clearFilter('categories')
  }, [clearFilter])

  // Calculate active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.categories?.length) count++
    if (filters.status) count++
    if (filters.frequency) count++
    if (filters.measurementType) count++
    if (filters.trendDirection) count++
    if (filters.targetAchievement && 
        (filters.targetAchievement[0] > 0 || filters.targetAchievement[1] < 200)) count++
    if (filters.dateRange) count++
    if (filters.search) count++
    return count
  }, [filters])

  const hasActiveFilters = useMemo(() => activeFilterCount > 0, [activeFilterCount])

  // Refresh data
  const refresh = useCallback(async () => {
    await loadKPIs()
  }, [loadKPIs])

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [debounceTimer])

  return {
    // Filter state
    filters,
    setFilters,
    updateFilter,
    resetFilters,
    
    // Filtered data
    filteredKPIs,
    loading,
    error,
    
    // Filter actions
    applyFilters,
    clearFilter,
    
    // Date range helpers
    setDateRange,
    clearDateRange,
    
    // Search helpers
    setSearchQuery,
    clearSearch,
    
    // Category helpers
    toggleCategory,
    selectAllCategories,
    clearCategories,
    
    // Filter statistics
    activeFilterCount,
    hasActiveFilters,
    
    // Data refresh
    refresh
  }
}
