/**
 * KPI Modals Hook
 * Custom hook for managing KPI-related modals
 */

import { useCallback } from 'react'
import { useModal, ModalType } from '@/contexts/ModalContext'
import { KPI, KPICategory, KPIValue } from '@/services/kpiService'

interface UseKPIModalsReturn {
  // KPI CRUD modals
  openCreateKPIModal: (categories: KPICategory[], onSave: (data: Partial<KPI>) => Promise<void>) => void
  openEditKPIModal: (kpi: KPI, categories: KPICategory[], onSave: (data: Partial<KPI>) => Promise<void>) => void
  openViewKPIModal: (kpi: KPI, categories: KPICategory[]) => void
  
  // KPI Value modals
  openAddValueModal: (kpi: KPI, onSave: (data: Partial<KPIValue>) => Promise<void>) => void
  openEditValueModal: (kpi: KPI, value: KPIValue, onSave: (data: Partial<KPIValue>) => Promise<void>) => void
  
  // KPI Category modals
  openCategoryModal: (category: KPICategory | null, onSave: (data: Partial<KPICategory>) => Promise<void>) => void
  
  // KPI Target modals
  openTargetModal: (kpi: KPI, onSave: (data: Partial<KPI>) => Promise<void>) => void
  
  // Confirmation modals
  openDeleteConfirmModal: (
    itemName: string,
    onConfirm: () => Promise<void>,
    title?: string,
    message?: string
  ) => void
  
  // Export/Import modals
  openExportModal: (
    onExport: (format: 'csv' | 'excel' | 'pdf') => Promise<void>,
    formats?: Array<'csv' | 'excel' | 'pdf'>,
    title?: string
  ) => void
  
  openImportModal: (
    onImport: (file: File) => Promise<{ success: number; errors: any[] }>,
    acceptedFormats?: string[],
    title?: string
  ) => void
}

export function useKPIModals(): UseKPIModalsReturn {
  const { openModal } = useModal()
  
  // KPI CRUD modals
  const openCreateKPIModal = useCallback((
    categories: KPICategory[],
    onSave: (data: Partial<KPI>) => Promise<void>
  ) => {
    openModal('kpi-create', null, { categories, onSave })
  }, [openModal])
  
  const openEditKPIModal = useCallback((
    kpi: KPI,
    categories: KPICategory[],
    onSave: (data: Partial<KPI>) => Promise<void>
  ) => {
    openModal('kpi-edit', kpi as unknown as Record<string, unknown>, { categories, onSave })
  }, [openModal])
  
  const openViewKPIModal = useCallback((
    kpi: KPI,
    categories: KPICategory[]
  ) => {
    openModal('kpi-view', kpi as unknown as Record<string, unknown>, { categories })
  }, [openModal])
  
  // KPI Value modals
  const openAddValueModal = useCallback((
    kpi: KPI,
    onSave: (data: Partial<KPIValue>) => Promise<void>
  ) => {
    openModal('kpi-value-add', kpi as unknown as Record<string, unknown>, { onSave })
  }, [openModal])

  const openEditValueModal = useCallback((
    kpi: KPI,
    value: KPIValue,
    onSave: (data: Partial<KPIValue>) => Promise<void>
  ) => {
    openModal('kpi-value-edit', kpi as unknown as Record<string, unknown>, { value, onSave })
  }, [openModal])
  
  // KPI Category modals
  const openCategoryModal = useCallback((
    category: KPICategory | null,
    onSave: (data: Partial<KPICategory>) => Promise<void>
  ) => {
    openModal('kpi-category', category as Record<string, unknown> | null, { onSave })
  }, [openModal])
  
  // KPI Target modals
  const openTargetModal = useCallback((
    kpi: KPI,
    onSave: (data: Partial<KPI>) => Promise<void>
  ) => {
    openModal('kpi-target', kpi as unknown as Record<string, unknown>, { onSave })
  }, [openModal])
  
  // Confirmation modals
  const openDeleteConfirmModal = useCallback((
    itemName: string,
    onConfirm: () => Promise<void>,
    title?: string,
    message?: string
  ) => {
    openModal('kpi-delete-confirm', null, {
      itemName,
      onConfirm,
      title,
      message
    })
  }, [openModal])
  
  // Export/Import modals
  const openExportModal = useCallback((
    onExport: (format: 'csv' | 'excel' | 'pdf') => Promise<void>,
    formats?: Array<'csv' | 'excel' | 'pdf'>,
    title?: string
  ) => {
    openModal('kpi-export', null, {
      onExport,
      formats,
      title
    })
  }, [openModal])
  
  const openImportModal = useCallback((
    onImport: (file: File) => Promise<{ success: number; errors: any[] }>,
    acceptedFormats?: string[],
    title?: string
  ) => {
    openModal('kpi-import', null, {
      onImport,
      acceptedFormats,
      title
    })
  }, [openModal])
  
  return {
    openCreateKPIModal,
    openEditKPIModal,
    openViewKPIModal,
    openAddValueModal,
    openEditValueModal,
    openCategoryModal,
    openTargetModal,
    openDeleteConfirmModal,
    openExportModal,
    openImportModal
  }
}
