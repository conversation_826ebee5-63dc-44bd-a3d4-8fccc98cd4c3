/**
 * Advanced Features Hook
 * Provides easy access to search, export, and notification features
 */

import { useState, useCallback, useEffect } from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '../store'
import { searchService, SearchQuery, SearchResult } from '../services/search'
import { exportService, ExportOptions, ExportColumn } from '../services/export'
import webSocketService, { NotificationData } from '../services/websocket'

export interface UseAdvancedFeaturesOptions {
  module?: string
  enableSearch?: boolean
  enableExport?: boolean
  enableNotifications?: boolean
}

export interface SearchState {
  isOpen: boolean
  query: string
  results: SearchResult[]
  isLoading: boolean
  suggestions: string[]
}

export interface ExportState {
  isOpen: boolean
  isExporting: boolean
  progress: number
  error: string | null
}

export interface NotificationState {
  isOpen: boolean
  unreadCount: number
  connectionStatus: 'connected' | 'disconnected' | 'connecting'
}

export function useAdvancedFeatures(options: UseAdvancedFeaturesOptions = {}) {
  const {
    module,
    enableSearch = true,
    enableExport = true,
    enableNotifications = true
  } = options

  // Redux state
  const { notifications } = useSelector((state: RootState) => state.notifications)
  const { user } = useSelector((state: RootState) => state.auth)

  // Search state
  const [searchState, setSearchState] = useState<SearchState>({
    isOpen: false,
    query: '',
    results: [],
    isLoading: false,
    suggestions: []
  })

  // Export state
  const [exportState, setExportState] = useState<ExportState>({
    isOpen: false,
    isExporting: false,
    progress: 0,
    error: null
  })

  // Notification state
  const [notificationState, setNotificationState] = useState<NotificationState>({
    isOpen: false,
    unreadCount: 0,
    connectionStatus: 'disconnected'
  })

  // Search functions
  const openSearch = useCallback(() => {
    if (!enableSearch) return
    setSearchState(prev => ({ ...prev, isOpen: true }))
  }, [enableSearch])

  const closeSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      isOpen: false,
      query: '',
      results: [],
      suggestions: []
    }))
  }, [])

  const performSearch = useCallback(async (query: string) => {
    if (!enableSearch || !query.trim()) return

    setSearchState(prev => ({ ...prev, isLoading: true, query }))

    try {
      const searchQuery: SearchQuery = {
        query,
        pagination: { page: 1, limit: 10 }
      }

      const response = module
        ? await searchService.moduleSearch(module, searchQuery)
        : await searchService.globalSearch(searchQuery)

      setSearchState(prev => ({
        ...prev,
        results: response.results,
        isLoading: false
      }))
    } catch (error) {
      console.error('Search error:', error)
      setSearchState(prev => ({
        ...prev,
        results: [],
        isLoading: false
      }))
    }
  }, [enableSearch, module])

  const getSuggestions = useCallback(async (partialQuery: string) => {
    if (!enableSearch || !partialQuery.trim()) return

    try {
      const suggestions = await searchService.getSuggestions(partialQuery, module)
      setSearchState(prev => ({ ...prev, suggestions }))
    } catch (error) {
      console.error('Suggestions error:', error)
    }
  }, [enableSearch, module])

  // Export functions
  const openExport = useCallback(() => {
    if (!enableExport) return
    setExportState(prev => ({ ...prev, isOpen: true, error: null }))
  }, [enableExport])

  const closeExport = useCallback(() => {
    setExportState(prev => ({
      ...prev,
      isOpen: false,
      isExporting: false,
      progress: 0,
      error: null
    }))
  }, [])

  const exportData = useCallback(async (
    data: Record<string, unknown>[],
    columns: ExportColumn[],
    options: Partial<ExportOptions> = {}
  ) => {
    if (!enableExport) return

    setExportState(prev => ({ ...prev, isExporting: true, progress: 0, error: null }))

    try {
      const exportOptions: ExportOptions = {
        format: 'excel',
        columns,
        ...options
      }

      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportState(prev => {
          if (prev.progress >= 90) return prev
          return { ...prev, progress: prev.progress + 10 }
        })
      }, 200)

      switch (exportOptions.format) {
        case 'excel':
          await exportService.exportToExcel(data, exportOptions)
          break
        case 'pdf':
          await exportService.exportToPDF(data, exportOptions)
          break
        case 'csv':
          await exportService.exportToCSV(data, exportOptions)
          break
        case 'json':
          await exportService.exportToJSON(data, exportOptions)
          break
      }

      clearInterval(progressInterval)
      setExportState(prev => ({ ...prev, progress: 100, isExporting: false }))

      // Auto close after success
      setTimeout(() => {
        closeExport()
      }, 2000)

    } catch (error) {
      console.error('Export error:', error)
      setExportState(prev => ({
        ...prev,
        isExporting: false,
        progress: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }))
    }
  }, [enableExport, closeExport])

  const exportLargeDataset = useCallback(async (
    endpoint: string,
    options: ExportOptions
  ) => {
    if (!enableExport) return

    setExportState(prev => ({ ...prev, isExporting: true, progress: 0, error: null }))

    try {
      const downloadUrl = await exportService.exportLargeDataset(
        endpoint,
        options,
        (progress) => {
          setExportState(prev => ({ ...prev, progress: progress.progress }))
        }
      )

      // Open download URL
      window.open(downloadUrl, '_blank')

      setExportState(prev => ({ ...prev, isExporting: false, progress: 100 }))

      setTimeout(() => {
        closeExport()
      }, 2000)

    } catch (error) {
      console.error('Large export error:', error)
      setExportState(prev => ({
        ...prev,
        isExporting: false,
        progress: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }))
    }
  }, [enableExport, closeExport])

  // Notification functions
  const openNotifications = useCallback(() => {
    if (!enableNotifications) return
    setNotificationState(prev => ({ ...prev, isOpen: true }))
  }, [enableNotifications])

  const closeNotifications = useCallback(() => {
    setNotificationState(prev => ({ ...prev, isOpen: false }))
  }, [])

  const requestNotificationPermission = useCallback(async () => {
    if (!enableNotifications) return 'denied'
    return await (webSocketService.constructor as any).requestNotificationPermission()
  }, [enableNotifications])

  // WebSocket connection management
  useEffect(() => {
    if (!enableNotifications) return

    const handleConnectionChange = (status: 'connected' | 'disconnected') => {
      setNotificationState(prev => ({ ...prev, connectionStatus: status }))
    }

    const handleNewNotification = (notification: NotificationData) => {
      // Update unread count
      setNotificationState(prev => ({
        ...prev,
        unreadCount: prev.unreadCount + 1
      }))
    }

    webSocketService.on('connected', () => handleConnectionChange('connected'))
    webSocketService.on('disconnected', () => handleConnectionChange('disconnected'))
    webSocketService.on('notification', handleNewNotification)

    // Set initial connection status
    setNotificationState(prev => ({
      ...prev,
      connectionStatus: webSocketService.isWebSocketConnected() ? 'connected' : 'disconnected'
    }))

    return () => {
      webSocketService.off('connected', () => handleConnectionChange('connected'))
      webSocketService.off('disconnected', () => handleConnectionChange('disconnected'))
      webSocketService.off('notification', handleNewNotification)
    }
  }, [enableNotifications])

  // Update unread count from Redux
  useEffect(() => {
    if (enableNotifications) {
      const unreadCount = notifications.filter(n => !n.isRead).length
      setNotificationState(prev => ({ ...prev, unreadCount }))
    }
  }, [notifications, enableNotifications])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K for search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        openSearch()
      }

      // Ctrl/Cmd + E for export
      if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
        event.preventDefault()
        openExport()
      }

      // Ctrl/Cmd + N for notifications
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault()
        openNotifications()
      }

      // Escape to close any open dialogs
      if (event.key === 'Escape') {
        if (searchState.isOpen) closeSearch()
        if (exportState.isOpen) closeExport()
        if (notificationState.isOpen) closeNotifications()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [
    openSearch,
    openExport,
    openNotifications,
    closeSearch,
    closeExport,
    closeNotifications,
    searchState.isOpen,
    exportState.isOpen,
    notificationState.isOpen
  ])

  return {
    // Search
    search: {
      ...searchState,
      open: openSearch,
      close: closeSearch,
      perform: performSearch,
      getSuggestions
    },

    // Export
    export: {
      ...exportState,
      open: openExport,
      close: closeExport,
      exportData,
      exportLargeDataset
    },

    // Notifications
    notifications: {
      ...notificationState,
      open: openNotifications,
      close: closeNotifications,
      requestPermission: requestNotificationPermission
    },

    // Utility functions
    utils: {
      isFeatureEnabled: (feature: 'search' | 'export' | 'notifications') => {
        switch (feature) {
          case 'search': return enableSearch
          case 'export': return enableExport
          case 'notifications': return enableNotifications
          default: return false
        }
      },

      getKeyboardShortcuts: () => ({
        search: 'Ctrl/Cmd + K',
        export: 'Ctrl/Cmd + E',
        notifications: 'Ctrl/Cmd + N',
        close: 'Escape'
      })
    }
  }
}

export default useAdvancedFeatures
