/**
 * API Service Layer for EMS Application
 * Handles all HTTP requests to the Django backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// API Response Types
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
}

export interface ApiErrorInterface {
  message: string
  status: number
  details?: Record<string, unknown>
}

// Authentication Types
export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  access: string
  refresh: string
  user: User
}

export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  profile: UserProfile
}

export interface UserRole {
  id: string
  name: string
  nameAr: string
  permissions: Permission[]
  dashboardConfig: DashboardConfig
}

export interface Permission {
  module: string
  actions: string[]
}

export interface DashboardConfig {
  allowedRoutes: string[]
  defaultWidgets: string[]
  customizations: Record<string, unknown>
}

export interface UserProfile {
  avatar?: string
  phone?: string
  department?: string
  position?: string
  preferred_language: 'ar' | 'en'
  timezone: string
}

// HTTP Client Class
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.token = localStorage.getItem('access_token') || localStorage.getItem('token')
    console.log('ApiClient initialized with token:', !!this.token)

    // Ensure token is available for immediate use
    if (this.token) {
      console.log('Token found in localStorage, API client ready')
    }
  }

  // Set authentication token
  setToken(token: string | null) {
    this.token = token
    if (token) {
      localStorage.setItem('access_token', token)
      localStorage.setItem('token', token) // Backup storage
      console.log('Token stored in localStorage')
    } else {
      localStorage.removeItem('access_token')
      localStorage.removeItem('token')
      console.log('Token removed from localStorage')
    }
  }

  // Get authentication headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`

    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError({
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        })
      }

      const data = await response.json()

      return {
        data,
        status: response.status,
        message: data.message,
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      throw new ApiError({
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
        details: error,
      })
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, config?: { params?: Record<string, string | number | boolean | undefined> }): Promise<ApiResponse<T>> {
    // Handle query parameters from config
    let url = endpoint
    if (config?.params) {
      const searchParams = new URLSearchParams()
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }
    return this.request<T>(url, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any, config?: Record<string, unknown>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key])
      })
    }

    const headers: HeadersInit = {}
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers,
    })
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// Custom error class
class ApiError extends Error {
  status: number
  details?: Record<string, unknown>

  constructor({ message, status, details }: { message: string; status: number; details?: Record<string, unknown> }) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

export { ApiError }

// Authentication API
export const authAPI = {
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login/', credentials)

      // Store tokens
      apiClient.setToken(response.data.access)
      localStorage.setItem('refresh_token', response.data.refresh)

      return response.data
    } catch (error) {
      throw error
    }
  },

  logout: async (): Promise<void> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        await apiClient.post('/auth/logout/', { refresh: refreshToken })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      apiClient.setToken(null)
      localStorage.removeItem('refresh_token')
    }
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get<User>('/auth/user/')
    return response.data
  },

  updateProfile: async (profileData: Partial<User>): Promise<User> => {
    const response = await apiClient.patch<User>('/auth/profile/', profileData)
    return response.data
  },

  changePassword: async (passwordData: {
    current_password: string
    new_password: string
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/auth/change-password/', passwordData)
    return response.data
  },
}

// Dashboard API
export interface DashboardStats {
  total_employees: number
  total_departments: number
  active_projects: number
  pending_tasks: number
  pending_leave_requests: number
  monthly_expenses: number
  system_health: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
  }
}

export const dashboardAPI = {
  getStats: async (): Promise<DashboardStats> => {
    const response = await apiClient.get<DashboardStats>('/dashboard-stats/')
    return response.data
  },
}

// Utility functions
export const isApiError = (error: unknown): error is ApiError => {
  return error instanceof ApiError
}

export const handleApiError = (error: unknown): string => {
  if (isApiError(error)) {
    return error.message
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'An unexpected error occurred'
}
