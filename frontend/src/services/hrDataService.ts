/**
 * HR Data Service
 * Generates realistic HR data for reports and analytics
 */

// HR Report Data Interface
export interface HRReportData {
  totalEmployees: number
  activeEmployees: number
  newHires: number
  departures: number
  departmentBreakdown: Array<{
    name: string
    count: number
    percentage: number
  }>
  genderDistribution: {
    male: number
    female: number
    other: number
  }
  ageGroups: Array<{
    range: string
    count: number
    percentage: number
  }>
  salaryRanges: Array<{
    range: string
    count: number
    averageSalary: number
  }>
  performanceMetrics: {
    excellent: number
    good: number
    average: number
    needsImprovement: number
  }
  attendanceRate: number
  turnoverRate: number
  averageTenure: number
  trainingHours: number
  satisfactionScore: number
}

// HR Data Service - generates realistic data for reports

export class HRDataService {
  private static instance: HRDataService

  public static getInstance(): HRDataService {
    if (!HRDataService.instance) {
      HRDataService.instance = new HRDataService()
    }
    return HRDataService.instance
  }

  private generateRandomData(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  private generateTrendData(baseValue: number, months: number, volatility: number = 0.1): number[] {
    const data: number[] = []
    let currentValue = baseValue

    for (let i = 0; i < months; i++) {
      const change = (Math.random() - 0.5) * 2 * volatility * baseValue
      currentValue = Math.max(0, currentValue + change)
      data.push(Math.round(currentValue))
    }

    return data
  }

  public generateComprehensiveHRData(language: 'ar' | 'en' = 'en'): HRReportData {
    const currentDate = new Date()
    const startDate = new Date(currentDate.getFullYear(), 0, 1) // Start of year
    const endDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate())

    // Base metrics
    const totalEmployees = this.generateRandomData(1200, 1300)
    const activeEmployees = Math.floor(totalEmployees * 0.96)
    const inactiveEmployees = totalEmployees - activeEmployees
    const newHires = this.generateRandomData(45, 65)
    const terminations = this.generateRandomData(15, 25)
    const presentToday = Math.floor(activeEmployees * 0.92)
    const absentToday = Math.floor(activeEmployees * 0.03)
    const onLeave = activeEmployees - presentToday - absentToday
    const averageSalary = this.generateRandomData(75000, 95000)
    const totalPayroll = Math.floor(activeEmployees * averageSalary / 12) // Monthly
    const turnoverRate = Math.round((terminations / totalEmployees) * 100 * 10) / 10
    const satisfactionScore = this.generateRandomData(75, 90)

    // Department data - Always use English for PDF compatibility
    const departments = [
      'Information Technology',
      'Human Resources',
      'Finance',
      'Sales',
      'Marketing',
      'Operations'
    ]

    const departmentData = departments.map(name => ({
      name,
      employees: this.generateRandomData(80, 250),
      budget: this.generateRandomData(500000, 2000000),
      performance: this.generateRandomData(75, 95)
    }))

    // Age distribution - Always use English for PDF compatibility
    const ageGroups = [
      '20-25 years',
      '26-30 years',
      '31-35 years',
      '36-40 years',
      '41-45 years',
      '46-50 years',
      '50+ years'
    ]

    const employeesByAge = ageGroups.map(ageGroup => ({
      ageGroup,
      count: this.generateRandomData(80, 200)
    }))

    // Salary distribution - Always use English for PDF compatibility
    const salaryRanges = [
      '$30,000-$50,000',
      '$50,000-$70,000',
      '$70,000-$90,000',
      '$90,000-$120,000',
      '$120,000+'
    ]

    const salaryDistribution = salaryRanges.map(range => ({
      range,
      count: this.generateRandomData(50, 300)
    }))

    // Leave data - Always use English for PDF compatibility
    const leaveTypes = [
      'Annual Leave',
      'Sick Leave',
      'Maternity Leave',
      'Emergency Leave',
      'Study Leave'
    ]

    const leaveData = leaveTypes.map(type => ({
      type,
      approved: this.generateRandomData(20, 80),
      pending: this.generateRandomData(5, 25),
      rejected: this.generateRandomData(1, 10)
    }))

    // Performance trend data (last 12 months) - Always use English for PDF compatibility
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ]

    const productivityTrend = this.generateTrendData(85, 12, 0.05)
    const satisfactionTrend = this.generateTrendData(80, 12, 0.08)

    const performanceData = months.slice(0, 12).map((month, index) => ({
      month,
      productivity: productivityTrend[index],
      satisfaction: satisfactionTrend[index]
    }))

    // Company information - Always use English for PDF compatibility
    const companyInfo = {
      name: 'Advanced Management Solutions',
      address: 'Riyadh, Saudi Arabia',
      phone: '+966 11 123 4567',
      email: '<EMAIL>'
    }

    // Report period - Always use English for PDF compatibility
    const reportPeriod = {
      startDate: startDate.toLocaleDateString('en-US'),
      endDate: endDate.toLocaleDateString('en-US'),
      generatedDate: currentDate.toLocaleDateString('en-US'),
      generatedBy: 'HR Management System'
    }

    return {
      companyInfo,
      reportPeriod,
      metrics: {
        totalEmployees,
        activeEmployees,
        inactiveEmployees,
        newHires,
        terminations,
        presentToday,
        absentToday,
        onLeave,
        averageSalary,
        totalPayroll,
        turnoverRate,
        satisfactionScore
      },
      departmentData,
      employeesByAge,
      salaryDistribution,
      leaveData,
      performanceData
    }
  }

  public generateQuickMetrics() {
    const totalEmployees = this.generateRandomData(1200, 1300)
    const activeEmployees = Math.floor(totalEmployees * 0.96)

    return {
      totalEmployees,
      activeEmployees,
      newHires: this.generateRandomData(45, 65),
      presentToday: Math.floor(activeEmployees * 0.92),
      absentToday: Math.floor(activeEmployees * 0.03),
      lateArrivals: this.generateRandomData(10, 25),
      pendingLeaves: this.generateRandomData(15, 35),
      approvedLeaves: this.generateRandomData(120, 180),
      overtimeHours: this.generateRandomData(2000, 3000),
      avgPerformance: this.generateRandomData(82, 92),
      topPerformers: this.generateRandomData(35, 55),
      trainingCompleted: this.generateRandomData(75, 95),
      monthlyPayroll: Math.floor(activeEmployees * this.generateRandomData(75000, 95000) / 12),
      benefitsCost: this.generateRandomData(1800000, 2400000),
      recruitmentCost: this.generateRandomData(400000, 600000)
    }
  }

  public generateEmployeeList(count: number = 50) {
    const firstNames = [
      'Ahmed', 'Mohammed', 'Fatima', 'Aisha', 'Omar', 'Layla', 'Hassan', 'Zainab',
      'Ali', 'Maryam', 'Khalid', 'Nour', 'Saad', 'Hala', 'Faisal', 'Reem'
    ]

    const lastNames = [
      'Al-Ahmad', 'Al-Mohammed', 'Al-Hassan', 'Al-Ali', 'Al-Omar', 'Al-Khalid',
      'Al-Saad', 'Al-Faisal', 'Al-Rashid', 'Al-Mansour', 'Al-Zahrani', 'Al-Ghamdi'
    ]

    const departments = ['HR', 'IT', 'Finance', 'Sales', 'Marketing', 'Operations']
    const positions = [
      'Manager', 'Senior Specialist', 'Specialist', 'Coordinator', 'Assistant',
      'Analyst', 'Developer', 'Consultant', 'Executive', 'Administrator'
    ]

    const employees = []

    for (let i = 0; i < count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
      const department = departments[Math.floor(Math.random() * departments.length)]
      const position = positions[Math.floor(Math.random() * positions.length)]

      employees.push({
        id: i + 1,
        firstName,
        lastName,
        name: `${firstName} ${lastName}`,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace('al-', '')}@company.com`,
        phone: `+966 5${Math.floor(Math.random() * 90000000 + 10000000)}`,
        department,
        position: `${department} ${position}`,
        status: Math.random() > 0.05 ? 'active' : 'inactive',
        performance: ['excellent', 'good', 'average'][Math.floor(Math.random() * 3)],
        salary: this.generateRandomData(50000, 150000),
        hireDate: new Date(
          2020 + Math.floor(Math.random() * 4),
          Math.floor(Math.random() * 12),
          Math.floor(Math.random() * 28) + 1
        ).toISOString().split('T')[0],
        address: 'Riyadh, Saudi Arabia',
        emergencyContact: `+966 5${Math.floor(Math.random() * 90000000 + 10000000)}`,
        nationalId: `${Math.floor(Math.random() * 9000000000 + 1000000000)}`
      })
    }

    return employees
  }

  public generateDepartmentList() {
    return [
      {
        id: 1,
        name: 'Human Resources',
        nameAr: 'Human Resources', // Use English for PDF compatibility
        manager: 'Sarah Al-Ahmad',
        employees: this.generateRandomData(25, 35),
        budget: this.generateRandomData(800000, 1200000),
        description: 'Manages employee relations, recruitment, and HR policies'
      },
      {
        id: 2,
        name: 'Information Technology',
        nameAr: 'Information Technology', // Use English for PDF compatibility
        manager: 'Ahmed Al-Hassan',
        employees: this.generateRandomData(45, 65),
        budget: this.generateRandomData(1500000, 2500000),
        description: 'Develops and maintains technology infrastructure'
      },
      {
        id: 3,
        name: 'Finance',
        nameAr: 'Finance', // Use English for PDF compatibility
        manager: 'Fatima Al-Omar',
        employees: this.generateRandomData(20, 30),
        budget: this.generateRandomData(600000, 1000000),
        description: 'Manages financial operations and accounting'
      },
      {
        id: 4,
        name: 'Sales',
        nameAr: 'Sales', // Use English for PDF compatibility
        manager: 'Omar Al-Khalid',
        employees: this.generateRandomData(35, 50),
        budget: this.generateRandomData(1000000, 1800000),
        description: 'Drives revenue through sales and client relationships'
      },
      {
        id: 5,
        name: 'Marketing',
        nameAr: 'Marketing', // Use English for PDF compatibility
        manager: 'Layla Al-Saad',
        employees: this.generateRandomData(15, 25),
        budget: this.generateRandomData(700000, 1300000),
        description: 'Promotes brand and manages marketing campaigns'
      },
      {
        id: 6,
        name: 'Operations',
        nameAr: 'Operations', // Use English for PDF compatibility
        manager: 'Hassan Al-Faisal',
        employees: this.generateRandomData(40, 60),
        budget: this.generateRandomData(1200000, 2000000),
        description: 'Oversees daily operations and process optimization'
      }
    ]
  }
}
