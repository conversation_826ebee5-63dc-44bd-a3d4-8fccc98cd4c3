/**
 * Calendar Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Users,
  Video,
  Eye,
  Edit,
  Trash2,
  User,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { calendarEventService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CalendarProps {
  language: 'ar' | 'en'
}

interface CalendarEvent {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  startTime: string
  endTime: string
  date: string
  type: 'meeting' | 'appointment' | 'deadline' | 'holiday' | 'training' | 'interview'
  location?: string
  locationAr?: string
  attendees: string
  attendeesAr: string
  isOnline: boolean
  meetingLink?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled'
  organizer: string
  organizerAr: string
  reminders: string // JSON string of reminder minutes
}

const translations = {
  ar: {
    calendar: 'التقويم والجدولة',
    addEvent: 'إضافة حدث',
    editEvent: 'تعديل الحدث',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الحدث؟',
    searchPlaceholder: 'البحث في الأحداث...',
    title: 'العنوان',
    description: 'الوصف',
    startTime: 'وقت البداية',
    endTime: 'وقت النهاية',
    date: 'التاريخ',
    type: 'النوع',
    location: 'المكان',
    attendees: 'الحضور',
    organizer: 'المنظم',
    priority: 'الأولوية',
    status: 'الحالة',
    isOnline: 'عبر الإنترنت',
    meetingLink: 'رابط الاجتماع',
    reminders: 'التذكيرات',
    meeting: 'اجتماع',
    appointment: 'موعد',
    deadline: 'موعد نهائي',
    holiday: 'عطلة',
    training: 'تدريب',
    interview: 'مقابلة',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل',
    scheduled: 'مجدول',
    'in-progress': 'قيد التنفيذ',
    completed: 'مكتمل',
    cancelled: 'ملغي'
  },
  en: {
    calendar: 'Calendar & Scheduling',
    addEvent: 'Add Event',
    editEvent: 'Edit Event',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this event?',
    searchPlaceholder: 'Search events...',
    title: 'Title',
    description: 'Description',
    startTime: 'Start Time',
    endTime: 'End Time',
    date: 'Date',
    type: 'Type',
    location: 'Location',
    attendees: 'Attendees',
    organizer: 'Organizer',
    priority: 'Priority',
    status: 'Status',
    isOnline: 'Online',
    meetingLink: 'Meeting Link',
    reminders: 'Reminders',
    meeting: 'Meeting',
    appointment: 'Appointment',
    deadline: 'Deadline',
    holiday: 'Holiday',
    training: 'Training',
    interview: 'Interview',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    scheduled: 'Scheduled',
    'in-progress': 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled'
  }
}

export default function Calendar({ language }: CalendarProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: events,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<CalendarEvent>({
    service: calendarEventService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'appointment': return 'bg-green-100 text-green-800 border-green-200'
      case 'deadline': return 'bg-red-100 text-red-800 border-red-200'
      case 'holiday': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'training': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'interview': return 'bg-cyan-100 text-cyan-800 border-cyan-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'urgent': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <Clock className="h-4 w-4 text-blue-400" />
      case 'in-progress': return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-400" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<CalendarEvent>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <CalendarIcon className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.date}</span>
        </div>
      )
    },
    {
      key: 'time',
      label: 'الوقت',
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.startTime} - {item.endTime}</span>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: CalendarEvent) => (
        <Badge className={getTypeColor(item.type)}>
          {t[item.type as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: CalendarEvent) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {t[item.priority as keyof typeof t]}
        </span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <span className="text-white/80">{t[item.status as keyof typeof t]}</span>
        </div>
      )
    },
    {
      key: 'organizer',
      label: t.organizer,
      render: (item: CalendarEvent) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.organizerAr : item.organizer}
          </span>
        </div>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: CalendarEvent) => (
        item.location ? (
          <div className="flex items-center gap-1">
            {item.isOnline ? <Video className="h-3 w-3 text-blue-400" /> : <MapPin className="h-3 w-3 text-red-400" />}
            <span className="text-white/80">
              {language === 'ar' ? item.locationAr : item.location}
            </span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<CalendarEvent>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: CalendarEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: CalendarEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: CalendarEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.meeting, value: 'meeting' },
        { label: t.appointment, value: 'appointment' },
        { label: t.deadline, value: 'deadline' },
        { label: t.holiday, value: 'holiday' },
        { label: t.training, value: 'training' },
        { label: t.interview, value: 'interview' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.urgent, value: 'urgent' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.scheduled, value: 'scheduled' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'startTime',
      label: t.startTime,
      type: 'text',
      required: true
    },
    {
      name: 'endTime',
      label: t.endTime,
      type: 'text',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.meeting, value: 'meeting' },
        { label: t.appointment, value: 'appointment' },
        { label: t.deadline, value: 'deadline' },
        { label: t.holiday, value: 'holiday' },
        { label: t.training, value: 'training' },
        { label: t.interview, value: 'interview' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.urgent, value: 'urgent' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.scheduled, value: 'scheduled' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'location',
      label: t.location,
      type: 'text'
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text'
    },
    {
      name: 'organizer',
      label: t.organizer,
      type: 'text',
      required: true
    },
    {
      name: 'organizerAr',
      label: t.organizer + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'attendees',
      label: t.attendees,
      type: 'text',
      placeholder: 'Comma-separated attendees'
    },
    {
      name: 'attendeesAr',
      label: t.attendees + ' (عربي)',
      type: 'text',
      placeholder: 'أسماء الحضور مفصولة بفواصل'
    },
    {
      name: 'isOnline',
      label: t.isOnline,
      type: 'checkbox'
    },
    {
      name: 'meetingLink',
      label: t.meetingLink,
      type: 'text'
    },
    {
      name: 'reminders',
      label: t.reminders,
      type: 'text',
      placeholder: 'Minutes before event (comma-separated)'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<CalendarEvent>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.calendar}
        data={events}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addEvent : modalMode === 'edit' ? t.editEvent : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
