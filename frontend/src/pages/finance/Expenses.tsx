/**
 * Expenses Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  Calendar,
  Receipt,
  CreditCard,
  Building,
  User,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { expenseService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ExpensesProps {
  language: 'ar' | 'en'
}

interface Expense {
  id: number
  description: string
  amount: number
  category: string
  date: string
  status: 'pending' | 'approved' | 'rejected'
  employee: string
  department: string
  receipt?: string
}

const translations = {
  ar: {
    expenses: 'المصروفات',
    addExpense: 'إضافة مصروف',
    editExpense: 'تعديل المصروف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المصروف؟',
    searchPlaceholder: 'البحث في المصروفات...',
    description: 'الوصف',
    amount: 'المبلغ',
    category: 'الفئة',
    date: 'التاريخ',
    status: 'الحالة',
    employee: 'الموظف',
    department: 'القسم',
    receipt: 'الإيصال',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض',
    categories: {
      travel: 'سفر',
      meals: 'وجبات',
      supplies: 'مستلزمات',
      equipment: 'معدات',
      training: 'تدريب',
      other: 'أخرى'
    }
  },
  en: {
    expenses: 'Expenses',
    addExpense: 'Add Expense',
    editExpense: 'Edit Expense',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this expense?',
    searchPlaceholder: 'Search expenses...',
    description: 'Description',
    amount: 'Amount',
    category: 'Category',
    date: 'Date',
    status: 'Status',
    employee: 'Employee',
    department: 'Department',
    receipt: 'Receipt',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    categories: {
      travel: 'Travel',
      meals: 'Meals',
      supplies: 'Supplies',
      equipment: 'Equipment',
      training: 'Training',
      other: 'Other'
    }
  }
}

export default function Expenses({ language }: ExpensesProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: expenses,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Expense>({
    service: expenseService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-3 w-3" />
      case 'approved':
        return <CheckCircle className="h-3 w-3" />
      case 'rejected':
        return <XCircle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'travel':
        return <Calendar className="h-4 w-4" />
      case 'equipment':
        return <Receipt className="h-4 w-4" />
      case 'training':
        return <User className="h-4 w-4" />
      default:
        return <DollarSign className="h-4 w-4" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<Expense>[] = [
    {
      key: 'description',
      label: t.description,
      sortable: true,
      render: (item: Expense) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon(item.category)}
          <span className="text-white">{item.description}</span>
        </div>
      )
    },
    {
      key: 'amount',
      label: t.amount,
      sortable: true,
      render: (item: Expense) => (
        <span className="text-white font-medium">
          {new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(item.amount)}
        </span>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Expense) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t.categories[item.category as keyof typeof t.categories] || item.category}
        </Badge>
      )
    },
    {
      key: 'employee',
      label: t.employee,
      render: (item: Expense) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.employee}</span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      render: (item: Expense) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.department}</span>
        </div>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: Expense) => (
        <span className="text-white/80">{item.date}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Expense) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{String(t[item.status as keyof typeof t])}</span>
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Expense>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Expense) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Expense) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Expense) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.travel, value: 'travel' },
        { label: t.categories.meals, value: 'meals' },
        { label: t.categories.supplies, value: 'supplies' },
        { label: t.categories.equipment, value: 'equipment' },
        { label: t.categories.training, value: 'training' },
        { label: t.categories.other, value: 'other' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'amount',
      label: t.amount,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.categories.travel, value: 'travel' },
        { label: t.categories.meals, value: 'meals' },
        { label: t.categories.supplies, value: 'supplies' },
        { label: t.categories.equipment, value: 'equipment' },
        { label: t.categories.training, value: 'training' },
        { label: t.categories.other, value: 'other' }
      ]
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'employee',
      label: t.employee,
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Expense>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for expense reports
        const response = await fetch(`http://localhost:8001/api/pdf/generate/expense-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          // Fallback to financial report if expense report not available
          const fallbackResponse = await fetch(`http://localhost:8001/api/pdf/generate/financial-report/?language=${language}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
            }
          })

          if (!fallbackResponse.ok) {
            throw new Error(`HTTP error! status: ${fallbackResponse.status}`)
          }

          const pdfBlob = await fallbackResponse.blob()
          const url = window.URL.createObjectURL(pdfBlob)
          const link = document.createElement('a')
          link.href = url
          link.download = `financial-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
          return
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `expense-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.expenses}
        data={expenses}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addExpense : modalMode === 'edit' ? t.editExpense : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
