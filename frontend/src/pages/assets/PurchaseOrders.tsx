/**
 * Purchase Orders Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  ShoppingCart,
  Calendar,
  DollarSign,
  Package,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { purchaseOrderService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PurchaseOrdersProps {
  language: 'ar' | 'en'
}

interface PurchaseOrder {
  id: number
  orderNumber: string
  supplier: string
  supplierAr: string
  orderDate: string
  deliveryDate: string
  totalAmount: number
  status: 'pending' | 'approved' | 'shipped' | 'delivered' | 'cancelled'
  items: number
  requestedBy: string
  approvedBy?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  description: string
  descriptionAr: string
}

const translations = {
  ar: {
    purchaseOrders: 'أوامر الشراء',
    addPurchaseOrder: 'إضافة أمر شراء',
    editPurchaseOrder: 'تعديل أمر الشراء',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف أمر الشراء هذا؟',
    searchPlaceholder: 'البحث في أوامر الشراء...',
    orderNumber: 'رقم الأمر',
    supplier: 'المورد',
    orderDate: 'تاريخ الأمر',
    deliveryDate: 'تاريخ التسليم',
    totalAmount: 'المبلغ الإجمالي',
    status: 'الحالة',
    items: 'عدد العناصر',
    requestedBy: 'طلب بواسطة',
    approvedBy: 'معتمد بواسطة',
    priority: 'الأولوية',
    description: 'الوصف',
    pending: 'معلق',
    approved: 'معتمد',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل'
  },
  en: {
    purchaseOrders: 'Purchase Orders',
    addPurchaseOrder: 'Add Purchase Order',
    editPurchaseOrder: 'Edit Purchase Order',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this purchase order?',
    searchPlaceholder: 'Search purchase orders...',
    orderNumber: 'Order Number',
    supplier: 'Supplier',
    orderDate: 'Order Date',
    deliveryDate: 'Delivery Date',
    totalAmount: 'Total Amount',
    status: 'Status',
    items: 'Items Count',
    requestedBy: 'Requested By',
    approvedBy: 'Approved By',
    priority: 'Priority',
    description: 'Description',
    pending: 'Pending',
    approved: 'Approved',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent'
  }
}

export default function PurchaseOrders({ language }: PurchaseOrdersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: purchaseOrders,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PurchaseOrder>({
    service: purchaseOrderService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-blue-500" />
      case 'shipped':
        return <Truck className="h-4 w-4 text-purple-500" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-400'
      case 'high':
        return 'text-orange-400'
      case 'medium':
        return 'text-blue-400'
      case 'low':
        return 'text-green-400'
      default:
        return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<PurchaseOrder>[] = [
    {
      key: 'orderNumber',
      label: t.orderNumber,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">{item.orderNumber}</div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 30) + '...' : item.description?.substring(0, 30) + '...'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'supplier',
      label: t.supplier,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <span className="text-white/80">
          {language === 'ar' ? item.supplierAr : item.supplier}
        </span>
      )
    },
    {
      key: 'orderDate',
      label: t.orderDate,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.orderDate}</span>
        </div>
      )
    },
    {
      key: 'deliveryDate',
      label: t.deliveryDate,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-1">
          <Truck className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.deliveryDate}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {t[item.priority as keyof typeof t]}
        </span>
      )
    },
    {
      key: 'totalAmount',
      label: t.totalAmount,
      sortable: true,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{formatCurrency(item.totalAmount)}</span>
        </div>
      )
    },
    {
      key: 'requestedBy',
      label: t.requestedBy,
      render: (item: PurchaseOrder) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.requestedBy}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<PurchaseOrder>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: PurchaseOrder) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: PurchaseOrder) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: PurchaseOrder) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'orderNumber',
      label: t.orderNumber,
      type: 'text',
      required: true
    },
    {
      name: 'supplier',
      label: t.supplier,
      type: 'text',
      required: true
    },
    {
      name: 'supplierAr',
      label: t.supplier + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'orderDate',
      label: t.orderDate,
      type: 'date',
      required: true
    },
    {
      name: 'deliveryDate',
      label: t.deliveryDate,
      type: 'date',
      required: true
    },
    {
      name: 'totalAmount',
      label: t.totalAmount,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'items',
      label: t.items,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'requestedBy',
      label: t.requestedBy,
      type: 'text',
      required: true
    },
    {
      name: 'approvedBy',
      label: t.approvedBy,
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PurchaseOrder>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.purchaseOrders}
        data={purchaseOrders}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addPurchaseOrder : modalMode === 'edit' ? t.editPurchaseOrder : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
