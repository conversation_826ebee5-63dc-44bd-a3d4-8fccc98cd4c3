/**
 * Assets Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  MapPin,
  // Calendar, // TODO: Add calendar integration for asset schedules
  User,
  // AlertTriangle, // TODO: Add asset status warnings
  CheckCircle,
  // Clock, // TODO: Add time-based asset tracking
  Wrench,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  Building,
  Laptop,
  Car,
  Printer,
  Monitor,
  Smartphone
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { assetService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface AssetsProps {
  language: 'ar' | 'en'
}

interface Asset {
  id: number
  assetId: string
  name: string
  nameAr: string
  category: string
  location: string
  assignedTo: string
  status: 'inUse' | 'available' | 'maintenance' | 'retired'
  purchaseDate: string
  purchasePrice: number
  currentValue: number
  warrantyExpiry: string
  serialNumber: string
  manufacturer: string
  model: string
  notes: string
  lastMaintenance: string
  nextMaintenance: string
}

const translations = {
  ar: {
    assets: 'الأصول',
    addAsset: 'إضافة أصل',
    editAsset: 'تعديل الأصل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الأصل؟',
    searchPlaceholder: 'البحث في الأصول...',
    assetId: 'رقم الأصل',
    assetName: 'اسم الأصل',
    category: 'الفئة',
    location: 'الموقع',
    assignedTo: 'مخصص لـ',
    status: 'الحالة',
    purchaseDate: 'تاريخ الشراء',
    purchasePrice: 'سعر الشراء',
    currentValue: 'القيمة الحالية',
    warrantyExpiry: 'انتهاء الضمان',
    serialNumber: 'الرقم التسلسلي',
    manufacturer: 'الشركة المصنعة',
    model: 'الموديل',
    notes: 'ملاحظات',
    lastMaintenance: 'آخر صيانة',
    nextMaintenance: 'الصيانة القادمة',
    available: 'متاح',
    inUse: 'قيد الاستخدام',
    maintenance: 'صيانة',
    retired: 'متقاعد',
    categories: {
      computers: 'أجهزة كمبيوتر',
      furniture: 'أثاث',
      vehicles: 'مركبات',
      equipment: 'معدات',
      electronics: 'إلكترونيات'
    }
  },
  en: {
    assets: 'Assets',
    addAsset: 'Add Asset',
    editAsset: 'Edit Asset',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this asset?',
    searchPlaceholder: 'Search assets...',
    assetId: 'Asset ID',
    assetName: 'Asset Name',
    category: 'Category',
    location: 'Location',
    assignedTo: 'Assigned To',
    status: 'Status',
    purchaseDate: 'Purchase Date',
    purchasePrice: 'Purchase Price',
    currentValue: 'Current Value',
    warrantyExpiry: 'Warranty Expiry',
    serialNumber: 'Serial Number',
    manufacturer: 'Manufacturer',
    model: 'Model',
    notes: 'Notes',
    lastMaintenance: 'Last Maintenance',
    nextMaintenance: 'Next Maintenance',
    available: 'Available',
    inUse: 'In Use',
    maintenance: 'Maintenance',
    retired: 'Retired',
    categories: {
      computers: 'Computers',
      furniture: 'Furniture',
      vehicles: 'Vehicles',
      equipment: 'Equipment',
      electronics: 'Electronics'
    }
  }
}

export default function Assets({ language }: AssetsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: assets,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Asset>({
    service: assetService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'computers':
        return <Laptop className="h-4 w-4" />
      case 'furniture':
        return <Building className="h-4 w-4" />
      case 'vehicles':
        return <Car className="h-4 w-4" />
      case 'equipment':
        return <Printer className="h-4 w-4" />
      case 'electronics':
        return <Monitor className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inUse':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'retired':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Asset>[] = [
    {
      key: 'assetId',
      label: t.assetId,
      sortable: true,
      render: (item: Asset) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon(item.category)}
          <span className="font-medium text-white">{item.assetId}</span>
        </div>
      )
    },
    {
      key: 'name',
      label: t.assetName,
      sortable: true,
      render: (item: Asset) => (
        <div>
          <div className="font-medium text-white">
            {language === 'ar' ? item.nameAr : item.name}
          </div>
          <div className="text-sm text-white/60">{item.manufacturer} {item.model}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Asset) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t.categories[item.category as keyof typeof t.categories] || item.category}
        </Badge>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: Asset) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.location}</span>
        </div>
      )
    },
    {
      key: 'assignedTo',
      label: t.assignedTo,
      render: (item: Asset) => (
        item.assignedTo ? (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3 text-green-400" />
            <span className="text-white/80">{item.assignedTo}</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Asset) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'purchasePrice',
      label: t.purchasePrice,
      sortable: true,
      render: (item: Asset) => (
        <span className="text-white font-medium">
          {new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(item.purchasePrice)}
        </span>
      )
    },
    {
      key: 'warrantyExpiry',
      label: t.warrantyExpiry,
      sortable: true,
      render: (item: Asset) => (
        <div className="text-sm">
          <div className="text-white/80">{item.warrantyExpiry}</div>
          {new Date(item.warrantyExpiry) < new Date() && (
            <div className="text-red-400 text-xs">منتهي</div>
          )}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Asset>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Asset) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Asset) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Asset) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.computers, value: 'computers' },
        { label: t.categories.furniture, value: 'furniture' },
        { label: t.categories.vehicles, value: 'vehicles' },
        { label: t.categories.equipment, value: 'equipment' },
        { label: t.categories.electronics, value: 'electronics' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.available, value: 'available' },
        { label: t.inUse, value: 'inUse' },
        { label: t.maintenance, value: 'maintenance' },
        { label: t.retired, value: 'retired' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'assetId',
      label: t.assetId,
      type: 'text',
      required: true,
      placeholder: 'AST-001'
    },
    {
      name: 'name',
      label: t.assetName,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.assetName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.categories.computers, value: 'computers' },
        { label: t.categories.furniture, value: 'furniture' },
        { label: t.categories.vehicles, value: 'vehicles' },
        { label: t.categories.equipment, value: 'equipment' },
        { label: t.categories.electronics, value: 'electronics' }
      ]
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.available, value: 'available' },
        { label: t.inUse, value: 'inUse' },
        { label: t.maintenance, value: 'maintenance' },
        { label: t.retired, value: 'retired' }
      ]
    },
    {
      name: 'purchaseDate',
      label: t.purchaseDate,
      type: 'date',
      required: true
    },
    {
      name: 'purchasePrice',
      label: t.purchasePrice,
      type: 'number',
      required: true
    },
    {
      name: 'currentValue',
      label: t.currentValue,
      type: 'number'
    },
    {
      name: 'warrantyExpiry',
      label: t.warrantyExpiry,
      type: 'date'
    },
    {
      name: 'serialNumber',
      label: t.serialNumber,
      type: 'text'
    },
    {
      name: 'manufacturer',
      label: t.manufacturer,
      type: 'text'
    },
    {
      name: 'model',
      label: t.model,
      type: 'text'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Asset>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for asset reports
        const response = await fetch(`http://localhost:8001/api/pdf/generate/asset-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `asset-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.assets}
        data={assets}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addAsset : modalMode === 'edit' ? t.editAsset : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
