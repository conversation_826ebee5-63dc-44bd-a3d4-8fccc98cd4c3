/**
 * KPI Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Target,
  TrendingUp,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Activity
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { kpiService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'
import { useKPIModals } from '@/hooks/useKPIModals'

interface KPIManagementProps {
  language: 'ar' | 'en'
}

interface KPI {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  targetValue: number
  currentValue: number
  unit: string
  unitAr: string
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  status: 'active' | 'inactive' | 'archived'
  priority: 'low' | 'medium' | 'high' | 'critical'
  owner: string
  ownerAr: string
  department: string
  departmentAr: string
  lastUpdated: string
  achievementPercentage: number
  trend: 'up' | 'down' | 'stable'
}

const translations = {
  ar: {
    kpiManagement: 'إدارة مؤشرات الأداء',
    addKPI: 'إضافة مؤشر أداء',
    editKPI: 'تعديل مؤشر الأداء',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف مؤشر الأداء هذا؟',
    searchPlaceholder: 'البحث في مؤشرات الأداء...',
    name: 'الاسم',
    description: 'الوصف',
    category: 'الفئة',
    targetValue: 'القيمة المستهدفة',
    currentValue: 'القيمة الحالية',
    unit: 'الوحدة',
    frequency: 'التكرار',
    status: 'الحالة',
    priority: 'الأولوية',
    owner: 'المالك',
    department: 'القسم',
    lastUpdated: 'آخر تحديث',
    achievementPercentage: 'نسبة الإنجاز',
    trend: 'الاتجاه',
    active: 'نشط',
    inactive: 'غير نشط',
    archived: 'مؤرشف',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    up: 'صاعد',
    down: 'هابط',
    stable: 'مستقر'
  },
  en: {
    kpiManagement: 'KPI Management',
    addKPI: 'Add KPI',
    editKPI: 'Edit KPI',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this KPI?',
    searchPlaceholder: 'Search KPIs...',
    name: 'Name',
    description: 'Description',
    category: 'Category',
    targetValue: 'Target Value',
    currentValue: 'Current Value',
    unit: 'Unit',
    frequency: 'Frequency',
    status: 'Status',
    priority: 'Priority',
    owner: 'Owner',
    department: 'Department',
    lastUpdated: 'Last Updated',
    achievementPercentage: 'Achievement %',
    trend: 'Trend',
    active: 'Active',
    inactive: 'Inactive',
    archived: 'Archived',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    up: 'Up',
    down: 'Down',
    stable: 'Stable'
  }
}

export default function KPIManagement({ language }: KPIManagementProps) {
  // Use the KPI modals hook instead of local state
  const kpiModals = useKPIModals()

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: kpis,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<KPI>({
    service: kpiService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'archived': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400'
      case 'high': return 'text-orange-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down': return <TrendingUp className="h-4 w-4 text-red-400 rotate-180" />
      case 'stable': return <Activity className="h-4 w-4 text-blue-400" />
      default: return <Activity className="h-4 w-4 text-gray-400" />
    }
  }

  const getAchievementColor = (percentage: number) => {
    if (percentage >= 100) return 'text-green-400'
    if (percentage >= 75) return 'text-yellow-400'
    if (percentage >= 50) return 'text-orange-400'
    return 'text-red-400'
  }

  // Table columns configuration
  const columns: TableColumn<KPI>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: KPI) => (
        <div className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.categoryAr : item.category}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'currentValue',
      label: t.currentValue,
      sortable: true,
      render: (item: KPI) => (
        <div className="flex items-center gap-2">
          <span className="text-white font-medium">{item.currentValue}</span>
          <span className="text-white/60">{language === 'ar' ? item.unitAr : item.unit}</span>
        </div>
      )
    },
    {
      key: 'targetValue',
      label: t.targetValue,
      sortable: true,
      render: (item: KPI) => (
        <div className="flex items-center gap-2">
          <Target className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.targetValue}</span>
        </div>
      )
    },
    {
      key: 'achievementPercentage',
      label: t.achievementPercentage,
      sortable: true,
      render: (item: KPI) => (
        <div className="flex items-center gap-2">
          <span className={`font-medium ${getAchievementColor(item.achievementPercentage)}`}>
            {item.achievementPercentage.toFixed(1)}%
          </span>
          {getTrendIcon(item.trend)}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: KPI) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {t[item.priority as keyof typeof t]}
        </span>
      )
    },
    {
      key: 'owner',
      label: t.owner,
      render: (item: KPI) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.ownerAr : item.owner}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: KPI) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'lastUpdated',
      label: t.lastUpdated,
      sortable: true,
      render: (item: KPI) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastUpdated}</span>
        </div>
      )
    }
  ]

  // Table actions configuration using modal context
  const actions: TableAction<KPI>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: KPI) => handleView(item),
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: KPI) => handleEdit(item),
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: KPI) => handleDelete(item),
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.archived, value: 'archived' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'frequency',
      label: t.frequency,
      options: [
        { label: t.daily, value: 'daily' },
        { label: t.weekly, value: 'weekly' },
        { label: t.monthly, value: 'monthly' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.yearly, value: 'yearly' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'targetValue',
      label: t.targetValue,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'currentValue',
      label: t.currentValue,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'unit',
      label: t.unit,
      type: 'text',
      required: true
    },
    {
      name: 'unitAr',
      label: t.unit + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'frequency',
      label: t.frequency,
      type: 'select',
      required: true,
      options: [
        { label: t.daily, value: 'daily' },
        { label: t.weekly, value: 'weekly' },
        { label: t.monthly, value: 'monthly' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.yearly, value: 'yearly' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.archived, value: 'archived' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'owner',
      label: t.owner,
      type: 'text',
      required: true
    },
    {
      name: 'ownerAr',
      label: t.owner + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    }
  ]

  // Event handlers using the modal context
  const handleCreate = () => {
    kpiModals.openCreateKPIModal([], async (data) => {
      await createItem(data as any)
      refresh()
    })
  }

  const handleEdit = (kpi: KPI) => {
    kpiModals.openEditKPIModal(kpi as any, [], async (data) => {
      await updateItem(kpi.id, data as any)
      refresh()
    })
  }

  const handleView = (kpi: KPI) => {
    kpiModals.openViewKPIModal(kpi as any, [])
  }

  const handleDelete = (kpi: KPI) => {
    kpiModals.openDeleteConfirmModal(
      kpi.name,
      async () => {
        await deleteItem(kpi.id)
        refresh()
      },
      t.confirmDelete,
      `Are you sure you want to delete "${kpi.name}"?`
    )
  }

  const handleExport = () => {
    kpiModals.openExportModal(
      async (format: 'csv' | 'excel' | 'pdf') => {
        try {
          if (format === 'pdf') {
            // Use new PDF generation API for KPI reports
            const response = await fetch(`http://localhost:8001/api/pdf/generate/kpi-report/?language=${language}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
              }
            })

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const pdfBlob = await response.blob()
            const url = window.URL.createObjectURL(pdfBlob)
            const link = document.createElement('a')
            link.href = url
            link.download = `kpi-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
          } else {
            await exportData('csv')
          }
        } catch (error) {
          console.error('Export error:', error)
        }
      },
      ['csv', 'excel', 'pdf'],
      'Export KPIs'
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.kpiManagement}
        data={kpis}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* Modal Manager is used instead of individual modals */}
    </div>
  )
}