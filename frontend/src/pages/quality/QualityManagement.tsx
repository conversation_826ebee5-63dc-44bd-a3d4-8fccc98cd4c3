/**
 * Quality Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Award,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  Star,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Building
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { qualityRecordService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface QualityManagementProps {
  language: 'ar' | 'en'
}

interface QualityRecord {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  type: 'inspection' | 'audit' | 'review' | 'incident' | 'improvement'
  category: string
  categoryAr: string
  status: 'planned' | 'in-progress' | 'completed' | 'failed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  inspector: string
  inspectorAr: string
  department: string
  departmentAr: string
  scheduledDate: string
  completedDate?: string
  score: number // 0-100
  findings: string
  findingsAr: string
  recommendations: string
  recommendationsAr: string
  correctiveActions: string
  correctiveActionsAr: string
  dueDate: string
  assignedTo: string
  assignedToAr: string
  standard: string
  standardAr: string
  nonConformities: number
  majorIssues: number
  minorIssues: number
  cost: number
}

const translations = {
  ar: {
    qualityManagement: 'إدارة الجودة',
    addRecord: 'إضافة سجل',
    editRecord: 'تعديل السجل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا السجل؟',
    searchPlaceholder: 'البحث في سجلات الجودة...',
    title: 'العنوان',
    description: 'الوصف',
    type: 'النوع',
    category: 'الفئة',
    status: 'الحالة',
    priority: 'الأولوية',
    inspector: 'المفتش',
    department: 'القسم',
    scheduledDate: 'التاريخ المجدول',
    completedDate: 'تاريخ الإكمال',
    score: 'النتيجة',
    findings: 'النتائج',
    recommendations: 'التوصيات',
    correctiveActions: 'الإجراءات التصحيحية',
    dueDate: 'تاريخ الاستحقاق',
    assignedTo: 'مكلف إلى',
    standard: 'المعيار',
    nonConformities: 'عدم المطابقة',
    majorIssues: 'القضايا الرئيسية',
    minorIssues: 'القضايا الطفيفة',
    cost: 'التكلفة',
    inspection: 'فحص',
    audit: 'تدقيق',
    review: 'مراجعة',
    incident: 'حادثة',
    improvement: 'تحسين',
    planned: 'مخطط',
    'in-progress': 'قيد التنفيذ',
    completed: 'مكتمل',
    failed: 'فاشل',
    cancelled: 'ملغي',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج'
  },
  en: {
    qualityManagement: 'Quality Management',
    addRecord: 'Add Record',
    editRecord: 'Edit Record',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this record?',
    searchPlaceholder: 'Search quality records...',
    title: 'Title',
    description: 'Description',
    type: 'Type',
    category: 'Category',
    status: 'Status',
    priority: 'Priority',
    inspector: 'Inspector',
    department: 'Department',
    scheduledDate: 'Scheduled Date',
    completedDate: 'Completed Date',
    score: 'Score',
    findings: 'Findings',
    recommendations: 'Recommendations',
    correctiveActions: 'Corrective Actions',
    dueDate: 'Due Date',
    assignedTo: 'Assigned To',
    standard: 'Standard',
    nonConformities: 'Non-Conformities',
    majorIssues: 'Major Issues',
    minorIssues: 'Minor Issues',
    cost: 'Cost',
    inspection: 'Inspection',
    audit: 'Audit',
    review: 'Review',
    incident: 'Incident',
    improvement: 'Improvement',
    planned: 'Planned',
    'in-progress': 'In Progress',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical'
  }
}

export default function QualityManagement({ language }: QualityManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: qualityRecords,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<QualityRecord>({
    service: qualityRecordService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-primary/20 text-primary border-primary/30'
      case 'in-progress': return 'bg-accent/20 text-accent border-accent/30'
      case 'completed': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'failed': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'cancelled': return 'bg-muted/20 text-muted-foreground border-muted/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'inspection': return 'bg-primary/20 text-primary border-primary/30'
      case 'audit': return 'bg-accent/20 text-accent border-accent/30'
      case 'review': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'incident': return 'bg-destructive/20 text-destructive border-destructive/30'
      case 'improvement': return 'bg-muted/20 text-muted-foreground border-muted/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-secondary/20 text-secondary border-secondary/30'
      case 'medium': return 'bg-accent/20 text-accent border-accent/30'
      case 'high': return 'bg-primary/20 text-primary border-primary/30'
      case 'critical': return 'bg-destructive/20 text-destructive border-destructive/30'
      default: return 'bg-muted/20 text-muted-foreground border-muted/30'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-secondary'
    if (score >= 80) return 'text-accent'
    if (score >= 70) return 'text-primary'
    return 'text-destructive'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'planned': return <Clock className="w-4 h-4" />
      case 'in-progress': return <Target className="w-4 h-4" />
      case 'completed': return <CheckCircle className="w-4 h-4" />
      case 'failed': return <XCircle className="w-4 h-4" />
      case 'cancelled': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<QualityRecord>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: QualityRecord) => (
        <div className="flex items-center gap-2">
          <Award className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      render: (item: QualityRecord) => (
        <Badge className={getTypeColor(item.type)}>
          {t[item.type as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: QualityRecord) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: QualityRecord) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {t[item.priority as keyof typeof t]}
        </span>
      )
    },
    {
      key: 'inspector',
      label: t.inspector,
      render: (item: QualityRecord) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.inspectorAr : item.inspector}
          </span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      render: (item: QualityRecord) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : item.department}
          </span>
        </div>
      )
    },
    {
      key: 'score',
      label: t.score,
      sortable: true,
      render: (item: QualityRecord) => (
        item.score > 0 ? (
          <div className="flex items-center gap-2">
            <span className={`font-medium ${getScoreColor(item.score)}`}>
              {item.score}%
            </span>
            <Star className="h-3 w-3 text-yellow-400" />
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'scheduledDate',
      label: t.scheduledDate,
      sortable: true,
      render: (item: QualityRecord) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.scheduledDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.planned, value: 'planned' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.failed, value: 'failed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.inspection, value: 'inspection' },
        { label: t.audit, value: 'audit' },
        { label: t.review, value: 'review' },
        { label: t.incident, value: 'incident' },
        { label: t.improvement, value: 'improvement' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.inspection, value: 'inspection' },
        { label: t.audit, value: 'audit' },
        { label: t.review, value: 'review' },
        { label: t.incident, value: 'incident' },
        { label: t.improvement, value: 'improvement' }
      ]
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.planned, value: 'planned' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.failed, value: 'failed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'inspector',
      label: t.inspector,
      type: 'text',
      required: true
    },
    {
      name: 'inspectorAr',
      label: t.inspector + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'scheduledDate',
      label: t.scheduledDate,
      type: 'date',
      required: true
    },
    {
      name: 'completedDate',
      label: t.completedDate,
      type: 'date'
    },
    {
      name: 'score',
      label: t.score,
      type: 'number',
      min: 0,
      max: 100
    },
    {
      name: 'findings',
      label: t.findings,
      type: 'textarea'
    },
    {
      name: 'findingsAr',
      label: t.findings + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'recommendations',
      label: t.recommendations,
      type: 'textarea'
    },
    {
      name: 'recommendationsAr',
      label: t.recommendations + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'correctiveActions',
      label: t.correctiveActions,
      type: 'textarea'
    },
    {
      name: 'correctiveActionsAr',
      label: t.correctiveActions + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'dueDate',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text'
    },
    {
      name: 'assignedToAr',
      label: t.assignedTo + ' (عربي)',
      type: 'text'
    },
    {
      name: 'standard',
      label: t.standard,
      type: 'text'
    },
    {
      name: 'standardAr',
      label: t.standard + ' (عربي)',
      type: 'text'
    },
    {
      name: 'nonConformities',
      label: t.nonConformities,
      type: 'number',
      min: 0
    },
    {
      name: 'majorIssues',
      label: t.majorIssues,
      type: 'number',
      min: 0
    },
    {
      name: 'minorIssues',
      label: t.minorIssues,
      type: 'number',
      min: 0
    },
    {
      name: 'cost',
      label: t.cost,
      type: 'number',
      min: 0
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<QualityRecord>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.qualityManagement}
        data={qualityRecords}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addRecord : modalMode === 'edit' ? t.editRecord : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
