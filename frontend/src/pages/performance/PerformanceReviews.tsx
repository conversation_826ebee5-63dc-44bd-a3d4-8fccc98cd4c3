/**
 * Performance Reviews Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Target,
  Eye,
  Edit,
  Trash2,
  Star,
  TrendingUp,
  Calendar,
  User,
  FileText,
  Award
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { performanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PerformanceReviewsProps {
  language: 'ar' | 'en'
}

interface PerformanceReview {
  id: number
  employee: string
  employeeAr: string
  employeeId: string
  reviewer: string
  reviewerAr: string
  reviewPeriod: string
  reviewType: 'annual' | 'quarterly' | 'probation' | 'project'
  status: 'draft' | 'in-progress' | 'completed' | 'approved'
  overallRating: number
  goals: number
  competencies: number
  development: number
  reviewDate: string
  dueDate: string
  department: string
  departmentAr: string
  position: string
  positionAr: string
}

const translations = {
  ar: {
    performanceReviews: 'تقييمات الأداء',
    addReview: 'إضافة تقييم',
    editReview: 'تعديل التقييم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقييم؟',
    searchPlaceholder: 'البحث في تقييمات الأداء...',
    employee: 'الموظف',
    employeeId: 'رقم الموظف',
    reviewer: 'المقيم',
    reviewPeriod: 'فترة التقييم',
    reviewType: 'نوع التقييم',
    status: 'الحالة',
    overallRating: 'التقييم العام',
    goals: 'الأهداف',
    competencies: 'الكفاءات',
    development: 'التطوير',
    reviewDate: 'تاريخ التقييم',
    dueDate: 'تاريخ الاستحقاق',
    department: 'القسم',
    position: 'المنصب',
    draft: 'مسودة',
    'in-progress': 'قيد التنفيذ',
    completed: 'مكتمل',
    approved: 'معتمد',
    annual: 'سنوي',
    quarterly: 'ربع سنوي',
    probation: 'فترة تجريبية',
    project: 'مشروع'
  },
  en: {
    performanceReviews: 'Performance Reviews',
    addReview: 'Add Review',
    editReview: 'Edit Review',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this review?',
    searchPlaceholder: 'Search performance reviews...',
    employee: 'Employee',
    employeeId: 'Employee ID',
    reviewer: 'Reviewer',
    reviewPeriod: 'Review Period',
    reviewType: 'Review Type',
    status: 'Status',
    overallRating: 'Overall Rating',
    goals: 'Goals',
    competencies: 'Competencies',
    development: 'Development',
    reviewDate: 'Review Date',
    dueDate: 'Due Date',
    department: 'Department',
    position: 'Position',
    draft: 'Draft',
    'in-progress': 'In Progress',
    completed: 'Completed',
    approved: 'Approved',
    annual: 'Annual',
    quarterly: 'Quarterly',
    probation: 'Probation',
    project: 'Project'
  }
}

export default function PerformanceReviews({ language }: PerformanceReviewsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reviews,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PerformanceReview>({
    service: performanceService as any,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'in-progress': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'approved': return 'bg-purple-100 text-purple-800 border-purple-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'annual': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'quarterly': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'probation': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'project': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-400" />)
    }

    return stars
  }

  // Table columns configuration
  const columns: TableColumn<PerformanceReview>[] = [
    {
      key: 'employee',
      label: t.employee,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.employeeAr : item.employee}
            </div>
            <div className="text-sm text-white/60">
              {item.employeeId} • {language === 'ar' ? item.positionAr : item.position}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'reviewer',
      label: t.reviewer,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.reviewerAr : item.reviewer}
          </span>
        </div>
      )
    },
    {
      key: 'reviewPeriod',
      label: t.reviewPeriod,
      render: (item: PerformanceReview) => (
        <span className="text-white/80">{item.reviewPeriod}</span>
      )
    },
    {
      key: 'reviewType',
      label: t.reviewType,
      sortable: true,
      render: (item: PerformanceReview) => (
        <Badge className={getTypeColor(item.reviewType)}>
          {t[item.reviewType as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: PerformanceReview) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'overallRating',
      label: t.overallRating,
      sortable: true,
      render: (item: PerformanceReview) => (
        item.overallRating > 0 ? (
          <div className="flex items-center gap-2">
            <span className="text-white font-medium">{item.overallRating.toFixed(1)}</span>
            <div className="flex">{getRatingStars(item.overallRating)}</div>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'dueDate',
      label: t.dueDate,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-red-400" />
          <span className="text-white/80">{item.dueDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.approved, value: 'approved' }
      ]
    },
    {
      key: 'reviewType',
      label: t.reviewType,
      options: [
        { label: t.annual, value: 'annual' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.probation, value: 'probation' },
        { label: t.project, value: 'project' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee',
      label: t.employee,
      type: 'text',
      required: true
    },
    {
      name: 'employeeAr',
      label: t.employee + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employeeId',
      label: t.employeeId,
      type: 'text',
      required: true
    },
    {
      name: 'reviewer',
      label: t.reviewer,
      type: 'text',
      required: true
    },
    {
      name: 'reviewerAr',
      label: t.reviewer + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'reviewPeriod',
      label: t.reviewPeriod,
      type: 'text',
      required: true
    },
    {
      name: 'reviewType',
      label: t.reviewType,
      type: 'select',
      required: true,
      options: [
        { label: t.annual, value: 'annual' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.probation, value: 'probation' },
        { label: t.project, value: 'project' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.approved, value: 'approved' }
      ]
    },
    {
      name: 'overallRating',
      label: t.overallRating,
      type: 'number',
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'goals',
      label: t.goals,
      type: 'number',
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'competencies',
      label: t.competencies,
      type: 'number',
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'development',
      label: t.development,
      type: 'number',
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'reviewDate',
      label: t.reviewDate,
      type: 'date',
      required: true
    },
    {
      name: 'dueDate',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true
    },
    {
      name: 'positionAr',
      label: t.position + ' (عربي)',
      type: 'text',
      required: true
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PerformanceReview>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.performanceReviews}
        data={reviews}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReview : modalMode === 'edit' ? t.editReview : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
