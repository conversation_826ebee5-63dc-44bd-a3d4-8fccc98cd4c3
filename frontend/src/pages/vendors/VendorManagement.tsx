/**
 * Vendor Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Star,
  Phone,
  Mail,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  CheckCircle,
  XCircle,
  Globe,
  AlertTriangle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { vendorService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface VendorManagementProps {
  language: 'ar' | 'en'
}

interface Vendor {
  id: number
  name: string
  name_ar: string
  contact_person: string
  email: string
  phone: string
  address: string
  website: string
  tax_number: string
  payment_terms: string
  is_active: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    vendorManagement: 'إدارة الموردين',
    addVendor: 'إضافة مورد',
    editVendor: 'تعديل المورد',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المورد؟',
    searchPlaceholder: 'البحث في الموردين...',
    name: 'الاسم',
    category: 'الفئة',
    contactPerson: 'الشخص المسؤول',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    address: 'العنوان',
    city: 'المدينة',
    country: 'البلد',
    status: 'الحالة',
    rating: 'التقييم',
    totalContracts: 'إجمالي العقود',
    totalValue: 'إجمالي القيمة',
    lastOrderDate: 'تاريخ آخر طلب',
    registrationDate: 'تاريخ التسجيل',
    paymentTerms: 'شروط الدفع',
    deliveryTime: 'وقت التسليم',
    qualityScore: 'نتيجة الجودة',
    reliabilityScore: 'نتيجة الموثوقية',
    communicationScore: 'نتيجة التواصل',
    certifications: 'الشهادات',
    riskLevel: 'مستوى المخاطر',
    preferredVendor: 'مورد مفضل',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'معلق',
    suspended: 'موقوف',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    days: 'أيام'
  },
  en: {
    vendorManagement: 'Vendor Management',
    addVendor: 'Add Vendor',
    editVendor: 'Edit Vendor',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this vendor?',
    searchPlaceholder: 'Search vendors...',
    name: 'Name',
    category: 'Category',
    contactPerson: 'Contact Person',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    city: 'City',
    country: 'Country',
    status: 'Status',
    rating: 'Rating',
    totalContracts: 'Total Contracts',
    totalValue: 'Total Value',
    lastOrderDate: 'Last Order Date',
    registrationDate: 'Registration Date',
    paymentTerms: 'Payment Terms',
    deliveryTime: 'Delivery Time',
    qualityScore: 'Quality Score',
    reliabilityScore: 'Reliability Score',
    communicationScore: 'Communication Score',
    certifications: 'Certifications',
    riskLevel: 'Risk Level',
    preferredVendor: 'Preferred Vendor',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    days: 'Days'
  }
}

export default function VendorManagement({ language }: VendorManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: vendors,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Vendor>({
    service: vendorService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'suspended': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'inactive': return <AlertTriangle className="h-4 w-4 text-gray-400" />
      case 'pending': return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'suspended': return <AlertTriangle className="h-4 w-4 text-red-400" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<Vendor>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Vendor) => (
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            <Building className="h-5 w-5" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-white">
                {language === 'ar' ? item.name_ar || item.name : item.name}
              </span>
              {item.is_active && (
                <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                  {t.active}
                </Badge>
              )}
            </div>
            <div className="text-sm text-white/60">
              {item.tax_number || 'No Tax Number'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'contact_person',
      label: t.contactPerson,
      render: (item: Vendor) => (
        <div>
          <div className="text-white">
            {item.contact_person || 'No Contact Person'}
          </div>
          <div className="flex items-center gap-1 text-sm text-white/60">
            <Mail className="h-3 w-3" />
            <span>{item.email || 'No Email'}</span>
          </div>
          <div className="flex items-center gap-1 text-sm text-white/60">
            <Phone className="h-3 w-3" />
            <span>{item.phone || 'No Phone'}</span>
          </div>
        </div>
      )
    },
    {
      key: 'is_active',
      label: t.status,
      sortable: true,
      render: (item: Vendor) => (
        <div className="flex items-center gap-2">
          {item.is_active ? (
            <CheckCircle className="h-4 w-4 text-green-400" />
          ) : (
            <XCircle className="h-4 w-4 text-red-400" />
          )}
          <Badge className={item.is_active ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200'}>
            {item.is_active ? t.active : t.inactive}
          </Badge>
        </div>
      )
    },
    {
      key: 'payment_terms',
      label: t.paymentTerms,
      sortable: true,
      render: (item: Vendor) => (
        <div className="text-white/80">
          {item.payment_terms || 'Not specified'}
        </div>
      )
    },
    {
      key: 'website',
      label: 'Website',
      render: (item: Vendor) => (
        <div className="flex items-center gap-1">
          <Globe className="h-3 w-3 text-blue-400" />
          {item.website ? (
            <a href={item.website} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">
              {item.website}
            </a>
          ) : (
            <span className="text-white/60">No website</span>
          )}
        </div>
      )
    },
    {
      key: 'created_at',
      label: t.registrationDate,
      sortable: true,
      render: (item: Vendor) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{new Date(item.created_at).toLocaleDateString()}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' },
        { label: t.suspended, value: 'suspended' }
      ]
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'contactPerson',
      label: t.contactPerson,
      type: 'text',
      required: true
    },
    {
      name: 'contactPersonAr',
      label: t.contactPerson + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea',
      required: true
    },
    {
      name: 'addressAr',
      label: t.address + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'city',
      label: t.city,
      type: 'text',
      required: true
    },
    {
      name: 'cityAr',
      label: t.city + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'country',
      label: t.country,
      type: 'text',
      required: true
    },
    {
      name: 'countryAr',
      label: t.country + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' },
        { label: t.suspended, value: 'suspended' }
      ]
    },
    {
      name: 'rating',
      label: t.rating,
      type: 'number',
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'totalContracts',
      label: t.totalContracts,
      type: 'number',
      min: 0
    },
    {
      name: 'totalValue',
      label: t.totalValue,
      type: 'number',
      min: 0
    },
    {
      name: 'lastOrderDate',
      label: t.lastOrderDate,
      type: 'date'
    },
    {
      name: 'registrationDate',
      label: t.registrationDate,
      type: 'date'
    },
    {
      name: 'paymentTerms',
      label: t.paymentTerms,
      type: 'text'
    },
    {
      name: 'paymentTermsAr',
      label: t.paymentTerms + ' (عربي)',
      type: 'text'
    },
    {
      name: 'deliveryTime',
      label: t.deliveryTime + ' (' + t.days + ')',
      type: 'number',
      min: 1
    },
    {
      name: 'qualityScore',
      label: t.qualityScore,
      type: 'number',
      min: 1,
      max: 10,
      step: 0.1
    },
    {
      name: 'reliabilityScore',
      label: t.reliabilityScore,
      type: 'number',
      min: 1,
      max: 10,
      step: 0.1
    },
    {
      name: 'communicationScore',
      label: t.communicationScore,
      type: 'number',
      min: 1,
      max: 10,
      step: 0.1
    },
    {
      name: 'certifications',
      label: t.certifications,
      type: 'textarea',
      placeholder: 'Comma-separated list'
    },
    {
      name: 'certificationsAr',
      label: t.certifications + ' (عربي)',
      type: 'textarea',
      placeholder: 'قائمة مفصولة بفواصل'
    },
    {
      name: 'riskLevel',
      label: t.riskLevel,
      type: 'select',
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'preferredVendor',
      label: t.preferredVendor,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Vendor>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.vendorManagement}
        data={vendors}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addVendor : modalMode === 'edit' ? t.editVendor : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
