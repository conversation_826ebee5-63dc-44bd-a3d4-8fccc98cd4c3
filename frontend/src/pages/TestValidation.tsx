/**
 * Test Validation Page
 * Demonstrates all validation features and API integration
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertCircle, TestTube } from 'lucide-react'
import EmployeeForm from '@/components/forms/EmployeeForm'
import { enhancedAPI, loadingManager } from '@/services/enhancedAPI'
import { 
  validateEmail, 
  validatePhone, 
  validatePassword, 
  validateSalary,
  validateForm,
  employeeValidationSchema 
} from '@/utils/validation'

interface TestValidationProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    testValidation: 'اختبار التحقق من صحة البيانات',
    validationTests: 'اختبارات التحقق',
    apiTests: 'اختبارات API',
    formTests: 'اختبارات النموذج',
    runTests: 'تشغيل الاختبارات',
    testResults: 'نتائج الاختبارات',
    passed: 'نجح',
    failed: 'فشل',
    running: 'قيد التشغيل',
    emailValidation: 'التحقق من البريد الإلكتروني',
    phoneValidation: 'التحقق من رقم الهاتف',
    passwordValidation: 'التحقق من كلمة المرور',
    salaryValidation: 'التحقق من الراتب',
    formValidation: 'التحقق من النموذج',
    apiConnection: 'اتصال API',
    loadingStates: 'حالات التحميل',
    employeeForm: 'نموذج الموظف'
  },
  en: {
    testValidation: 'Validation Testing',
    validationTests: 'Validation Tests',
    apiTests: 'API Tests',
    formTests: 'Form Tests',
    runTests: 'Run Tests',
    testResults: 'Test Results',
    passed: 'Passed',
    failed: 'Failed',
    running: 'Running',
    emailValidation: 'Email Validation',
    phoneValidation: 'Phone Validation',
    passwordValidation: 'Password Validation',
    salaryValidation: 'Salary Validation',
    formValidation: 'Form Validation',
    apiConnection: 'API Connection',
    loadingStates: 'Loading States',
    employeeForm: 'Employee Form'
  }
}

interface TestResult {
  name: string
  status: 'passed' | 'failed' | 'running'
  message: string
}

export default function TestValidation({ language }: TestValidationProps) {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const t = translations[language]
  const isRTL = language === 'ar'

  // Subscribe to loading states
  React.useEffect(() => {
    const unsubscribe = loadingManager.subscribe(setLoadingStates)
    return () => unsubscribe()
  }, [])

  const runValidationTests = async () => {
    setIsRunning(true)
    const results: TestResult[] = []

    // Email validation tests
    try {
      const validEmail = validateEmail('<EMAIL>')
      const invalidEmail = validateEmail('invalid-email')
      
      results.push({
        name: t.emailValidation,
        status: (validEmail === null && invalidEmail !== null) ? 'passed' : 'failed',
        message: 'Valid email passes, invalid email fails'
      })
    } catch (_error) {
      results.push({
        name: t.emailValidation,
        status: 'failed',
        message: 'Error in email validation'
      })
    }

    // Phone validation tests
    try {
      const validPhone = validatePhone('+966501234567')
      const invalidPhone = validatePhone('invalid-phone')
      
      results.push({
        name: t.phoneValidation,
        status: (validPhone === null && invalidPhone !== null) ? 'passed' : 'failed',
        message: 'Valid phone passes, invalid phone fails'
      })
    } catch (_error) {
      results.push({
        name: t.phoneValidation,
        status: 'failed',
        message: 'Error in phone validation'
      })
    }

    // Password validation tests
    try {
      const validPassword = validatePassword('StrongPass123')
      const invalidPassword = validatePassword('weak')
      
      results.push({
        name: t.passwordValidation,
        status: (validPassword === null && invalidPassword !== null) ? 'passed' : 'failed',
        message: 'Strong password passes, weak password fails'
      })
    } catch (error) {
      results.push({
        name: t.passwordValidation,
        status: 'failed',
        message: 'Error in password validation'
      })
    }

    // Salary validation tests
    try {
      const validSalary = validateSalary(5000)
      const invalidSalary = validateSalary(500)
      
      results.push({
        name: t.salaryValidation,
        status: (validSalary === null && invalidSalary !== null) ? 'passed' : 'failed',
        message: 'Valid salary passes, low salary fails'
      })
    } catch (error) {
      results.push({
        name: t.salaryValidation,
        status: 'failed',
        message: 'Error in salary validation'
      })
    }

    // Form validation tests
    try {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+966501234567',
        salary: 5000,
        hireDate: '2023-01-01',
        departmentId: '1'
      }
      
      const invalidData = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        phone: 'invalid-phone',
        salary: 500,
        hireDate: '',
        departmentId: ''
      }
      
      const validErrors = validateForm(validData, employeeValidationSchema)
      const invalidErrors = validateForm(invalidData, employeeValidationSchema)
      
      results.push({
        name: t.formValidation,
        status: (Object.keys(validErrors).length === 0 && Object.keys(invalidErrors).length > 0) ? 'passed' : 'failed',
        message: `Valid data: ${Object.keys(validErrors).length} errors, Invalid data: ${Object.keys(invalidErrors).length} errors`
      })
    } catch (_error) {
      results.push({
        name: t.formValidation,
        status: 'failed',
        message: 'Error in form validation'
      })
    }

    // API connection test
    try {
      // Test if API client is properly configured
      const apiTest = enhancedAPI.getList('employees', {}, language)
      results.push({
        name: t.apiConnection,
        status: 'passed',
        message: 'API client is properly configured'
      })
    } catch (_error) {
      results.push({
        name: t.apiConnection,
        status: 'failed',
        message: 'API client configuration error'
      })
    }

    // Loading states test
    try {
      const hasLoadingManager = typeof loadingManager.setLoading === 'function'
      results.push({
        name: t.loadingStates,
        status: hasLoadingManager ? 'passed' : 'failed',
        message: hasLoadingManager ? 'Loading manager is working' : 'Loading manager not found'
      })
    } catch (_error) {
      results.push({
        name: t.loadingStates,
        status: 'failed',
        message: 'Error testing loading states'
      })
    }

    setTestResults(results)
    setIsRunning(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-400" />
      case 'running':
        return <AlertCircle className="h-4 w-4 text-yellow-400" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'running':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">{t.testValidation}</h1>
        <Button
          onClick={runValidationTests}
          disabled={isRunning}
          className="glass-button"
        >
          <TestTube className="h-4 w-4 mr-2" />
          {isRunning ? t.running : t.runTests}
        </Button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">{t.testResults}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <span className="text-white font-medium">{result.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-white/70 text-sm">{result.message}</span>
                    <Badge className={getStatusColor(result.status)}>
                      {t[result.status as keyof typeof t]}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading States Display */}
      {Object.keys(loadingStates).length > 0 && (
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white">{t.loadingStates}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(loadingStates).map(([key, loading]) => (
                <div key={key} className="flex items-center justify-between p-2 rounded bg-white/5">
                  <span className="text-white/80">{key}</span>
                  <Badge className={loading ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}>
                    {loading ? 'Loading' : 'Idle'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employee Form Test */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            {t.employeeForm}
            <Button
              onClick={() => setShowForm(!showForm)}
              variant="outline"
              className="glass-button"
            >
              {showForm ? 'Hide' : 'Show'} Form
            </Button>
          </CardTitle>
        </CardHeader>
        {showForm && (
          <CardContent>
            <EmployeeForm
              language={language}
              onSuccess={(data) => {
                console.log('Form submitted successfully:', data)
                setShowForm(false)
              }}
              onCancel={() => setShowForm(false)}
            />
          </CardContent>
        )}
      </Card>
    </div>
  )
}
