/**
 * Departments Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Users,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Eye,
  Edit,
  Trash2,
  User<PERSON><PERSON><PERSON>,
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { departmentService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface DepartmentsProps {
  language: 'ar' | 'en'
}

interface Department {
  id: string
  name: string
  name_ar?: string
  description: string
  description_ar?: string
  manager?: number
  manager_name?: string
  manager_name_ar?: string
  employee_count: number
  budget?: number
  location?: string
  phone?: string
  email?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    departments: 'الأقسام',
    addDepartment: 'إضافة قسم',
    editDepartment: 'تعديل القسم',
    searchDepartments: 'البحث في الأقسام',
    name: 'اسم القسم',
    nameAr: 'الاسم بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    manager: 'المدير',
    managerAr: 'المدير بالعربية',
    employeeCount: 'عدد الموظفين',
    budget: 'الميزانية',
    location: 'الموقع',
    phone: 'الهاتف',
    email: 'البريد الإلكتروني',
    isActive: 'نشط',
    createdAt: 'تاريخ الإنشاء',
    actions: 'الإجراءات',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    active: 'نشط',
    inactive: 'غير نشط',
    searchPlaceholder: 'البحث بالاسم أو الوصف...',
    noDepartments: 'لا يوجد أقسام',
    loading: 'جاري التحميل...',
    manageDepartments: 'إدارة الأقسام والهيكل التنظيمي',
    createSuccess: 'تم إنشاء القسم بنجاح',
    updateSuccess: 'تم تحديث القسم بنجاح',
    deleteSuccess: 'تم حذف القسم بنجاح',
    confirmDelete: 'هل أنت متأكد من حذف هذا القسم؟'
  },
  en: {
    departments: 'Departments',
    addDepartment: 'Add Department',
    editDepartment: 'Edit Department',
    searchDepartments: 'Search Departments',
    name: 'Department Name',
    nameAr: 'Name (Arabic)',
    description: 'Description',
    descriptionAr: 'Description (Arabic)',
    manager: 'Manager',
    managerAr: 'Manager (Arabic)',
    employeeCount: 'Employee Count',
    budget: 'Budget',
    location: 'Location',
    phone: 'Phone',
    email: 'Email',
    isActive: 'Active',
    createdAt: 'Created Date',
    actions: 'Actions',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    active: 'Active',
    inactive: 'Inactive',
    searchPlaceholder: 'Search by name or description...',
    noDepartments: 'No departments found',
    loading: 'Loading...',
    manageDepartments: 'Manage departments and organizational structure',
    createSuccess: 'Department created successfully',
    updateSuccess: 'Department updated successfully',
    deleteSuccess: 'Department deleted successfully',
    confirmDelete: 'Are you sure you want to delete this department?'
  }
}

export default function Departments({ language }: DepartmentsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: departments,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Department>({
    service: departmentService,
    autoLoad: true,
    pageSize: 20
  })



  // Table columns configuration
  const columns: TableColumn<Department>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Department) => {
        if (!item) return <span className="text-white/40">-</span>

        return (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <Building className="h-5 w-5 text-white" />
            </div>
            <div>
              <div className="font-medium text-white">
                {language === 'ar' && item.name_ar ? item.name_ar : item.name}
              </div>
              <div className="text-sm text-white/60">
                {language === 'ar' && item.description_ar ? item.description_ar : item.description}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'manager',
      label: t.manager,
      sortable: true,
      render: (item: Department) => {
        if (!item) return <span className="text-white/40">-</span>

        const managerName = language === 'ar' && item.manager_name_ar && item.manager_name_ar.trim()
          ? item.manager_name_ar.trim()
          : item.manager_name

        return (
          <div className="text-white/90">
            {managerName || '-'}
          </div>
        )
      }
    },
    {
      key: 'employee_count',
      label: t.employeeCount,
      sortable: true,
      align: 'center' as const,
      render: (item: Department) => {
        if (!item) return <span className="text-white/40">-</span>

        return (
          <div className="flex items-center justify-center gap-2">
            <Users className="h-4 w-4 text-white/60" />
            <span className="text-white font-medium">{item.employee_count || 0}</span>
          </div>
        )
      }
    },
    {
      key: 'location',
      label: t.location,
      render: (item: Department) => {
        if (!item) return <span className="text-white/40">-</span>

        return (
          <div className="flex items-center gap-2 text-white/90">
            <MapPin className="h-4 w-4 text-white/60" />
            <span>{item.location || '-'}</span>
          </div>
        )
      }
    },
    {
      key: 'is_active',
      label: t.isActive,
      align: 'center' as const,
      render: (item: Department) => {
        if (!item) return <span className="text-white/40">-</span>

        return (
          <Badge variant={item.is_active ? 'default' : 'secondary'} className={item.is_active ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}>
            {item.is_active ? t.active : t.inactive}
          </Badge>
        )
      }
    },
    {
      key: 'created_at',
      label: t.createdAt,
      sortable: true,
      render: (item: Department) => {
        if (!item || !item.created_at) return <span className="text-white/40">-</span>

        return (
          <div className="flex items-center gap-2 text-white/90">
            <Calendar className="h-4 w-4 text-white/60" />
            <span className="text-sm">
              {new Date(item.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </span>
          </div>
        )
      }
    }
  ]

  // Table actions configuration
  const actions: TableAction<Department>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Department) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Department) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Department) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'is_active',
      label: t.isActive,
      options: [
        { value: 'true', label: t.active },
        { value: 'false', label: t.inactive }
      ]
    } as any
  ]

  // Form fields for modal
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true,
      placeholder: 'Enter department name'
    },
    {
      name: 'name_ar',
      label: t.nameAr,
      type: 'text',
      placeholder: 'أدخل اسم القسم بالعربية'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true,
      placeholder: 'Enter department description',
      rows: 3
    },
    {
      name: 'description_ar',
      label: t.descriptionAr,
      type: 'textarea',
      placeholder: 'أدخل وصف القسم بالعربية',
      rows: 3
    },
    {
      name: 'manager',
      label: t.manager,
      type: 'text',
      placeholder: 'Enter manager name'
    },
    {
      name: 'manager_ar',
      label: t.managerAr,
      type: 'text',
      placeholder: 'أدخل اسم المدير بالعربية'
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      placeholder: 'Enter department location'
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'text',
      placeholder: 'Enter phone number'
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      placeholder: 'Enter email address'
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      placeholder: 'Enter budget amount',
      min: 0
    },
    {
      name: 'is_active',
      label: t.isActive,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Department>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
      selectItem(null)
    } catch (error) {
      // Error is handled by the CRUD hook
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' = 'csv') => {
    try {
      const blob = await exportData(format)
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `departments.${format === 'excel' ? 'xlsx' : 'csv'}`
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* CRUD Table */}
      <CrudTable
        title={t.departments}
        data={departments}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addDepartment : modalMode === 'edit' ? t.editDepartment : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
