/**
 * Workflow Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Workflow,
  Eye,
  Edit,
  Trash2,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  User,
  Calendar,
  Settings,
  Zap
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { workflowService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface WorkflowManagementProps {
  language: 'ar' | 'en'
}

interface WorkflowItem {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  category: string
  category_ar: string
  status: 'active' | 'inactive' | 'draft' | 'archived'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  trigger_event: string
  conditions: string
  conditions_ar: string
  actions: string
  actions_ar: string
  is_automated: boolean
  next_run?: string
  last_run?: string
  run_count: number
  success_count: number
  created_by?: any
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    workflowManagement: 'إدارة سير العمل',
    addWorkflow: 'إضافة سير عمل',
    editWorkflow: 'تعديل سير العمل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سير العمل هذا؟',
    searchPlaceholder: 'البحث في سير العمل...',
    name: 'الاسم',
    description: 'الوصف',
    category: 'الفئة',
    status: 'الحالة',
    trigger: 'المحفز',
    priority: 'الأولوية',
    createdBy: 'أنشأ بواسطة',
    createdDate: 'تاريخ الإنشاء',
    lastModified: 'آخر تعديل',
    lastRun: 'آخر تشغيل',
    nextRun: 'التشغيل القادم',
    executionCount: 'عدد مرات التنفيذ',
    successRate: 'معدل النجاح',
    averageExecutionTime: 'متوسط وقت التنفيذ',
    assignedTo: 'مُكلف إلى',
    department: 'القسم',
    isAutomated: 'آلي',
    steps: 'الخطوات',
    conditions: 'الشروط',
    actions: 'الإجراءات',
    active: 'نشط',
    inactive: 'غير نشط',
    draft: 'مسودة',
    archived: 'مؤرشف',
    manual: 'يدوي',
    automatic: 'آلي',
    scheduled: 'مجدول',
    'event-based': 'مبني على الأحداث',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    categories: {
      approval: 'الموافقات',
      notification: 'الإشعارات',
      dataProcessing: 'معالجة البيانات',
      reporting: 'التقارير',
      integration: 'التكامل',
      maintenance: 'الصيانة'
    }
  },
  en: {
    workflowManagement: 'Workflow Management',
    addWorkflow: 'Add Workflow',
    editWorkflow: 'Edit Workflow',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this workflow?',
    searchPlaceholder: 'Search workflows...',
    name: 'Name',
    description: 'Description',
    category: 'Category',
    status: 'Status',
    trigger: 'Trigger',
    priority: 'Priority',
    createdBy: 'Created By',
    createdDate: 'Created Date',
    lastModified: 'Last Modified',
    lastRun: 'Last Run',
    nextRun: 'Next Run',
    executionCount: 'Execution Count',
    successRate: 'Success Rate',
    averageExecutionTime: 'Avg Execution Time',
    assignedTo: 'Assigned To',
    department: 'Department',
    isAutomated: 'Automated',
    steps: 'Steps',
    conditions: 'Conditions',
    actions: 'Actions',
    active: 'Active',
    inactive: 'Inactive',
    draft: 'Draft',
    archived: 'Archived',
    manual: 'Manual',
    automatic: 'Automatic',
    scheduled: 'Scheduled',
    'event-based': 'Event-based',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    categories: {
      approval: 'Approval',
      notification: 'Notification',
      dataProcessing: 'Data Processing',
      reporting: 'Reporting',
      integration: 'Integration',
      maintenance: 'Maintenance'
    }
  }
}

export default function WorkflowManagement({ language }: WorkflowManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: workflows,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<WorkflowItem>({
    service: workflowService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTriggerColor = (trigger: string) => {
    switch (trigger) {
      case 'manual': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'automatic': return 'bg-green-100 text-green-800 border-green-200'
      case 'scheduled': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'event-based': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="h-4 w-4 text-green-400" />
      case 'inactive': return <Pause className="h-4 w-4 text-red-400" />
      case 'draft': return <Clock className="h-4 w-4 text-yellow-400" />
      case 'archived': return <XCircle className="h-4 w-4 text-gray-400" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<WorkflowItem>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          <Workflow className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.name_ar || item.name : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.description_ar?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.is_automated && <Zap className="h-4 w-4 text-yellow-400" />}
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: WorkflowItem) => (
        <span className="text-white/80">
          {language === 'ar' ? item.category_ar || item.category : item.category}
        </span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {String(t[item.status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'trigger_event',
      label: t.trigger,
      sortable: true,
      render: (item: WorkflowItem) => (
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          {item.trigger_event || 'Manual'}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: WorkflowItem) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {String(t[item.priority as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'run_count',
      label: t.executionCount,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <Settings className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.run_count}</span>
        </div>
      )
    },
    {
      key: 'success_rate',
      label: t.successRate,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-2">
          <span className="text-white font-medium">
            {item.run_count > 0 ? Math.round((item.success_count / item.run_count) * 100) : 0}%
          </span>
          <div className={`w-2 h-2 rounded-full ${
            item.run_count > 0 && (item.success_count / item.run_count) * 100 >= 90 ? 'bg-green-400' :
            item.run_count > 0 && (item.success_count / item.run_count) * 100 >= 70 ? 'bg-yellow-400' : 'bg-red-400'
          }`}></div>
        </div>
      )
    },
    {
      key: 'last_run',
      label: t.lastRun,
      sortable: true,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {item.last_run ? new Date(item.last_run).toLocaleDateString() : 'Never'}
          </span>
        </div>
      )
    },
    {
      key: 'created_by',
      label: t.assignedTo,
      render: (item: WorkflowItem) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">
            {item.created_by?.user?.username || 'System'}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.draft, value: 'draft' },
        { label: t.archived, value: 'archived' }
      ]
    },
    {
      key: 'trigger',
      label: t.trigger,
      options: [
        { label: t.manual, value: 'manual' },
        { label: t.automatic, value: 'automatic' },
        { label: t.scheduled, value: 'scheduled' },
        { label: t['event-based'], value: 'event-based' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.approval, value: 'approval' },
        { label: t.categories.notification, value: 'notification' },
        { label: t.categories.dataProcessing, value: 'dataProcessing' },
        { label: t.categories.reporting, value: 'reporting' },
        { label: t.categories.integration, value: 'integration' },
        { label: t.categories.maintenance, value: 'maintenance' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.draft, value: 'draft' },
        { label: t.archived, value: 'archived' }
      ]
    },
    {
      name: 'trigger',
      label: t.trigger,
      type: 'select',
      required: true,
      options: [
        { label: t.manual, value: 'manual' },
        { label: t.automatic, value: 'automatic' },
        { label: t.scheduled, value: 'scheduled' },
        { label: t['event-based'], value: 'event-based' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'createdBy',
      label: t.createdBy,
      type: 'text',
      required: true
    },
    {
      name: 'createdByAr',
      label: t.createdBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text',
      required: true
    },
    {
      name: 'assignedToAr',
      label: t.assignedTo + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'steps',
      label: t.steps,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'conditions',
      label: t.conditions,
      type: 'textarea',
      placeholder: 'Define workflow conditions'
    },
    {
      name: 'conditionsAr',
      label: t.conditions + ' (عربي)',
      type: 'textarea',
      placeholder: 'تحديد شروط سير العمل'
    },
    {
      name: 'actions',
      label: t.actions,
      type: 'textarea',
      placeholder: 'Define workflow actions'
    },
    {
      name: 'actionsAr',
      label: t.actions + ' (عربي)',
      type: 'textarea',
      placeholder: 'تحديد إجراءات سير العمل'
    },
    {
      name: 'isAutomated',
      label: t.isAutomated,
      type: 'checkbox'
    },
    {
      name: 'nextRun',
      label: t.nextRun,
      type: 'datetime-local'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<WorkflowItem>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.workflowManagement}
        data={workflows}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addWorkflow : modalMode === 'edit' ? t.editWorkflow : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}