/**
 * Project Reports Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Clock,
  Target,
  Users,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  FileText,
  Eye,
  Edit,
  Trash2,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectReportService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProjectReportsProps {
  language: 'ar' | 'en'
}

interface ProjectReport {
  id: number
  name: string
  nameAr: string
  manager: string
  managerAr: string
  progress: number
  budget: number
  spent: number
  remaining: number
  status: 'completed' | 'inProgress' | 'planning' | 'onHold'
  teamMembers: number
  completedTasks: number
  totalTasks: number
  startDate: string
  endDate: string
  actualEndDate?: string
  estimatedHours: number
  actualHours: number
  riskLevel: 'low' | 'medium' | 'high'
  efficiency: number
  budgetUtilization: number
  taskCompletionRate: number
}

const translations = {
  ar: {
    projectReports: 'تقارير المشاريع',
    addReport: 'إضافة تقرير',
    editReport: 'تعديل التقرير',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقرير؟',
    searchPlaceholder: 'البحث في التقارير...',
    name: 'اسم المشروع',
    manager: 'مدير المشروع',
    progress: 'التقدم',
    budget: 'الميزانية',
    spent: 'المنفق',
    remaining: 'المتبقي',
    status: 'الحالة',
    teamMembers: 'أعضاء الفريق',
    completedTasks: 'المهام المكتملة',
    totalTasks: 'إجمالي المهام',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    actualEndDate: 'تاريخ الانتهاء الفعلي',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    efficiency: 'الكفاءة',
    riskLevel: 'مستوى المخاطر',
    budgetUtilization: 'استخدام الميزانية',
    taskCompletionRate: 'معدل إكمال المهام',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    planning: 'التخطيط',
    onHold: 'معلق',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي'
  },
  en: {
    projectReports: 'Project Reports',
    addReport: 'Add Report',
    editReport: 'Edit Report',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this report?',
    searchPlaceholder: 'Search reports...',
    name: 'Project Name',
    manager: 'Project Manager',
    progress: 'Progress',
    budget: 'Budget',
    spent: 'Spent',
    remaining: 'Remaining',
    status: 'Status',
    teamMembers: 'Team Members',
    completedTasks: 'Completed Tasks',
    totalTasks: 'Total Tasks',
    startDate: 'Start Date',
    endDate: 'End Date',
    actualEndDate: 'Actual End Date',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    efficiency: 'Efficiency',
    riskLevel: 'Risk Level',
    budgetUtilization: 'Budget Utilization',
    taskCompletionRate: 'Task Completion Rate',
    completed: 'Completed',
    inProgress: 'In Progress',
    planning: 'Planning',
    onHold: 'On Hold',
    low: 'Low',
    medium: 'Medium',
    high: 'High'
  }
}

export default function ProjectReports({ language }: ProjectReportsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reports,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<ProjectReport>({
    service: projectReportService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'planning':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'onHold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low':
        return <CheckCircle className="h-3 w-3" />
      case 'medium':
        return <Clock className="h-3 w-3" />
      case 'high':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Target className="h-3 w-3" />
    }
  }

  const calculateEfficiency = (estimated: number, actual: number) => {
    if (actual === 0) return 100
    return Math.round((estimated / actual) * 100)
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const isDelayed = (endDate: string, progress: number) => {
    const end = new Date(endDate)
    const today = new Date()
    const totalDuration = end.getTime() - new Date('2024-01-01').getTime()
    const elapsed = today.getTime() - new Date('2024-01-01').getTime()
    const expectedProgress = (elapsed / totalDuration) * 100
    return progress < expectedProgress && progress < 100
  }

  // Table columns configuration
  const columns: TableColumn<ProjectReport>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: ProjectReport) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <BarChart3 className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {item.teamMembers} أعضاء • {item.completedTasks}/{item.totalTasks} مهام
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'manager',
      label: t.manager,
      render: (item: ProjectReport) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.managerAr : item.manager}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: ProjectReport) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: ProjectReport) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(item.progress)}`}
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
          {isDelayed(item.endDate, item.progress) && (
            <AlertTriangle className="h-4 w-4 text-red-400" />
          )}
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: ProjectReport) => (
        <div>
          <div className="text-white text-sm">
            {formatCurrency(item.spent)} / {formatCurrency(item.budget)}
          </div>
          <div className="text-white/60 text-xs">
            {Math.round((item.spent / item.budget) * 100)}% مستخدم
          </div>
        </div>
      )
    },
    {
      key: 'efficiency',
      label: t.efficiency,
      sortable: true,
      render: (item: ProjectReport) => (
        <div>
          <div className="text-white text-sm">
            {calculateEfficiency(item.estimatedHours, item.actualHours)}%
          </div>
          <div className="text-white/60 text-xs">
            {item.actualHours}h / {item.estimatedHours}h
          </div>
        </div>
      )
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      sortable: true,
      render: (item: ProjectReport) => (
        <div className={`flex items-center gap-1 ${getRiskColor(item.riskLevel)}`}>
          {getRiskIcon(item.riskLevel)}
          <span className="font-medium">
            {t[item.riskLevel as keyof typeof t]}
          </span>
        </div>
      )
    },
    {
      key: 'endDate',
      label: t.endDate,
      sortable: true,
      render: (item: ProjectReport) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.endDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.completed, value: 'completed' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.planning, value: 'planning' },
        { label: t.onHold, value: 'onHold' }
      ]
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'manager',
      label: t.manager,
      type: 'text',
      required: true
    },
    {
      name: 'managerAr',
      label: t.manager + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'spent',
      label: t.spent,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.completed, value: 'completed' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.planning, value: 'planning' },
        { label: t.onHold, value: 'onHold' }
      ]
    },
    {
      name: 'teamMembers',
      label: t.teamMembers,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'completedTasks',
      label: t.completedTasks,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'totalTasks',
      label: t.totalTasks,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'actualEndDate',
      label: t.actualEndDate,
      type: 'date'
    },
    {
      name: 'estimatedHours',
      label: t.estimatedHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'actualHours',
      label: t.actualHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'riskLevel',
      label: t.riskLevel,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<ProjectReport>) => {
    try {
      // Calculate derived fields
      const processedData = {
        ...data,
        remaining: (data.budget || 0) - (data.spent || 0),
        efficiency: calculateEfficiency(data.estimatedHours || 0, data.actualHours || 0),
        budgetUtilization: data.budget ? Math.round(((data.spent || 0) / data.budget) * 100) : 0,
        taskCompletionRate: data.totalTasks ? Math.round(((data.completedTasks || 0) / data.totalTasks) * 100) : 0
      }

      if (modalMode === 'create') {
        await createItem(processedData)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, processedData)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.projectReports}
        data={reports}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReport : modalMode === 'edit' ? t.editReport : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
