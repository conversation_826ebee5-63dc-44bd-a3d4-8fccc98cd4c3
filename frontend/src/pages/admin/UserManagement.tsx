/**
 * User Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  Shield,
  Lock,
  Unlock,
  Mail,
  Phone,
  Calendar,
  Building,
  Crown,
  UserCheck,
  UserX,
  Settings,
  Clock
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { userManagementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface UserManagementProps {
  language: 'ar' | 'en'
}

interface User {
  id: number
  fullName: string
  fullNameAr: string
  email: string
  phone: string
  role: 'superAdmin' | 'admin' | 'hrManager' | 'financeManager' | 'salesManager' | 'departmentManager' | 'employee'
  roleAr: string
  department: string
  departmentAr: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  lastLogin: string
  joinDate: string
  position: string
  positionAr: string
  permissions: string[]
  profilePicture?: string
  address?: string
  addressAr?: string
  emergencyContact?: string
  emergencyContactAr?: string
}

const translations = {
  ar: {
    userManagement: 'إدارة المستخدمين',
    addUser: 'إضافة مستخدم',
    editUser: 'تعديل المستخدم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المستخدم؟',
    searchPlaceholder: 'البحث في المستخدمين...',
    fullName: 'الاسم الكامل',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    role: 'الدور',
    department: 'القسم',
    status: 'الحالة',
    lastLogin: 'آخر تسجيل دخول',
    joinDate: 'تاريخ الانضمام',
    position: 'المنصب',
    permissions: 'الصلاحيات',
    address: 'العنوان',
    emergencyContact: 'جهة الاتصال الطارئة',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'قيد المراجعة',
    suspended: 'موقوف',
    superAdmin: 'مدير عام',
    admin: 'مدير',
    hrManager: 'مدير الموارد البشرية',
    financeManager: 'مدير المالية',
    salesManager: 'مدير المبيعات',
    departmentManager: 'مدير القسم',
    employee: 'موظف',
    it: 'تقنية المعلومات',
    hr: 'الموارد البشرية',
    finance: 'المالية',
    marketing: 'التسويق',
    operations: 'العمليات',
    sales: 'المبيعات',
    resetPassword: 'إعادة تعيين كلمة المرور',
    managePermissions: 'إدارة الصلاحيات',
    activate: 'تفعيل',
    deactivate: 'إلغاء التفعيل'
  },
  en: {
    userManagement: 'User Management',
    addUser: 'Add User',
    editUser: 'Edit User',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this user?',
    searchPlaceholder: 'Search users...',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    role: 'Role',
    department: 'Department',
    status: 'Status',
    lastLogin: 'Last Login',
    joinDate: 'Join Date',
    position: 'Position',
    permissions: 'Permissions',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    suspended: 'Suspended',
    superAdmin: 'Super Admin',
    admin: 'Admin',
    hrManager: 'HR Manager',
    financeManager: 'Finance Manager',
    salesManager: 'Sales Manager',
    departmentManager: 'Department Manager',
    employee: 'Employee',
    it: 'IT',
    hr: 'HR',
    finance: 'Finance',
    marketing: 'Marketing',
    operations: 'Operations',
    sales: 'Sales',
    resetPassword: 'Reset Password',
    managePermissions: 'Manage Permissions',
    activate: 'Activate',
    deactivate: 'Deactivate'
  }
}

export default function UserManagement({ language }: UserManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: users,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<User>({
    service: userManagementService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-3 w-3" />
      case 'inactive':
        return <UserX className="h-3 w-3" />
      case 'pending':
        return <Clock className="h-3 w-3 animate-pulse" />
      case 'suspended':
        return <Lock className="h-3 w-3" />
      default:
        return <Users className="h-3 w-3" />
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return <Crown className="h-3 w-3" />
      case 'admin':
      case 'hrManager':
      case 'financeManager':
      case 'salesManager':
      case 'departmentManager':
        return <Shield className="h-3 w-3" />
      case 'employee':
        return <Users className="h-3 w-3" />
      default:
        return <Users className="h-3 w-3" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superAdmin':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'hrManager':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'financeManager':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'salesManager':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'departmentManager':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case 'employee':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<User>[] = [
    {
      key: 'fullName',
      label: t.fullName,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
            {((language === 'ar' ? item.fullNameAr : item.fullName) || '').charAt(0) || '?'}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.fullNameAr : item.fullName}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.positionAr : item.position}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          <Mail className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.email}</span>
        </div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.phone}</span>
        </div>
      )
    },
    {
      key: 'role',
      label: t.role,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          {getRoleIcon(item.role)}
          <Badge className={getRoleColor(item.role)}>
            {language === 'ar' ? item.roleAr : t[item.role as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : t[item.department as keyof typeof t]}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'lastLogin',
      label: t.lastLogin,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-yellow-400" />
          <span className="text-white/80">{item.lastLogin}</span>
        </div>
      )
    },
    {
      key: 'joinDate',
      label: t.joinDate,
      sortable: true,
      render: (item: User) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.joinDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<User>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: User) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: User) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.managePermissions,
      icon: Shield,
      onClick: (item: User) => {
        // Handle permissions management
        console.log('Manage permissions for:', item.fullName)
      },
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: t.resetPassword,
      icon: Lock,
      onClick: (item: User) => {
        // Handle password reset
        console.log('Reset password for:', item.fullName)
      },
      variant: 'ghost',
      className: 'text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/20'
    },
    {
      label: t.deactivate,
      icon: Unlock,
      onClick: async (item: User) => {
        await updateItem(item.id, { ...item, status: 'inactive' })
      },
      variant: 'ghost',
      className: 'text-orange-400 hover:text-orange-300 hover:bg-orange-500/20',
      show: (item: User) => item.status === 'active'
    },
    {
      label: t.activate,
      icon: Lock,
      onClick: async (item: User) => {
        await updateItem(item.id, { ...item, status: 'active' })
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20',
      show: (item: User) => item.status === 'inactive'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: User) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' },
        { label: t.suspended, value: 'suspended' }
      ]
    },
    {
      key: 'role',
      label: t.role,
      options: [
        { label: t.superAdmin, value: 'superAdmin' },
        { label: t.admin, value: 'admin' },
        { label: t.hrManager, value: 'hrManager' },
        { label: t.financeManager, value: 'financeManager' },
        { label: t.salesManager, value: 'salesManager' },
        { label: t.departmentManager, value: 'departmentManager' },
        { label: t.employee, value: 'employee' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: t.it, value: 'it' },
        { label: t.hr, value: 'hr' },
        { label: t.finance, value: 'finance' },
        { label: t.marketing, value: 'marketing' },
        { label: t.operations, value: 'operations' },
        { label: t.sales, value: 'sales' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'fullName',
      label: t.fullName,
      type: 'text',
      required: true
    },
    {
      name: 'fullNameAr',
      label: t.fullName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'role',
      label: t.role,
      type: 'select',
      required: true,
      options: [
        { label: t.superAdmin, value: 'superAdmin' },
        { label: t.admin, value: 'admin' },
        { label: t.hrManager, value: 'hrManager' },
        { label: t.financeManager, value: 'financeManager' },
        { label: t.salesManager, value: 'salesManager' },
        { label: t.departmentManager, value: 'departmentManager' },
        { label: t.employee, value: 'employee' }
      ]
    },
    {
      name: 'roleAr',
      label: t.role + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'select',
      required: true,
      options: [
        { label: t.it, value: 'it' },
        { label: t.hr, value: 'hr' },
        { label: t.finance, value: 'finance' },
        { label: t.marketing, value: 'marketing' },
        { label: t.operations, value: 'operations' },
        { label: t.sales, value: 'sales' }
      ]
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true
    },
    {
      name: 'positionAr',
      label: t.position + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' },
        { label: t.suspended, value: 'suspended' }
      ]
    },
    {
      name: 'joinDate',
      label: t.joinDate,
      type: 'date',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea'
    },
    {
      name: 'addressAr',
      label: t.address + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'emergencyContact',
      label: t.emergencyContact,
      type: 'text'
    },
    {
      name: 'emergencyContactAr',
      label: t.emergencyContact + ' (عربي)',
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<User>) => {
    try {
      if (modalMode === 'create') {
        await createItem({
          ...data,
          lastLogin: 'Never',
          permissions: []
        })
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.userManagement}
        data={users}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addUser : modalMode === 'edit' ? t.editUser : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
