/**
 * Reports Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Download,
  Calendar,
  Users,
  Building,
  DollarSign,
  FileText,
  Activity,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { reportService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ReportsProps {
  language: 'ar' | 'en'
}

interface Report {
  id: string
  name: string
  name_ar?: string
  type: string
  description: string
  description_ar?: string
  status: 'pending' | 'completed' | 'failed' | 'running'
  created_date: string
  completed_date?: string
  file_size?: string
  file_url?: string
  parameters: Record<string, unknown>
  created_by: number
  created_by_name?: string
}

const translations = {
  ar: {
    reports: 'التقارير',
    searchPlaceholder: 'البحث في التقارير...',
    addReport: 'إضافة تقرير',
    editReport: 'تعديل التقرير',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقرير؟',
    name: 'اسم التقرير',
    type: 'نوع التقرير',
    description: 'الوصف',
    status: 'الحالة',
    createdDate: 'تاريخ الإنشاء',
    completedDate: 'تاريخ الإكمال',
    fileSize: 'حجم الملف',
    createdBy: 'أنشأ بواسطة',
    parameters: 'المعاملات',
    pending: 'قيد الانتظار',
    completed: 'مكتمل',
    failed: 'فشل',
    running: 'قيد التشغيل',
    all: 'الكل',
    employee: 'الموظفين',
    department: 'الأقسام',
    financial: 'مالي',
    performance: 'الأداء',
    attendance: 'الحضور',
    payroll: 'الرواتب'
  },
  en: {
    reports: 'Reports',
    searchPlaceholder: 'Search reports...',
    addReport: 'Add Report',
    editReport: 'Edit Report',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this report?',
    name: 'Report Name',
    type: 'Report Type',
    description: 'Description',
    status: 'Status',
    createdDate: 'Created Date',
    completedDate: 'Completed Date',
    fileSize: 'File Size',
    createdBy: 'Created By',
    parameters: 'Parameters',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    running: 'Running',
    all: 'All',
    employee: 'Employee',
    department: 'Department',
    financial: 'Financial',
    performance: 'Performance',
    attendance: 'Attendance',
    payroll: 'Payroll'
  }
}

export default function Reports({ language }: ReportsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reports,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Report>({
    service: reportService,
    autoLoad: true,
    pageSize: 20
  })

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: Clock },
      completed: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      failed: { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: XCircle },
      running: { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: Activity }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon className="h-3 w-3 mr-1" />
        {t[status as keyof typeof t] || status}
      </Badge>
    )
  }

  // Table columns configuration
  const columns: TableColumn<Report>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Report) => (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-blue-400" />
          <div>
            <span className="font-medium text-white">{language === 'ar' ? item.name_ar : item.name}</span>
            <p className="text-sm text-white/60">{language === 'ar' ? item.description_ar : item.description}</p>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: Report) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[item.type as keyof typeof t] || item.type}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Report) => getStatusBadge(item.status)
    },
    {
      key: 'created_date',
      label: t.createdDate,
      sortable: true,
      render: (item: Report) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-400" />
          <span className="text-white">{new Date(item.created_date).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'file_size',
      label: t.fileSize,
      sortable: true,
      render: (item: Report) => (
        <span className="text-white">{item.file_size || '-'}</span>
      )
    },
    {
      key: 'created_by',
      label: t.createdBy,
      sortable: true,
      render: (item: Report) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-purple-400" />
          <span className="text-white">{item.created_by_name || 'Unknown'}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Download',
      icon: Download,
      onClick: (item: any) => {
        if (item.file_url && item.status === 'completed') {
          window.open(item.file_url, '_blank')
        }
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.all, value: '' },
        { label: t.pending, value: 'pending' },
        { label: t.completed, value: 'completed' },
        { label: t.failed, value: 'failed' },
        { label: t.running, value: 'running' }
      ]
    },
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.all, value: '' },
        { label: t.employee, value: 'employee' },
        { label: t.department, value: 'department' },
        { label: t.financial, value: 'financial' },
        { label: t.performance, value: 'performance' },
        { label: t.attendance, value: 'attendance' },
        { label: t.payroll, value: 'payroll' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: `${t.name} (عربي)`,
      type: 'text'
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.employee, value: 'employee' },
        { label: t.department, value: 'department' },
        { label: t.financial, value: 'financial' },
        { label: t.performance, value: 'performance' },
        { label: t.attendance, value: 'attendance' },
        { label: t.payroll, value: 'payroll' }
      ]
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'description_ar',
      label: `${t.description} (عربي)`,
      type: 'textarea'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.completed, value: 'completed' },
        { label: t.failed, value: 'failed' },
        { label: t.running, value: 'running' }
      ]
    },
    {
      name: 'parameters',
      label: t.parameters,
      type: 'textarea',
      placeholder: 'JSON format: {"key": "value"}'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Report>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.reports}
        data={reports}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReport : modalMode === 'edit' ? t.editReport : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}


