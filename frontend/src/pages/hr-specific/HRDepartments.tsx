/**
 * HR Departments Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Users,
  TrendingUp,
  Award,
  Eye,
  Edit,
  Trash2,
  DollarSign,
  User<PERSON>heck,
  Target,
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { hrDepartmentService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface HRDepartmentsProps {
  language: 'ar' | 'en'
}

interface HRDepartment {
  id: number
  name: string
  nameAr: string
  manager: string
  managerAr: string
  employeeCount: number
  avgPerformance: number
  budget: number
  vacancies: number
  avgSalary: number
  turnoverRate: number
  performanceRating: 'excellent' | 'good' | 'average' | 'needsImprovement'
  description: string
  descriptionAr: string
  location: string
  locationAr: string
  establishedDate: string
}

const translations = {
  ar: {
    hrDepartments: 'إدارة الأقسام - الموارد البشرية',
    addDepartment: 'إضافة قسم',
    editDepartment: 'تعديل القسم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا القسم؟',
    searchPlaceholder: 'البحث في الأقسام...',
    name: 'اسم القسم',
    manager: 'المدير',
    employeeCount: 'عدد الموظفين',
    avgPerformance: 'متوسط الأداء',
    budget: 'الميزانية',
    vacancies: 'الوظائف الشاغرة',
    avgSalary: 'متوسط الراتب',
    turnoverRate: 'معدل دوران الموظفين',
    performanceRating: 'تقييم الأداء',
    description: 'الوصف',
    location: 'الموقع',
    establishedDate: 'تاريخ التأسيس',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين'
  },
  en: {
    hrDepartments: 'HR Department Management',
    addDepartment: 'Add Department',
    editDepartment: 'Edit Department',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this department?',
    searchPlaceholder: 'Search departments...',
    name: 'Department Name',
    manager: 'Manager',
    employeeCount: 'Employee Count',
    avgPerformance: 'Avg Performance',
    budget: 'Budget',
    vacancies: 'Vacancies',
    avgSalary: 'Average Salary',
    turnoverRate: 'Turnover Rate',
    performanceRating: 'Performance Rating',
    description: 'Description',
    location: 'Location',
    establishedDate: 'Established Date',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement'
  }
}

export default function HRDepartments({ language }: HRDepartmentsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: departments,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<HRDepartment>({
    service: hrDepartmentService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getPerformanceColor = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'good':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'average':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'needsImprovement':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPerformanceIcon = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return <Award className="h-4 w-4" />
      case 'good':
        return <TrendingUp className="h-4 w-4" />
      case 'average':
        return <Target className="h-4 w-4" />
      case 'needsImprovement':
        return <BarChart3 className="h-4 w-4" />
      default:
        return <BarChart3 className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<HRDepartment>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: HRDepartment) => {
        const displayName = language === 'ar' ? (item.nameAr || item.name) : (item.name || item.nameAr)
        const firstLetter = displayName ? displayName.charAt(0).toUpperCase() : '?'

        return (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
              {firstLetter}
            </div>
            <div>
              <div className="font-medium text-white">
                {displayName || 'N/A'}
              </div>
              <div className="text-sm text-white/60">{item.employeeCount || 0} employees</div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'manager',
      label: t.manager,
      render: (item: HRDepartment) => {
        const managerName = language === 'ar' ? (item.managerAr || item.manager) : (item.manager || item.managerAr)

        return (
          <div className="flex items-center gap-1">
            <UserCheck className="h-3 w-3 text-blue-400" />
            <span className="text-white/80">
              {managerName || 'N/A'}
            </span>
          </div>
        )
      }
    },
    {
      key: 'employeeCount',
      label: t.employeeCount,
      sortable: true,
      render: (item: HRDepartment) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{item.employeeCount || 0}</span>
        </div>
      )
    },
    {
      key: 'avgPerformance',
      label: t.avgPerformance,
      sortable: true,
      render: (item: HRDepartment) => (
        <div className="flex items-center gap-2">
          {getPerformanceIcon(item.performanceRating || 'average')}
          <span className="text-white font-medium">{item.avgPerformance || 0}/5.0</span>
          <Badge className={getPerformanceColor(item.performanceRating || 'average')}>
            {t[(item.performanceRating || 'average') as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: HRDepartment) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{formatCurrency(item.budget || 0)}</span>
        </div>
      )
    },
    {
      key: 'avgSalary',
      label: t.avgSalary,
      sortable: true,
      render: (item: HRDepartment) => (
        <span className="text-white font-medium">{formatCurrency(item.avgSalary || 0)}</span>
      )
    },
    {
      key: 'vacancies',
      label: t.vacancies,
      sortable: true,
      render: (item: HRDepartment) => (
        <Badge className={(item.vacancies || 0) > 0 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
          {item.vacancies || 0} open
        </Badge>
      )
    },
    {
      key: 'turnoverRate',
      label: t.turnoverRate,
      sortable: true,
      render: (item: HRDepartment) => {
        const turnoverRate = item.turnoverRate || 0
        return (
          <span className={`font-medium ${
            turnoverRate > 15 ? 'text-red-400' :
            turnoverRate > 10 ? 'text-yellow-400' : 'text-green-400'
          }`}>
            {turnoverRate}%
          </span>
        )
      }
    }
  ]

  // Table actions configuration
  const actions: TableAction<HRDepartment>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: HRDepartment) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: HRDepartment) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: HRDepartment) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'performanceRating',
      label: t.performanceRating,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.needsImprovement, value: 'needsImprovement' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'manager',
      label: t.manager,
      type: 'text',
      required: true
    },
    {
      name: 'managerAr',
      label: t.manager + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employeeCount',
      label: t.employeeCount,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'avgPerformance',
      label: t.avgPerformance,
      type: 'number',
      required: true,
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'vacancies',
      label: t.vacancies,
      type: 'number',
      min: 0
    },
    {
      name: 'avgSalary',
      label: t.avgSalary,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'turnoverRate',
      label: t.turnoverRate,
      type: 'number',
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      name: 'performanceRating',
      label: t.performanceRating,
      type: 'select',
      required: true,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.needsImprovement, value: 'needsImprovement' }
      ]
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea'
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'location',
      label: t.location,
      type: 'text'
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text'
    },
    {
      name: 'establishedDate',
      label: t.establishedDate,
      type: 'date'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<HRDepartment>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.hrDepartments}
        data={departments}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addDepartment : modalMode === 'edit' ? t.editDepartment : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
