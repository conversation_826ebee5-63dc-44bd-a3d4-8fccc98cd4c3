/**
 * HR Customers Page with Full CRUD Implementation
 * HR-specific view of customers focusing on contracts and compliance
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserCheck,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle,
  Building,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface HRCustomersProps {
  language: 'ar' | 'en'
}

interface HRCustomer {
  id: string
  name: string
  name_ar: string
  company: string
  company_ar: string
  contract_type: string
  employee_count: number
  contract_start: string
  contract_end: string
  hr_contact: string
  hr_contact_ar: string
  compliance_status: 'compliant' | 'non-compliant' | 'pending'
  email: string
  phone: string
  notes: string
}

const translations = {
  ar: {
    customers: 'العملاء - HR',
    searchPlaceholder: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    editCustomer: 'تعديل العميل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل؟',
    name: 'اسم العميل',
    company: 'الشركة',
    contractType: 'نوع العقد',
    employeeCount: 'عدد الموظفين',
    contractStart: 'بداية العقد',
    contractEnd: 'نهاية العقد',
    hrContact: 'جهة الاتصال HR',
    complianceStatus: 'حالة الامتثال',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    notes: 'ملاحظات',
    compliant: 'ملتزم',
    nonCompliant: 'غير ملتزم',
    pending: 'معلق',
    all: 'الكل',
    staffing: 'توظيف',
    consulting: 'استشارات',
    outsourcing: 'استعانة خارجية',
    maintenance: 'صيانة'
  },
  en: {
    customers: 'Customers - HR',
    searchPlaceholder: 'Search customers...',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this customer?',
    name: 'Customer Name',
    company: 'Company',
    contractType: 'Contract Type',
    employeeCount: 'Employee Count',
    contractStart: 'Contract Start',
    contractEnd: 'Contract End',
    hrContact: 'HR Contact',
    complianceStatus: 'Compliance Status',
    email: 'Email',
    phone: 'Phone',
    notes: 'Notes',
    compliant: 'Compliant',
    nonCompliant: 'Non-Compliant',
    pending: 'Pending',
    all: 'All',
    staffing: 'Staffing',
    consulting: 'Consulting',
    outsourcing: 'Outsourcing',
    maintenance: 'Maintenance'
  }
}

export default function HRCustomers({ language }: HRCustomersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<HRCustomer>({
    service: customerService,
    autoLoad: true,
    pageSize: 20
  })

  // Compliance status badge helper
  const getComplianceBadge = (status: string) => {
    const statusConfig = {
      compliant: { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: CheckCircle },
      'non-compliant': { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: AlertTriangle },
      pending: { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: AlertTriangle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon className="h-3 w-3 mr-1" />
        {t[status as keyof typeof t] || status}
      </Badge>
    )
  }

  // Table columns configuration
  const columns: TableColumn<HRCustomer>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: HRCustomer) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <div>
            <span className="font-medium text-white">{language === 'ar' ? item.name_ar : item.name}</span>
            <p className="text-sm text-white/60">{language === 'ar' ? item.company_ar : item.company}</p>
          </div>
        </div>
      )
    },
    {
      key: 'contract_type',
      label: t.contractType,
      sortable: true,
      render: (item: HRCustomer) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[item.contract_type as keyof typeof t] || item.contract_type}
        </Badge>
      )
    },
    {
      key: 'employee_count',
      label: t.employeeCount,
      sortable: true,
      render: (item: HRCustomer) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-purple-400" />
          <span className="text-white">{item.employee_count}</span>
        </div>
      )
    },
    {
      key: 'contract_start',
      label: t.contractStart,
      sortable: true,
      render: (item: HRCustomer) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-blue-400" />
          <span className="text-white">{new Date(item.contract_start).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'compliance_status',
      label: t.complianceStatus,
      sortable: true,
      render: (item: HRCustomer) => getComplianceBadge(item.compliance_status)
    },
    {
      key: 'hr_contact',
      label: t.hrContact,
      sortable: true,
      render: (item: HRCustomer) => (
        <div className="flex items-center gap-2">
          <UserCheck className="h-4 w-4 text-green-400" />
          <span className="text-white">{language === 'ar' ? item.hr_contact_ar : item.hr_contact}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<HRCustomer>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: HRCustomer) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: HRCustomer) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: HRCustomer) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'compliance_status',
      label: t.complianceStatus,
      options: [
        { label: t.all, value: '' },
        { label: t.compliant, value: 'compliant' },
        { label: t.nonCompliant, value: 'non-compliant' },
        { label: t.pending, value: 'pending' }
      ]
    },
    {
      key: 'contract_type',
      label: t.contractType,
      options: [
        { label: t.all, value: '' },
        { label: t.staffing, value: 'staffing' },
        { label: t.consulting, value: 'consulting' },
        { label: t.outsourcing, value: 'outsourcing' },
        { label: t.maintenance, value: 'maintenance' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: `${t.name} (عربي)`,
      type: 'text'
    },
    {
      name: 'company',
      label: t.company,
      type: 'text',
      required: true
    },
    {
      name: 'company_ar',
      label: `${t.company} (عربي)`,
      type: 'text'
    },
    {
      name: 'contract_type',
      label: t.contractType,
      type: 'select',
      required: true,
      options: [
        { label: t.staffing, value: 'staffing' },
        { label: t.consulting, value: 'consulting' },
        { label: t.outsourcing, value: 'outsourcing' },
        { label: t.maintenance, value: 'maintenance' }
      ]
    },
    {
      name: 'employee_count',
      label: t.employeeCount,
      type: 'number',
      min: 0,
      required: true
    },
    {
      name: 'contract_start',
      label: t.contractStart,
      type: 'date',
      required: true
    },
    {
      name: 'contract_end',
      label: t.contractEnd,
      type: 'date',
      required: true
    },
    {
      name: 'hr_contact',
      label: t.hrContact,
      type: 'text',
      required: true
    },
    {
      name: 'hr_contact_ar',
      label: `${t.hrContact} (عربي)`,
      type: 'text'
    },
    {
      name: 'compliance_status',
      label: t.complianceStatus,
      type: 'select',
      required: true,
      options: [
        { label: t.compliant, value: 'compliant' },
        { label: t.nonCompliant, value: 'non-compliant' },
        { label: t.pending, value: 'pending' }
      ]
    },
    {
      name: 'email',
      label: t.email,
      type: 'email'
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<HRCustomer>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.customers}
        data={customers}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}


