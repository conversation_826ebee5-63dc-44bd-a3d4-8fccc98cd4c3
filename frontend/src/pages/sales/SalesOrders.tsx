/**
 * Sales Orders Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '../../components/ui/badge'
import {
  ShoppingCart,
  Calendar,
  DollarSign,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  User,
  Package
} from 'lucide-react'
import { useCrud } from '../../hooks/useCrud'
import { salesOrderService } from '../../services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '../../components/common/CrudTable'
import CrudModal, { FormField } from '../../components/common/CrudModal'

interface SalesOrdersProps {
  language: 'ar' | 'en'
}

interface SalesOrder {
  id: number
  order_number: string
  customer: number
  customer_id: number
  customer_name?: string
  total_amount: number
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  order_date: string
  delivery_date?: string
  items_count: number
  discount: number
  tax: number
  notes?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    salesOrders: 'أوامر المبيعات',
    addOrder: 'إضافة أمر',
    editOrder: 'تعديل الأمر',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الأمر؟',
    searchPlaceholder: 'البحث في أوامر المبيعات...',
    orderNumber: 'رقم الأمر',
    customer: 'العميل',
    totalAmount: 'إجمالي المبلغ',
    status: 'الحالة',
    priority: 'الأولوية',
    orderDate: 'تاريخ الأمر',
    deliveryDate: 'تاريخ التسليم',
    itemsCount: 'عدد العناصر',
    discount: 'الخصم',
    tax: 'الضريبة',
    notes: 'الملاحظات',
    pending: 'معلق',
    confirmed: 'مؤكد',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'ملغي',
    priorities: {
      low: 'منخفض',
      medium: 'متوسط',
      high: 'عالي',
      urgent: 'عاجل'
    }
  },
  en: {
    salesOrders: 'Sales Orders',
    addOrder: 'Add Order',
    editOrder: 'Edit Order',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this order?',
    searchPlaceholder: 'Search sales orders...',
    orderNumber: 'Order Number',
    customer: 'Customer',
    totalAmount: 'Total Amount',
    status: 'Status',
    priority: 'Priority',
    orderDate: 'Order Date',
    deliveryDate: 'Delivery Date',
    itemsCount: 'Items Count',
    discount: 'Discount',
    tax: 'Tax',
    notes: 'Notes',
    pending: 'Pending',
    confirmed: 'Confirmed',
    processing: 'Processing',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    priorities: {
      low: 'Low',
      medium: 'Medium',
      high: 'High',
      urgent: 'Urgent'
    }
  }
}

export default function SalesOrders({ language }: SalesOrdersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: salesOrders,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<SalesOrder>({
    service: salesOrderService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'processing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'shipped':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<SalesOrder>[] = [
    {
      key: 'order_number',
      label: t.orderNumber,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <ShoppingCart className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.order_number}</span>
        </div>
      )
    },
    {
      key: 'customer_name',
      label: t.customer,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-purple-400" />
          <span className="text-white">{item.customer_name || 'Unknown Customer'}</span>
        </div>
      )
    },
    {
      key: 'total_amount',
      label: t.totalAmount,
      sortable: true,
      render: (item: SalesOrder) => (
        <span className="text-white font-medium">{formatCurrency(item.total_amount)}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: SalesOrder) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: SalesOrder) => (
        <Badge className={getPriorityColor(item.priority)}>
          {t.priorities[item.priority as keyof typeof t.priorities]}
        </Badge>
      )
    },
    {
      key: 'order_date',
      label: t.orderDate,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.order_date}</span>
        </div>
      )
    },
    {
      key: 'items_count',
      label: t.itemsCount,
      sortable: true,
      render: (item: SalesOrder) => (
        <div className="flex items-center gap-1">
          <Package className="h-3 w-3 text-green-400" />
          <span className="text-white">{item.items_count}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.confirmed, value: 'confirmed' },
        { label: t.processing, value: 'processing' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.priorities.low, value: 'low' },
        { label: t.priorities.medium, value: 'medium' },
        { label: t.priorities.high, value: 'high' },
        { label: t.priorities.urgent, value: 'urgent' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'order_number',
      label: t.orderNumber,
      type: 'text',
      required: true
    },
    {
      name: 'customer',
      label: t.customer,
      type: 'select',
      required: true,
      options: [
        { label: 'John Doe', value: '1' },
        { label: 'Jane Smith', value: '2' },
        { label: 'Company ABC', value: '3' }
      ]
    },
    {
      name: 'total_amount',
      label: t.totalAmount,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.confirmed, value: 'confirmed' },
        { label: t.processing, value: 'processing' },
        { label: t.shipped, value: 'shipped' },
        { label: t.delivered, value: 'delivered' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.priorities.low, value: 'low' },
        { label: t.priorities.medium, value: 'medium' },
        { label: t.priorities.high, value: 'high' },
        { label: t.priorities.urgent, value: 'urgent' }
      ]
    },
    {
      name: 'order_date',
      label: t.orderDate,
      type: 'date',
      required: true
    },
    {
      name: 'delivery_date',
      label: t.deliveryDate,
      type: 'date'
    },
    {
      name: 'items_count',
      label: t.itemsCount,
      type: 'number',
      min: 1
    },
    {
      name: 'discount',
      label: t.discount,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'tax',
      label: t.tax,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<SalesOrder>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.salesOrders}
        data={salesOrders}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addOrder : modalMode === 'edit' ? t.editOrder : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
