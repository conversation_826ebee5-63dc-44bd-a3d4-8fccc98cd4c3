/**
 * Department Projects Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Briefcase,
  Users,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  CheckSquare,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit,
  Trash2,
  User,
  Bar<PERSON><PERSON>3,
  DollarSign
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { departmentProjectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface DepartmentProjectsProps {
  language: 'ar' | 'en'
}

interface DepartmentProject {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  startDate: string
  endDate: string
  progress: number
  status: 'active' | 'completed' | 'onHold' | 'planning'
  priority: 'high' | 'medium' | 'low'
  teamSize: number
  budget: number
  assignee: string
  assigneeAr: string
  timeline: 'onTime' | 'delayed' | 'ahead'
  department: string
  departmentAr: string
  estimatedHours: number
  actualHours: number
}

const translations = {
  ar: {
    departmentProjects: 'مشاريع القسم',
    addProject: 'إضافة مشروع',
    editProject: 'تعديل المشروع',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المشروع؟',
    searchPlaceholder: 'البحث في المشاريع...',
    name: 'اسم المشروع',
    description: 'الوصف',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    progress: 'التقدم',
    status: 'الحالة',
    priority: 'الأولوية',
    teamSize: 'حجم الفريق',
    budget: 'الميزانية',
    assignee: 'المسؤول',
    timeline: 'الجدول الزمني',
    department: 'القسم',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    active: 'نشط',
    completed: 'مكتمل',
    onHold: 'معلق',
    planning: 'تخطيط',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    onTime: 'في الوقت المحدد',
    delayed: 'متأخر',
    ahead: 'متقدم'
  },
  en: {
    departmentProjects: 'Department Projects',
    addProject: 'Add Project',
    editProject: 'Edit Project',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this project?',
    searchPlaceholder: 'Search projects...',
    name: 'Project Name',
    description: 'Description',
    startDate: 'Start Date',
    endDate: 'End Date',
    progress: 'Progress',
    status: 'Status',
    priority: 'Priority',
    teamSize: 'Team Size',
    budget: 'Budget',
    assignee: 'Assignee',
    timeline: 'Timeline',
    department: 'Department',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    active: 'Active',
    completed: 'Completed',
    onHold: 'On Hold',
    planning: 'Planning',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    onTime: 'On Time',
    delayed: 'Delayed',
    ahead: 'Ahead'
  }
}

export default function DepartmentProjects({ language }: DepartmentProjectsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: projects,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<DepartmentProject>({
    service: departmentProjectService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'onHold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'planning':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTimelineColor = (timeline: string) => {
    switch (timeline) {
      case 'onTime':
        return 'text-green-400'
      case 'delayed':
        return 'text-red-400'
      case 'ahead':
        return 'text-blue-400'
      default:
        return 'text-gray-400'
    }
  }

  const getTimelineIcon = (timeline: string) => {
    switch (timeline) {
      case 'onTime':
        return <CheckCircle className="h-3 w-3" />
      case 'delayed':
        return <AlertTriangle className="h-3 w-3" />
      case 'ahead':
        return <TrendingUp className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<DepartmentProject>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <Briefcase className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: DepartmentProject) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: DepartmentProject) => (
        <Badge className={getPriorityColor(item.priority)}>
          {t[item.priority as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(item.progress)}`}
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'timeline',
      label: t.timeline,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className={`flex items-center gap-1 ${getTimelineColor(item.timeline)}`}>
          {getTimelineIcon(item.timeline)}
          <span className="text-sm font-medium">
            {t[item.timeline as keyof typeof t]}
          </span>
        </div>
      )
    },
    {
      key: 'assignee',
      label: t.assignee,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.assigneeAr : item.assignee}
          </span>
        </div>
      )
    },
    {
      key: 'teamSize',
      label: t.teamSize,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.teamSize}</span>
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-green-400 font-medium">
            {formatCurrency(item.budget)}
          </span>
        </div>
      )
    },
    {
      key: 'endDate',
      label: t.endDate,
      sortable: true,
      render: (item: DepartmentProject) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.endDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<DepartmentProject>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: DepartmentProject) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: DepartmentProject) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: DepartmentProject) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.completed, value: 'completed' },
        { label: t.onHold, value: 'onHold' },
        { label: t.planning, value: 'planning' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      key: 'timeline',
      label: t.timeline,
      options: [
        { label: t.onTime, value: 'onTime' },
        { label: t.delayed, value: 'delayed' },
        { label: t.ahead, value: 'ahead' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.completed, value: 'completed' },
        { label: t.onHold, value: 'onHold' },
        { label: t.planning, value: 'planning' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'teamSize',
      label: t.teamSize,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'assignee',
      label: t.assignee,
      type: 'text',
      required: true
    },
    {
      name: 'assigneeAr',
      label: t.assignee + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'timeline',
      label: t.timeline,
      type: 'select',
      required: true,
      options: [
        { label: t.onTime, value: 'onTime' },
        { label: t.delayed, value: 'delayed' },
        { label: t.ahead, value: 'ahead' }
      ]
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'estimatedHours',
      label: t.estimatedHours,
      type: 'number',
      min: 0
    },
    {
      name: 'actualHours',
      label: t.actualHours,
      type: 'number',
      min: 0
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<DepartmentProject>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.departmentProjects}
        data={projects}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProject : modalMode === 'edit' ? t.editProject : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
