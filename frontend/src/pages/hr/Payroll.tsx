/**
 * Payroll Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  Calculator,
  User,
  Calendar,
  FileText,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  AlertCircle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { payrollService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PayrollProps {
  language: 'ar' | 'en'
}

interface PayrollRecord {
  id: number
  employee_id: number
  employee_name: string
  department: string
  pay_period: string
  basic_salary: number
  allowances: number
  overtime_pay: number
  bonus: number
  deductions: number
  tax: number
  insurance: number
  net_salary: number
  status: 'pending' | 'processed' | 'paid'
  processed_date?: string
  paid_date?: string
}

const translations = {
  ar: {
    payroll: 'كشوف المرتبات',
    addPayroll: 'إضافة كشف مرتبات',
    editPayroll: 'تعديل كشف مرتبات',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف كشف المرتبات هذا؟',
    searchPlaceholder: 'البحث في كشوف المرتبات...',
    employee: 'الموظف',
    department: 'القسم',
    payPeriod: 'فترة الدفع',
    basicSalary: 'الراتب الأساسي',
    allowances: 'البدلات',
    overtimePay: 'أجر الوقت الإضافي',
    bonus: 'المكافآت',
    deductions: 'الخصومات',
    tax: 'الضرائب',
    insurance: 'التأمين',
    netSalary: 'صافي الراتب',
    status: 'الحالة',
    processedDate: 'تاريخ المعالجة',
    paidDate: 'تاريخ الدفع',
    pending: 'معلق',
    processed: 'معالج',
    paid: 'مدفوع',
    generatePayroll: 'إنشاء كشف مرتبات',
    viewPayslip: 'عرض كشف الراتب'
  },
  en: {
    payroll: 'Payroll',
    addPayroll: 'Add Payroll',
    editPayroll: 'Edit Payroll',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this payroll record?',
    searchPlaceholder: 'Search payroll...',
    employee: 'Employee',
    department: 'Department',
    payPeriod: 'Pay Period',
    basicSalary: 'Basic Salary',
    allowances: 'Allowances',
    overtimePay: 'Overtime Pay',
    bonus: 'Bonus',
    deductions: 'Deductions',
    tax: 'Tax',
    insurance: 'Insurance',
    netSalary: 'Net Salary',
    status: 'Status',
    processedDate: 'Processed Date',
    paidDate: 'Paid Date',
    pending: 'Pending',
    processed: 'Processed',
    paid: 'Paid',
    generatePayroll: 'Generate Payroll',
    viewPayslip: 'View Payslip'
  }
}

export default function Payroll({ language }: PayrollProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: payrollRecords,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PayrollRecord>({
    service: payrollService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'processed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-3 w-3" />
      case 'processed':
        return <FileText className="h-3 w-3" />
      case 'pending':
        return <Clock className="h-3 w-3" />
      default:
        return <AlertCircle className="h-3 w-3" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<PayrollRecord>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: PayrollRecord) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.employee_name}</span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: PayrollRecord) => (
        <span className="text-white/80">{item.department}</span>
      )
    },
    {
      key: 'pay_period',
      label: t.payPeriod,
      sortable: true,
      render: (item: PayrollRecord) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.pay_period}</span>
        </div>
      )
    },
    {
      key: 'basic_salary',
      label: t.basicSalary,
      sortable: true,
      render: (item: PayrollRecord) => (
        <span className="text-white font-medium">{formatCurrency(item.basic_salary)}</span>
      )
    },
    {
      key: 'allowances',
      label: t.allowances,
      sortable: true,
      render: (item: PayrollRecord) => (
        <span className="text-green-400 font-medium">{formatCurrency(item.allowances)}</span>
      )
    },
    {
      key: 'deductions',
      label: t.deductions,
      sortable: true,
      render: (item: PayrollRecord) => (
        <span className="text-red-400 font-medium">{formatCurrency(item.deductions)}</span>
      )
    },
    {
      key: 'net_salary',
      label: t.netSalary,
      sortable: true,
      render: (item: PayrollRecord) => (
        <span className="text-white font-bold">{formatCurrency(item.net_salary)}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: PayrollRecord) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{t[item.status as keyof typeof t]}</span>
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<PayrollRecord>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: PayrollRecord) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: PayrollRecord) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.viewPayslip,
      icon: FileText,
      onClick: (item: PayrollRecord) => {
        // Handle payslip view
        console.log('View payslip for:', item.employee_name)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: PayrollRecord) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.processed, value: 'processed' },
        { label: t.paid, value: 'paid' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: 'HR', value: 'hr' },
        { label: 'Finance', value: 'finance' },
        { label: 'IT', value: 'it' },
        { label: 'Sales', value: 'sales' },
        { label: 'Marketing', value: 'marketing' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee_id',
      label: t.employee,
      type: 'select',
      required: true,
      options: [] // Should be populated with employee options
    },
    {
      name: 'pay_period',
      label: t.payPeriod,
      type: 'text',
      required: true
    },
    {
      name: 'basic_salary',
      label: t.basicSalary,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'allowances',
      label: t.allowances,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'overtime_pay',
      label: t.overtimePay,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'bonus',
      label: t.bonus,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'deductions',
      label: t.deductions,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'tax',
      label: t.tax,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'insurance',
      label: t.insurance,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.processed, value: 'processed' },
        { label: t.paid, value: 'paid' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PayrollRecord>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.payroll}
        data={payrollRecords}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addPayroll : modalMode === 'edit' ? t.editPayroll : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
