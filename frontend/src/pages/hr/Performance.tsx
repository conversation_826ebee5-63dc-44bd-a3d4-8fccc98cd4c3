/**
 * Performance Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  Star,
  Target,
  Award,
  User,
  Calendar,
  BarChart3,
  CheckCircle,
  Eye,
  Edit,
  Trash2,
  Clock,
  AlertCircle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { performanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PerformanceProps {
  language: 'ar' | 'en'
}

interface PerformanceReview {
  id: number
  employee_id: number
  employee_name: string
  department: string
  review_period: string
  review_type: 'annual' | 'quarterly' | 'probation' | 'mid_year'
  reviewer_id: number
  reviewer_name: string
  overall_rating: number
  goals_rating: number
  competencies_rating: number
  development_rating: number
  status: 'pending' | 'in_progress' | 'completed' | 'approved'
  review_date: string
  due_date: string
  comments?: string
  strengths?: string
  areas_for_improvement?: string
}

const translations = {
  ar: {
    performance: 'تقييم الأداء',
    addReview: 'إضافة مراجعة',
    editReview: 'تعديل المراجعة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المراجعة؟',
    searchPlaceholder: 'البحث في مراجعات الأداء...',
    employee: 'الموظف',
    department: 'القسم',
    reviewPeriod: 'فترة المراجعة',
    reviewType: 'نوع المراجعة',
    reviewer: 'المراجع',
    overallRating: 'التقييم العام',
    goalsRating: 'تقييم الأهداف',
    competenciesRating: 'تقييم الكفاءات',
    developmentRating: 'تقييم التطوير',
    status: 'الحالة',
    reviewDate: 'تاريخ المراجعة',
    dueDate: 'تاريخ الاستحقاق',
    comments: 'التعليقات',
    strengths: 'نقاط القوة',
    areasForImprovement: 'مجالات التحسين',
    pending: 'معلق',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    approved: 'معتمد',
    reviewTypes: {
      annual: 'سنوي',
      quarterly: 'ربع سنوي',
      probation: 'فترة تجريبية',
      mid_year: 'منتصف السنة'
    }
  },
  en: {
    performance: 'Performance Reviews',
    addReview: 'Add Review',
    editReview: 'Edit Review',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this review?',
    searchPlaceholder: 'Search performance reviews...',
    employee: 'Employee',
    department: 'Department',
    reviewPeriod: 'Review Period',
    reviewType: 'Review Type',
    reviewer: 'Reviewer',
    overallRating: 'Overall Rating',
    goalsRating: 'Goals Rating',
    competenciesRating: 'Competencies Rating',
    developmentRating: 'Development Rating',
    status: 'Status',
    reviewDate: 'Review Date',
    dueDate: 'Due Date',
    comments: 'Comments',
    strengths: 'Strengths',
    areasForImprovement: 'Areas for Improvement',
    pending: 'Pending',
    inProgress: 'In Progress',
    completed: 'Completed',
    approved: 'Approved',
    reviewTypes: {
      annual: 'Annual',
      quarterly: 'Quarterly',
      probation: 'Probation',
      mid_year: 'Mid Year'
    }
  }
}

export default function Performance({ language }: PerformanceProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: performanceReviews,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PerformanceReview>({
    service: performanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-400'
    if (rating >= 4.0) return 'text-blue-400'
    if (rating >= 3.0) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'pending':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-3 w-3" />
      case 'completed':
        return <Target className="h-3 w-3" />
      case 'in_progress':
        return <Clock className="h-3 w-3" />
      case 'pending':
        return <AlertCircle className="h-3 w-3" />
      default:
        return <AlertCircle className="h-3 w-3" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<PerformanceReview>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.employee_name}</span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: PerformanceReview) => (
        <span className="text-white/80">{item.department}</span>
      )
    },
    {
      key: 'review_period',
      label: t.reviewPeriod,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.review_period}</span>
        </div>
      )
    },
    {
      key: 'review_type',
      label: t.reviewType,
      sortable: true,
      render: (item: PerformanceReview) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t.reviewTypes[item.review_type as keyof typeof t.reviewTypes]}
        </Badge>
      )
    },
    {
      key: 'reviewer_name',
      label: t.reviewer,
      render: (item: PerformanceReview) => (
        <span className="text-white/80">{item.reviewer_name}</span>
      )
    },
    {
      key: 'overall_rating',
      label: t.overallRating,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-1">
          <Star className={`h-4 w-4 ${getRatingColor(item.overall_rating)} fill-current`} />
          <span className={`font-medium ${getRatingColor(item.overall_rating)}`}>
            {item.overall_rating.toFixed(1)}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: PerformanceReview) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{String(t[item.status as keyof typeof t])}</span>
        </Badge>
      )
    },
    {
      key: 'due_date',
      label: t.dueDate,
      sortable: true,
      render: (item: PerformanceReview) => (
        <span className="text-white/80">{item.due_date}</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<PerformanceReview>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: PerformanceReview) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: PerformanceReview) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: PerformanceReview) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'in_progress' },
        { label: t.completed, value: 'completed' },
        { label: t.approved, value: 'approved' }
      ]
    },
    {
      key: 'review_type',
      label: t.reviewType,
      options: [
        { label: t.reviewTypes.annual, value: 'annual' },
        { label: t.reviewTypes.quarterly, value: 'quarterly' },
        { label: t.reviewTypes.probation, value: 'probation' },
        { label: t.reviewTypes.mid_year, value: 'mid_year' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: 'HR', value: 'hr' },
        { label: 'Finance', value: 'finance' },
        { label: 'IT', value: 'it' },
        { label: 'Sales', value: 'sales' },
        { label: 'Marketing', value: 'marketing' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee_id',
      label: t.employee,
      type: 'select',
      required: true,
      options: [] // Should be populated with employee options
    },
    {
      name: 'review_period',
      label: t.reviewPeriod,
      type: 'text',
      required: true
    },
    {
      name: 'review_type',
      label: t.reviewType,
      type: 'select',
      required: true,
      options: [
        { label: t.reviewTypes.annual, value: 'annual' },
        { label: t.reviewTypes.quarterly, value: 'quarterly' },
        { label: t.reviewTypes.probation, value: 'probation' },
        { label: t.reviewTypes.mid_year, value: 'mid_year' }
      ]
    },
    {
      name: 'reviewer_id',
      label: t.reviewer,
      type: 'select',
      required: true,
      options: [] // Should be populated with reviewer options
    },
    {
      name: 'overall_rating',
      label: t.overallRating,
      type: 'number',
      required: true,
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'goals_rating',
      label: t.goalsRating,
      type: 'number',
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'competencies_rating',
      label: t.competenciesRating,
      type: 'number',
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'development_rating',
      label: t.developmentRating,
      type: 'number',
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'review_date',
      label: t.reviewDate,
      type: 'date',
      required: true
    },
    {
      name: 'due_date',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'in_progress' },
        { label: t.completed, value: 'completed' },
        { label: t.approved, value: 'approved' }
      ]
    },
    {
      name: 'comments',
      label: t.comments,
      type: 'textarea'
    },
    {
      name: 'strengths',
      label: t.strengths,
      type: 'textarea'
    },
    {
      name: 'areas_for_improvement',
      label: t.areasForImprovement,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PerformanceReview>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.performance}
        data={performanceReviews}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReview : modalMode === 'edit' ? t.editReview : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
