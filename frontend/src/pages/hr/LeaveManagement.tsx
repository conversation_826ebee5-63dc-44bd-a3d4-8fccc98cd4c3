/**
 * Leave Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  CalendarDays,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { leaveManagementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface LeaveManagementProps {
  language: 'ar' | 'en'
}

interface LeaveRequest {
  id: number
  employee_name: string
  employee_nameAr: string
  leave_type: string
  leave_typeAr: string
  start_date: string
  end_date: string
  duration: number
  status: 'pending' | 'approved' | 'rejected'
  reason: string
  reasonAr: string
  applied_date: string
  approved_by?: string
  approved_byAr?: string
  approved_date?: string
  comments?: string
  commentsAr?: string
}

const translations = {
  ar: {
    leaveManagement: 'إدارة الإجازات',
    addRequest: 'إضافة طلب إجازة',
    editRequest: 'تعديل طلب الإجازة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف طلب الإجازة هذا؟',
    searchPlaceholder: 'البحث في طلبات الإجازات...',
    employee: 'الموظف',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة (بالأيام)',
    status: 'الحالة',
    reason: 'السبب',
    appliedDate: 'تاريخ التقديم',
    approvedBy: 'معتمد بواسطة',
    approvedDate: 'تاريخ الاعتماد',
    comments: 'التعليقات',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض',
    employeeName: 'اسم الموظف',
    leaveTypes: {
      annual: 'إجازة سنوية',
      sick: 'إجازة مرضية',
      personal: 'إجازة شخصية',
      maternity: 'إجازة أمومة',
      emergency: 'إجازة طارئة'
    }
  },
  en: {
    leaveManagement: 'Leave Management',
    addRequest: 'Add Leave Request',
    editRequest: 'Edit Leave Request',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this leave request?',
    searchPlaceholder: 'Search leave requests...',
    employee: 'Employee',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration (days)',
    status: 'Status',
    reason: 'Reason',
    appliedDate: 'Applied Date',
    approvedBy: 'Approved By',
    approvedDate: 'Approved Date',
    comments: 'Comments',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    employeeName: 'Employee Name',
    leaveTypes: {
      annual: 'Annual Leave',
      sick: 'Sick Leave',
      personal: 'Personal Leave',
      maternity: 'Maternity Leave',
      emergency: 'Emergency Leave'
    }
  }
}

export default function LeaveManagement({ language }: LeaveManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: leaveRequests,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<LeaveRequest>({
    service: leaveManagementService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const calculateDuration = (startDate: string, endDate: string): number => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-400" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<LeaveRequest>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">
            {language === 'ar' ? item.employee_nameAr : item.employee_name}
          </span>
        </div>
      )
    },
    {
      key: 'leave_type',
      label: t.leaveType,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">
          {language === 'ar' ? item.leave_typeAr : item.leave_type}
        </span>
      )
    },
    {
      key: 'start_date',
      label: t.startDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.start_date}</span>
        </div>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-red-400" />
          <span className="text-white/80">{item.end_date}</span>
        </div>
      )
    },
    {
      key: 'duration',
      label: t.duration,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <CalendarDays className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.duration} days</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {String(t[item.status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'applied_date',
      label: t.appliedDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">{item.applied_date}</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<LeaveRequest>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: LeaveRequest) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    },
    {
      key: 'leave_type',
      label: t.leaveType,
      options: [
        { label: t.leaveTypes.annual, value: 'annual' },
        { label: t.leaveTypes.sick, value: 'sick' },
        { label: t.leaveTypes.personal, value: 'personal' },
        { label: t.leaveTypes.maternity, value: 'maternity' },
        { label: t.leaveTypes.emergency, value: 'emergency' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee_name',
      label: t.employeeName,
      type: 'text',
      required: true
    },
    {
      name: 'employee_nameAr',
      label: t.employeeName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'leave_type',
      label: t.leaveType,
      type: 'select',
      required: true,
      options: [
        { label: t.leaveTypes.annual, value: 'annual' },
        { label: t.leaveTypes.sick, value: 'sick' },
        { label: t.leaveTypes.personal, value: 'personal' },
        { label: t.leaveTypes.maternity, value: 'maternity' },
        { label: t.leaveTypes.emergency, value: 'emergency' }
      ]
    },
    {
      name: 'leave_typeAr',
      label: t.leaveType + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'end_date',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'duration',
      label: t.duration,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'reason',
      label: t.reason,
      type: 'textarea',
      required: true
    },
    {
      name: 'reasonAr',
      label: t.reason + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    },
    {
      name: 'applied_date',
      label: t.appliedDate,
      type: 'date',
      required: true
    },
    {
      name: 'approved_by',
      label: t.approvedBy,
      type: 'text'
    },
    {
      name: 'approved_byAr',
      label: t.approvedBy + ' (عربي)',
      type: 'text'
    },
    {
      name: 'approved_date',
      label: t.approvedDate,
      type: 'date'
    },
    {
      name: 'comments',
      label: t.comments,
      type: 'textarea'
    },
    {
      name: 'commentsAr',
      label: t.comments + ' (عربي)',
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<LeaveRequest>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for leave reports
        const response = await fetch(`http://localhost:8001/api/pdf/generate/hr-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `leave-management-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.leaveManagement}
        data={leaveRequests}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addRequest : modalMode === 'edit' ? t.editRequest : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
