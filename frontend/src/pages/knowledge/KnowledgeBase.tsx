/**
 * Knowledge Base Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  BookOpen,
  Eye,
  Edit,
  Trash2,
  Star,
  ThumbsUp,
  MessageCircle,
  FileText,
  Video,
  User,
  Calendar,
  Clock,
  TrendingUp
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { knowledgeBaseService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface KnowledgeBaseProps {
  language: 'ar' | 'en'
}

interface Article {
  id: number
  title: string
  titleAr: string
  content: string
  contentAr: string
  summary: string
  summaryAr: string
  category: string
  categoryAr: string
  tags: string
  tagsAr: string
  author: string
  authorAr: string
  createdDate: string
  lastUpdated: string
  views: number
  likes: number
  comments: number
  rating: number
  status: 'draft' | 'published' | 'archived' | 'under-review'
  type: 'article' | 'faq' | 'procedure' | 'policy' | 'tutorial' | 'video' | 'document'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedReadTime: number
  isBookmarked: boolean
  isFeatured: boolean
}

const translations = {
  ar: {
    knowledgeBase: 'قاعدة المعرفة',
    addArticle: 'إضافة مقال',
    editArticle: 'تعديل المقال',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المقال؟',
    searchPlaceholder: 'البحث في قاعدة المعرفة...',
    title: 'العنوان',
    summary: 'الملخص',
    content: 'المحتوى',
    category: 'الفئة',
    tags: 'العلامات',
    author: 'الكاتب',
    type: 'النوع',
    difficulty: 'المستوى',
    status: 'الحالة',
    views: 'المشاهدات',
    likes: 'الإعجابات',
    rating: 'التقييم',
    readTime: 'وقت القراءة',
    createdDate: 'تاريخ الإنشاء',
    lastUpdated: 'آخر تحديث',
    isFeatured: 'مميز',
    isBookmarked: 'مفضل',
    draft: 'مسودة',
    published: 'منشور',
    archived: 'مؤرشف',
    'under-review': 'قيد المراجعة',
    article: 'مقال',
    faq: 'أسئلة شائعة',
    procedure: 'إجراء',
    policy: 'سياسة',
    tutorial: 'دليل تعليمي',
    video: 'فيديو',
    document: 'وثيقة',
    beginner: 'مبتدئ',
    intermediate: 'متوسط',
    advanced: 'متقدم',
    categories: {
      hr: 'الموارد البشرية',
      it: 'تقنية المعلومات',
      finance: 'المالية',
      operations: 'العمليات',
      management: 'الإدارة',
      training: 'التدريب'
    }
  },
  en: {
    knowledgeBase: 'Knowledge Base',
    addArticle: 'Add Article',
    editArticle: 'Edit Article',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this article?',
    searchPlaceholder: 'Search knowledge base...',
    title: 'Title',
    summary: 'Summary',
    content: 'Content',
    category: 'Category',
    tags: 'Tags',
    author: 'Author',
    type: 'Type',
    difficulty: 'Difficulty',
    status: 'Status',
    views: 'Views',
    likes: 'Likes',
    rating: 'Rating',
    readTime: 'Read Time',
    createdDate: 'Created Date',
    lastUpdated: 'Last Updated',
    isFeatured: 'Featured',
    isBookmarked: 'Bookmarked',
    draft: 'Draft',
    published: 'Published',
    archived: 'Archived',
    'under-review': 'Under Review',
    article: 'Article',
    faq: 'FAQ',
    procedure: 'Procedure',
    policy: 'Policy',
    tutorial: 'Tutorial',
    video: 'Video',
    document: 'Document',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    categories: {
      hr: 'Human Resources',
      it: 'Information Technology',
      finance: 'Finance',
      operations: 'Operations',
      management: 'Management',
      training: 'Training'
    }
  }
}

export default function KnowledgeBase({ language }: KnowledgeBaseProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: articles,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Article>({
    service: knowledgeBaseService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'article': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'faq': return 'bg-green-100 text-green-800 border-green-200'
      case 'procedure': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'policy': return 'bg-red-100 text-red-800 border-red-200'
      case 'tutorial': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'video': return 'bg-pink-100 text-pink-800 border-pink-200'
      case 'document': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400'
      case 'intermediate': return 'text-yellow-400'
      case 'advanced': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'article': return <FileText className="h-4 w-4 text-blue-400" />
      case 'video': return <Video className="h-4 w-4 text-pink-400" />
      case 'faq': return <MessageCircle className="h-4 w-4 text-green-400" />
      case 'procedure': return <BookOpen className="h-4 w-4 text-purple-400" />
      case 'policy': return <FileText className="h-4 w-4 text-red-400" />
      case 'tutorial': return <BookOpen className="h-4 w-4 text-orange-400" />
      case 'document': return <FileText className="h-4 w-4 text-gray-400" />
      default: return <BookOpen className="h-4 w-4 text-gray-400" />
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />)
    }

    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />)
    }

    const remainingStars = 5 - Math.ceil(rating)
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-3 h-3 text-gray-400" />)
    }

    return stars
  }

  // Table columns configuration
  const columns: TableColumn<Article>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: Article) => (
        <div className="flex items-center gap-2">
          {getTypeIcon(item.type)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.summaryAr?.substring(0, 50) + '...' : item.summary?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.isFeatured && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: Article) => (
        <Badge className={getTypeColor(item.type)}>
          {String(t[item.type as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: Article) => (
        <span className="text-white/80">
          {language === 'ar' ? item.categoryAr : item.category}
        </span>
      )
    },
    {
      key: 'difficulty',
      label: t.difficulty,
      sortable: true,
      render: (item: Article) => (
        <span className={`font-medium ${getDifficultyColor(item.difficulty)}`}>
          {String(t[item.difficulty as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'author',
      label: t.author,
      render: (item: Article) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.authorAr : item.author}
          </span>
        </div>
      )
    },
    {
      key: 'views',
      label: t.views,
      sortable: true,
      render: (item: Article) => (
        <div className="flex items-center gap-1">
          <Eye className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.views}</span>
        </div>
      )
    },
    {
      key: 'rating',
      label: t.rating,
      sortable: true,
      render: (item: Article) => (
        <div className="flex items-center gap-1">
          <div className="flex">{getRatingStars(item.rating)}</div>
          <span className="text-white/80 text-sm">({item.rating.toFixed(1)})</span>
        </div>
      )
    },
    {
      key: 'lastUpdated',
      label: t.lastUpdated,
      sortable: true,
      render: (item: Article) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastUpdated}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Article>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Article) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Article) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Article) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.article, value: 'article' },
        { label: t.faq, value: 'faq' },
        { label: t.procedure, value: 'procedure' },
        { label: t.policy, value: 'policy' },
        { label: t.tutorial, value: 'tutorial' },
        { label: t.video, value: 'video' },
        { label: t.document, value: 'document' }
      ]
    },
    {
      key: 'difficulty',
      label: t.difficulty,
      options: [
        { label: t.beginner, value: 'beginner' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.published, value: 'published' },
        { label: t.archived, value: 'archived' },
        { label: t['under-review'], value: 'under-review' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.hr, value: 'hr' },
        { label: t.categories.it, value: 'it' },
        { label: t.categories.finance, value: 'finance' },
        { label: t.categories.operations, value: 'operations' },
        { label: t.categories.management, value: 'management' },
        { label: t.categories.training, value: 'training' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'summary',
      label: t.summary,
      type: 'textarea',
      required: true
    },
    {
      name: 'summaryAr',
      label: t.summary + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'content',
      label: t.content,
      type: 'textarea',
      required: true
    },
    {
      name: 'contentAr',
      label: t.content + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'tags',
      label: t.tags,
      type: 'text',
      placeholder: 'Comma-separated tags'
    },
    {
      name: 'tagsAr',
      label: t.tags + ' (عربي)',
      type: 'text',
      placeholder: 'علامات مفصولة بفواصل'
    },
    {
      name: 'author',
      label: t.author,
      type: 'text',
      required: true
    },
    {
      name: 'authorAr',
      label: t.author + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.article, value: 'article' },
        { label: t.faq, value: 'faq' },
        { label: t.procedure, value: 'procedure' },
        { label: t.policy, value: 'policy' },
        { label: t.tutorial, value: 'tutorial' },
        { label: t.video, value: 'video' },
        { label: t.document, value: 'document' }
      ]
    },
    {
      name: 'difficulty',
      label: t.difficulty,
      type: 'select',
      required: true,
      options: [
        { label: t.beginner, value: 'beginner' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.published, value: 'published' },
        { label: t.archived, value: 'archived' },
        { label: t['under-review'], value: 'under-review' }
      ]
    },
    {
      name: 'estimatedReadTime',
      label: t.readTime + ' (minutes)',
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'isFeatured',
      label: t.isFeatured,
      type: 'checkbox'
    },
    {
      name: 'isBookmarked',
      label: t.isBookmarked,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Article>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.knowledgeBase}
        data={articles}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addArticle : modalMode === 'edit' ? t.editArticle : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
