/**
 * Business Intelligence Page with Real API Data and Charts
 * Professional KPIs and Reports management with data visualization
 */

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { But<PERSON> } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Checkbox } from '../../components/ui/checkbox'
import { Progress } from '../../components/ui/progress'
import { toast } from 'react-hot-toast'
import {
  BarChart3,
  Plus,
  Eye,
  Download,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  RefreshCw,
  Activity,
  FileText,
  Edit,
  Trash2,
  MoreHorizontal,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  CheckSquare,
  Square,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Building,
  PieChart,
  LineChart,
  Brain,
  Lightbulb,
  Bell,
  Settings,
  Zap,
  Shield,
  Gauge,
  TrendingUp as TrendIcon,
  PieChart as PieIcon,
  Activity as ActivityIcon,
  AlertCircle,
  Info,
  Star,
  ArrowRight,
  PlayCircle,
  PauseCircle,
  StopCircle
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart
} from 'recharts'
import { useCrud } from '../../hooks/useCrud'
import { kpiService } from '../../services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '../../components/common/CrudTable'
import CrudModal, { FormField } from '../../components/common/CrudModal'

interface BusinessIntelligenceProps {
  language: 'ar' | 'en'
}

// API Data Models
interface KPIValue {
  id: number
  value: number
  date: string
  notes?: string
}

interface KPICategory {
  id: number
  name: string
  name_ar: string
  description?: string
  color: string
  is_active: boolean
}

interface KPI {
  id: number
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  category: KPICategory
  target_value: number
  current_value: number
  unit: string
  unit_ar: string
  status: 'ACTIVE' | 'INACTIVE'
  trend: 'up' | 'down' | 'stable'
  change_percentage: number
  last_updated: string
  values: KPIValue[]
  achievement_percentage: number
}

interface KPIAlert {
  id: number
  kpi: number
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  message_ar: string
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED'
  created_at: string
}

interface DashboardData {
  categories: KPICategory[]
  recent_alerts: KPIAlert[]
  top_performing_kpis: KPI[]
  underperforming_kpis: KPI[]
  kpi_summary: {
    total_kpis: number
    active_kpis: number
    kpis_on_target: number
    kpis_above_target: number
    kpis_below_target: number
    active_alerts: number
    critical_alerts: number
    categories_count: number
    last_updated: string
  }
  all_kpis: KPI[]
}

interface ChartDataPoint {
  name: string
  value: number
  target?: number
  achievement?: number
  category?: string
  color?: string
}

// Translations
const translations = {
  ar: {
    title: 'ذكاء الأعمال',
    description: 'تحليلات متقدمة ومؤشرات الأداء الرئيسية',
    // Tabs
    overview: 'نظرة عامة',
    kpis: 'مؤشرات الأداء الرئيسية',
    analytics: 'التحليلات والرسوم البيانية',
    reports: 'التقارير المخصصة',
    insights: 'الرؤى والتوصيات',
    predictions: 'التحليلات التنبؤية',
    alerts: 'التنبيهات والإشعارات',
    // Actions
    newKPI: 'مؤشر جديد',
    refresh: 'تحديث',
    export: 'تصدير',
    target: 'الهدف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    save: 'حفظ',
    cancel: 'إلغاء',
    create: 'إنشاء',
    loading: 'جاري التحميل...',
    saving: 'جاري الحفظ...',
    search: 'البحث...',
    allCategories: 'جميع الفئات',
    sortBy: 'ترتيب حسب',
    name: 'الاسم',
    value: 'القيمة',
    category: 'الفئة',
    filters: 'فلاتر',
    clearFilters: 'مسح الفلاتر',
    selectAll: 'تحديد الكل',
    fromDate: 'من تاريخ',
    toDate: 'إلى تاريخ',
    results: 'النتائج',
    // Overview
    executiveSummary: 'الملخص التنفيذي',
    keyMetrics: 'المقاييس الرئيسية',
    performanceTrends: 'اتجاهات الأداء',
    quickActions: 'الإجراءات السريعة',
    // Reports
    generateReport: 'إنشاء تقرير',
    scheduleReport: 'جدولة التقرير',
    reportTemplates: 'قوالب التقارير',
    // Insights
    aiInsights: 'رؤى الذكاء الاصطناعي',
    recommendations: 'التوصيات',
    opportunities: 'الفرص',
    risks: 'المخاطر',
    // Predictions
    forecastModels: 'نماذج التنبؤ',
    trendPrediction: 'توقع الاتجاهات',
    scenarioAnalysis: 'تحليل السيناريوهات',
    // Alerts
    activeAlerts: 'التنبيهات النشطة',
    alertRules: 'قواعد التنبيه',
    notificationSettings: 'إعدادات الإشعارات'
  },
  en: {
    title: 'Business Intelligence',
    description: 'Advanced analytics and key performance indicators',
    // Tabs
    overview: 'Overview',
    kpis: 'Key Performance Indicators',
    analytics: 'Analytics & Charts',
    reports: 'Custom Reports',
    insights: 'Insights & Recommendations',
    predictions: 'Predictive Analytics',
    alerts: 'Alerts & Notifications',
    // Actions
    newKPI: 'New KPI',
    refresh: 'Refresh',
    export: 'Export',
    target: 'Target',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    create: 'Create',
    loading: 'Loading...',
    saving: 'Saving...',
    search: 'Search...',
    allCategories: 'All Categories',
    sortBy: 'Sort by',
    name: 'Name',
    value: 'Value',
    category: 'Category',
    filters: 'Filters',
    clearFilters: 'Clear Filters',
    selectAll: 'Select All',
    fromDate: 'From Date',
    toDate: 'To Date',
    results: 'Results',
    // Overview
    executiveSummary: 'Executive Summary',
    keyMetrics: 'Key Metrics',
    performanceTrends: 'Performance Trends',
    quickActions: 'Quick Actions',
    // Reports
    generateReport: 'Generate Report',
    scheduleReport: 'Schedule Report',
    reportTemplates: 'Report Templates',
    // Insights
    aiInsights: 'AI Insights',
    recommendations: 'Recommendations',
    opportunities: 'Opportunities',
    risks: 'Risks',
    // Predictions
    forecastModels: 'Forecast Models',
    trendPrediction: 'Trend Prediction',
    scenarioAnalysis: 'Scenario Analysis',
    // Alerts
    activeAlerts: 'Active Alerts',
    alertRules: 'Alert Rules',
    notificationSettings: 'Notification Settings'
  }
}

export default function BusinessIntelligence({ language }: BusinessIntelligenceProps) {
  // State Management
  const [activeTab, setActiveTab] = useState<'overview' | 'kpis' | 'analytics' | 'reports' | 'insights' | 'predictions' | 'alerts'>('overview')
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  // API Data State
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [categories, setCategories] = useState<KPICategory[]>([])
  const [alerts, setAlerts] = useState<KPIAlert[]>([])

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: kpis,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<KPI>({
    service: kpiService,
    autoLoad: true,
    pageSize: 20
  })

  // Table Columns Configuration
  const columns: TableColumn<KPI>[] = [
    {
      key: 'name',
      label: language === 'ar' ? 'اسم المؤشر' : 'KPI Name',
      sortable: true,
      render: (kpi: KPI) => (
        <div className="flex items-center gap-3">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: kpi.category.color }}
          />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? kpi.name_ar : kpi.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? kpi.category.name_ar : kpi.category.name}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'current_value',
      label: language === 'ar' ? 'القيمة الحالية' : 'Current Value',
      sortable: true,
      render: (kpi: KPI) => (
        <div className="text-right">
          <div className="font-medium text-white">
            {kpi.current_value.toLocaleString()} {language === 'ar' ? kpi.unit_ar : kpi.unit}
          </div>
        </div>
      )
    },
    {
      key: 'target_value',
      label: language === 'ar' ? 'القيمة المستهدفة' : 'Target Value',
      sortable: true,
      render: (kpi: KPI) => (
        <div className="text-right">
          <div className="font-medium text-white">
            {kpi.target_value.toLocaleString()} {language === 'ar' ? kpi.unit_ar : kpi.unit}
          </div>
        </div>
      )
    },
    {
      key: 'achievement_percentage',
      label: language === 'ar' ? 'نسبة الإنجاز' : 'Achievement',
      sortable: true,
      render: (kpi: KPI) => (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-white">
              {kpi.achievement_percentage.toFixed(1)}%
            </span>
          </div>
          <Progress
            value={Math.min(kpi.achievement_percentage, 100)}
            className="h-2 bg-white/10"
          />
        </div>
      )
    },
    {
      key: 'trend',
      label: language === 'ar' ? 'الاتجاه' : 'Trend',
      render: (kpi: KPI) => (
        <div className="flex items-center gap-2">
          {kpi.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-400" />}
          {kpi.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-400" />}
          {kpi.trend === 'stable' && <Activity className="w-4 h-4 text-yellow-400" />}
          <span className={`text-sm font-medium ${
            kpi.change_percentage > 0 ? 'text-green-400' :
            kpi.change_percentage < 0 ? 'text-red-400' : 'text-yellow-400'
          }`}>
            {kpi.change_percentage > 0 ? '+' : ''}{kpi.change_percentage.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: language === 'ar' ? 'الحالة' : 'Status',
      render: (kpi: KPI) => (
        <Badge
          variant={kpi.status === 'ACTIVE' ? 'default' : 'secondary'}
          className={kpi.status === 'ACTIVE' ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-gray-500/20 text-gray-400 border-gray-500/30'}
        >
          {kpi.status === 'ACTIVE' ? (language === 'ar' ? 'نشط' : 'Active') : (language === 'ar' ? 'غير نشط' : 'Inactive')}
        </Badge>
      )
    }
  ]

  // API Functions
  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/kpi-dashboard/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: DashboardData = await response.json()
      setDashboardData(data)
      setCategories(data.categories || [])
      setAlerts(data.recent_alerts || [])

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      // Fallback to mock data for demonstration
      setCategories([
        { id: 1, name: 'Financial', name_ar: 'مالية', color: '#3b82f6', is_active: true },
        { id: 2, name: 'Customer', name_ar: 'العملاء', color: '#10b981', is_active: true },
        { id: 3, name: 'HR', name_ar: 'الموارد البشرية', color: '#f59e0b', is_active: true },
        { id: 4, name: 'Sales', name_ar: 'المبيعات', color: '#ef4444', is_active: true }
      ])
      setDashboardData({
        categories: [
          { id: 1, name: 'Financial', name_ar: 'مالية', color: '#3b82f6', is_active: true },
          { id: 2, name: 'Customer', name_ar: 'العملاء', color: '#10b981', is_active: true },
          { id: 3, name: 'HR', name_ar: 'الموارد البشرية', color: '#f59e0b', is_active: true },
          { id: 4, name: 'Sales', name_ar: 'المبيعات', color: '#ef4444', is_active: true }
        ],
        recent_alerts: [],
        top_performing_kpis: [],
        underperforming_kpis: [],
        kpi_summary: {
          total_kpis: 0,
          active_kpis: 0,
          kpis_on_target: 0,
          kpis_above_target: 0,
          kpis_below_target: 0,
          active_alerts: 0,
          critical_alerts: 0,
          categories_count: 4,
          last_updated: new Date().toISOString()
        },
        all_kpis: []
      })
    }
  }

  // CRUD Handler Functions
  const handleCreate = () => {
    selectItem(null)
    setModalMode('create')
    setShowModal(true)
  }

  const handleView = (kpi: KPI) => {
    selectItem(kpi)
    setModalMode('view')
    setShowModal(true)
  }

  const handleEdit = (kpi: KPI) => {
    selectItem(kpi)
    setModalMode('edit')
    setShowModal(true)
  }

  const handleDelete = async (kpi: KPI) => {
    if (window.confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا المؤشر؟' : 'Are you sure you want to delete this KPI?')) {
      try {
        await deleteItem(kpi.id)
        toast.success(language === 'ar' ? 'تم حذف المؤشر بنجاح' : 'KPI deleted successfully')
      } catch (error) {
        toast.error(language === 'ar' ? 'فشل في حذف المؤشر' : 'Failed to delete KPI')
      }
    }
  }

  const handleSave = async (data: any) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
        toast.success(language === 'ar' ? 'تم إنشاء المؤشر بنجاح' : 'KPI created successfully')
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
        toast.success(language === 'ar' ? 'تم تحديث المؤشر بنجاح' : 'KPI updated successfully')
      }
      setShowModal(false)
    } catch (error) {
      toast.error(language === 'ar' ? 'حدث خطأ' : 'An error occurred')
    }
  }

  const handleExport = async (format?: 'csv' | 'excel' | 'pdf') => {
    try {
      await exportData(format)
      toast.success(language === 'ar' ? 'تم تصدير البيانات بنجاح' : 'Data exported successfully')
    } catch (error) {
      toast.error(language === 'ar' ? 'فشل في تصدير البيانات' : 'Failed to export data')
    }
  }

  // Table Actions Configuration
  const actions: TableAction<KPI>[] = [
    {
      label: language === 'ar' ? 'عرض' : 'View',
      icon: Eye,
      onClick: handleView,
      variant: 'ghost'
    },
    {
      label: language === 'ar' ? 'تعديل' : 'Edit',
      icon: Edit,
      onClick: handleEdit,
      variant: 'ghost'
    },
    {
      label: language === 'ar' ? 'حذف' : 'Delete',
      icon: Trash2,
      onClick: handleDelete,
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300'
    }
  ]

  // Filter Options Configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: language === 'ar' ? 'الفئة' : 'Category',
      options: categories.map(cat => ({
        value: cat.id.toString(),
        label: language === 'ar' ? cat.name_ar : cat.name
      }))
    },
    {
      key: 'status',
      label: language === 'ar' ? 'الحالة' : 'Status',
      options: [
        { value: 'ACTIVE', label: language === 'ar' ? 'نشط' : 'Active' },
        { value: 'INACTIVE', label: language === 'ar' ? 'غير نشط' : 'Inactive' }
      ]
    }
  ]

  // Mock data fallback
  const mockKPIs: KPI[] = [
    {
      id: 1,
      name: 'Total Revenue',
      name_ar: 'إجمالي الإيرادات',
      description: 'Total company revenue',
      description_ar: 'إجمالي إيرادات الشركة',
      category: {
        id: 1,
        name: 'Financial',
        name_ar: 'مالية',
        color: '#3b82f6',
        is_active: true
      },
      target_value: 2500000,
      current_value: 2450000,
      unit: 'SAR',
      unit_ar: 'ر.س',
      status: 'ACTIVE',
      trend: 'up',
      change_percentage: 11.4,
      last_updated: '2024-01-22T10:00:00Z',
      values: [
        { id: 1, value: 2450000, date: '2024-01-22' }
      ],
      achievement_percentage: 98.0
    },
    {
      id: 2,
      name: 'Customer Satisfaction',
      name_ar: 'رضا العملاء',
      description: 'Customer satisfaction rating',
      description_ar: 'تقييم رضا العملاء',
      category: {
        id: 2,
        name: 'Customer',
        name_ar: 'العملاء',
        color: '#10b981',
        is_active: true
      },
      target_value: 4.5,
      current_value: 4.2,
      unit: '/5',
      unit_ar: '/5',
      status: 'ACTIVE',
      trend: 'up',
      change_percentage: 5.0,
      last_updated: '2024-01-22T10:00:00Z',
      values: [
        { id: 2, value: 4.2, date: '2024-01-22' }
      ],
      achievement_percentage: 93.3
    },
    {
      id: 3,
      name: 'Employee Productivity',
      name_ar: 'إنتاجية الموظفين',
      description: 'Employee productivity percentage',
      description_ar: 'نسبة إنتاجية الموظفين',
      category: {
        id: 3,
        name: 'HR',
        name_ar: 'الموارد البشرية',
        color: '#f59e0b',
        is_active: true
      },
      target_value: 90,
      current_value: 87,
      unit: '%',
      unit_ar: '%',
      status: 'ACTIVE',
      trend: 'down',
      change_percentage: -5.4,
      last_updated: '2024-01-22T10:00:00Z',
      values: [
        { id: 3, value: 87, date: '2024-01-22' }
      ],
      achievement_percentage: 96.7
    },
    {
      id: 4,
      name: 'Sales Conversion Rate',
      name_ar: 'معدل تحويل المبيعات',
      description: 'Sales conversion rate percentage',
      description_ar: 'نسبة تحويل المبيعات',
      category: {
        id: 4,
        name: 'Sales',
        name_ar: 'المبيعات',
        color: '#ef4444',
        is_active: true
      },
      target_value: 25.0,
      current_value: 23.5,
      unit: '%',
      unit_ar: '%',
      status: 'ACTIVE',
      trend: 'up',
      change_percentage: 7.8,
      last_updated: '2024-01-22T10:00:00Z',
      values: [
        { id: 4, value: 23.5, date: '2024-01-22' }
      ],
      achievement_percentage: 94.0
    }
  ]

  // Form Fields Configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: language === 'ar' ? 'الاسم (إنجليزي)' : 'Name (English)',
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: language === 'ar' ? 'الاسم (عربي)' : 'Name (Arabic)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: language === 'ar' ? 'الوصف (إنجليزي)' : 'Description (English)',
      type: 'textarea',
      required: false,
      rows: 3
    },
    {
      name: 'description_ar',
      label: language === 'ar' ? 'الوصف (عربي)' : 'Description (Arabic)',
      type: 'textarea',
      required: false,
      rows: 3
    },
    {
      name: 'current_value',
      label: language === 'ar' ? 'القيمة الحالية' : 'Current Value',
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'target_value',
      label: language === 'ar' ? 'القيمة المستهدفة' : 'Target Value',
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'unit',
      label: language === 'ar' ? 'الوحدة (إنجليزي)' : 'Unit (English)',
      type: 'text',
      required: true
    },
    {
      name: 'unit_ar',
      label: language === 'ar' ? 'الوحدة (عربي)' : 'Unit (Arabic)',
      type: 'text',
      required: true
    },
    {
      name: 'category',
      label: language === 'ar' ? 'الفئة' : 'Category',
      type: 'select',
      required: true,
      options: categories.map(cat => ({
        value: cat.id.toString(),
        label: language === 'ar' ? cat.name_ar : cat.name
      }))
    },
    {
      name: 'status',
      label: language === 'ar' ? 'الحالة' : 'Status',
      type: 'select',
      required: true,
      options: [
        { value: 'ACTIVE', label: language === 'ar' ? 'نشط' : 'Active' },
        { value: 'INACTIVE', label: language === 'ar' ? 'غير نشط' : 'Inactive' }
      ]
    }
  ]

  // Load data on component mount
  useEffect(() => {
    fetchDashboardData()
  }, [])

  // Chart data preparation using CRUD data
  const chartData = useMemo(() => {
    if (!kpis.length) return []

    return kpis.map(kpi => ({
      name: language === 'ar' ? kpi.name_ar : kpi.name,
      value: kpi.current_value,
      target: kpi.target_value,
      achievement: kpi.achievement_percentage,
      category: language === 'ar' ? kpi.category.name_ar : kpi.category.name,
      color: kpi.category.color
    }))
  }, [kpis, language])

  const categoryData = useMemo(() => {
    if (!categories.length || !kpis.length) return []

    return categories.map(category => {
      const categoryKPIs = kpis.filter(kpi => kpi.category.id === category.id)
      return {
        name: language === 'ar' ? category.name_ar : category.name,
        value: categoryKPIs.length,
        color: category.color
      }
    })
  }, [categories, kpis, language])

  // Auto-refresh data every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      if (!loading) {
        refresh()
        fetchDashboardData()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [loading, refresh])

  // Chart Components
  const KPIPerformanceChart = () => (
    <Card className="hover:scale-105 transition-all duration-300">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600">
            <BarChart3 className="w-5 h-5 text-white" />
          </div>
          {language === 'ar' ? 'أداء المؤشرات' : 'KPI Performance'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <ComposedChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
            <XAxis
              dataKey="name"
              stroke="rgba(255,255,255,0.7)"
              fontSize={12}
            />
            <YAxis stroke="rgba(255,255,255,0.7)" fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: 'rgba(0,0,0,0.8)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
            <Legend />
            <Bar
              dataKey="value"
              fill="#3b82f6"
              name={language === 'ar' ? 'القيمة الحالية' : 'Current Value'}
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="target"
              fill="#10b981"
              name={language === 'ar' ? 'الهدف' : 'Target'}
              radius={[4, 4, 0, 0]}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )

  const CategoryDistributionChart = () => (
    <Card className="hover:scale-105 transition-all duration-300">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-green-600">
            <PieChart className="w-5 h-5 text-white" />
          </div>
          {language === 'ar' ? 'توزيع الفئات' : 'Category Distribution'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <RechartsPieChart>
            <Pie
              data={categoryData}
              cx="50%"
              cy="50%"
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
              label={({ name, value }) => `${name}: ${value}`}
            >
              {categoryData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: 'rgba(0,0,0,0.8)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </RechartsPieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )

  // Clear any CRUD errors
  useEffect(() => {
    if (error) {
      clearError()
    }
  }, [error, clearError])

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2 flex items-center gap-3">
            <BarChart3 className="w-8 h-8 text-blue-400" />
            {t.title}
          </h1>
          <p className="text-white/70">
            {t.description}
          </p>
        </div>
      </div>

      {/* Statistics Overview */}
      {dashboardData?.kpi_summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'إجمالي المؤشرات' : 'Total KPIs'}
                  </p>
                  <p className="text-2xl font-bold text-white">
                    {kpis.length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                  <Target className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'على الهدف' : 'On Target'}
                  </p>
                  <p className="text-2xl font-bold text-green-400">
                    {kpis.filter(kpi => kpi.achievement_percentage >= 95 && kpi.achievement_percentage <= 105).length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 shadow-lg">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'فوق الهدف' : 'Above Target'}
                  </p>
                  <p className="text-2xl font-bold text-blue-400">
                    {kpis.filter(kpi => kpi.achievement_percentage > 105).length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:scale-105 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">
                    {language === 'ar' ? 'يحتاج انتباه' : 'Needs Attention'}
                  </p>
                  <p className="text-2xl font-bold text-red-400">
                    {kpis.filter(kpi => kpi.achievement_percentage < 95).length}
                  </p>
                </div>
                <div className="p-3 rounded-xl bg-gradient-to-r from-red-500 to-red-600 shadow-lg">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tab Navigation */}
      <Card>
        <CardContent className="p-1">
          <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-1">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'overview'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Gauge className="w-4 h-4" />
              {t.overview}
            </button>

            <button
              onClick={() => setActiveTab('kpis')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'kpis'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Target className="w-4 h-4" />
              {t.kpis}
            </button>

            <button
              onClick={() => setActiveTab('analytics')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'analytics'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <BarChart3 className="w-4 h-4" />
              {t.analytics}
            </button>

            <button
              onClick={() => setActiveTab('reports')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'reports'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <FileText className="w-4 h-4" />
              {t.reports}
            </button>

            <button
              onClick={() => setActiveTab('insights')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'insights'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Lightbulb className="w-4 h-4" />
              {t.insights}
            </button>

            <button
              onClick={() => setActiveTab('predictions')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'predictions'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Brain className="w-4 h-4" />
              {t.predictions}
            </button>

            <button
              onClick={() => setActiveTab('alerts')}
              className={`py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 text-sm ${
                activeTab === 'alerts'
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              <Bell className="w-4 h-4" />
              {t.alerts}
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Content Area */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Executive Summary */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600">
                  <Gauge className="w-5 h-5 text-white" />
                </div>
                {t.executiveSummary}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">{kpis.filter(k => k.achievement_percentage >= 100).length}</div>
                  <div className="text-white/70 text-sm">{language === 'ar' ? 'أهداف محققة' : 'Goals Achieved'}</div>
                </div>
                <div className="text-center p-4 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-blue-400">{kpis.length}</div>
                  <div className="text-white/70 text-sm">{language === 'ar' ? 'إجمالي المؤشرات' : 'Total KPIs'}</div>
                </div>
                <div className="text-center p-4 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-400">{categories.length}</div>
                  <div className="text-white/70 text-sm">{language === 'ar' ? 'الفئات النشطة' : 'Active Categories'}</div>
                </div>
                <div className="text-center p-4 bg-white/5 rounded-lg">
                  <div className="text-2xl font-bold text-red-400">{alerts.length}</div>
                  <div className="text-white/70 text-sm">{language === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts'}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-green-600">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                {t.quickActions}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  onClick={() => setActiveTab('kpis')}
                  className="h-20 flex flex-col gap-2 bg-blue-500/20 hover:bg-blue-500/30 border-blue-400/30"
                >
                  <Target className="w-6 h-6" />
                  <span className="text-sm">{language === 'ar' ? 'إدارة المؤشرات' : 'Manage KPIs'}</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('reports')}
                  className="h-20 flex flex-col gap-2 bg-purple-500/20 hover:bg-purple-500/30 border-purple-400/30"
                >
                  <FileText className="w-6 h-6" />
                  <span className="text-sm">{language === 'ar' ? 'إنشاء تقرير' : 'Create Report'}</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('insights')}
                  className="h-20 flex flex-col gap-2 bg-yellow-500/20 hover:bg-yellow-500/30 border-yellow-400/30"
                >
                  <Lightbulb className="w-6 h-6" />
                  <span className="text-sm">{language === 'ar' ? 'عرض الرؤى' : 'View Insights'}</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('alerts')}
                  className="h-20 flex flex-col gap-2 bg-red-500/20 hover:bg-red-500/30 border-red-400/30"
                >
                  <Bell className="w-6 h-6" />
                  <span className="text-sm">{language === 'ar' ? 'إدارة التنبيهات' : 'Manage Alerts'}</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Performance Trends */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <KPIPerformanceChart />
            <CategoryDistributionChart />
          </div>
        </div>
      )}

      {activeTab === 'kpis' && (
        <CrudTable
          title={language === 'ar' ? 'مؤشرات الأداء الرئيسية' : 'Key Performance Indicators'}
          data={kpis}
          columns={columns}
          actions={actions}
          filters={filterOptions}
          loading={loading}
          searchPlaceholder={language === 'ar' ? 'البحث في المؤشرات...' : 'Search KPIs...'}
          language={language}
          onCreate={handleCreate}
          onRefresh={refresh}
          onExport={handleExport}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          activeFilters={filters as Record<string, string>}
          onFilterChange={setFilters}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={setSorting}
        />
      )}

      {/* Analytics Tab with Charts */}
      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Charts Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <KPIPerformanceChart />
            <CategoryDistributionChart />
          </div>

          {/* Trend Analysis Chart */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600">
                  <LineChart className="w-5 h-5 text-white" />
                </div>
                {language === 'ar' ? 'تحليل الاتجاهات' : 'Trend Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RechartsLineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis
                    dataKey="name"
                    stroke="rgba(255,255,255,0.7)"
                    fontSize={12}
                  />
                  <YAxis stroke="rgba(255,255,255,0.7)" fontSize={12} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                    name={language === 'ar' ? 'القيمة الحالية' : 'Current Value'}
                  />
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke="#10b981"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                    name={language === 'ar' ? 'الهدف' : 'Target'}
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'reports' && (
        <div className="space-y-6">
          {/* Report Templates */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                {t.reportTemplates}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { name: language === 'ar' ? 'تقرير الأداء الشهري' : 'Monthly Performance Report', icon: BarChart3, color: 'blue' },
                  { name: language === 'ar' ? 'تقرير المؤشرات الرئيسية' : 'KPI Summary Report', icon: Target, color: 'green' },
                  { name: language === 'ar' ? 'تقرير التحليل المقارن' : 'Comparative Analysis Report', icon: TrendIcon, color: 'purple' },
                  { name: language === 'ar' ? 'تقرير التنبؤات' : 'Forecast Report', icon: Brain, color: 'orange' },
                  { name: language === 'ar' ? 'تقرير مخصص' : 'Custom Report', icon: Settings, color: 'gray' },
                  { name: language === 'ar' ? 'تقرير تنفيذي' : 'Executive Report', icon: Star, color: 'yellow' }
                ].map((template, index) => (
                  <Card key={index} className="hover:scale-105 transition-all duration-300 cursor-pointer bg-white/5 hover:bg-white/10">
                    <CardContent className="p-6 text-center">
                      <div className={`p-3 rounded-xl bg-gradient-to-r from-${template.color}-500 to-${template.color}-600 shadow-lg mx-auto w-fit mb-4`}>
                        <template.icon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-white font-medium mb-2">{template.name}</h3>
                      <Button size="sm" className="w-full">
                        {t.generateReport}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Report Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="hover:scale-105 transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-green-500 to-green-600">
                    <PlayCircle className="w-5 h-5 text-white" />
                  </div>
                  {t.generateReport}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-white/70 text-sm mb-2 block">
                    {language === 'ar' ? 'نوع التقرير' : 'Report Type'}
                  </label>
                  <Select>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <SelectValue placeholder={language === 'ar' ? 'اختر نوع التقرير' : 'Select report type'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="performance">{language === 'ar' ? 'تقرير الأداء' : 'Performance Report'}</SelectItem>
                      <SelectItem value="kpi">{language === 'ar' ? 'تقرير المؤشرات' : 'KPI Report'}</SelectItem>
                      <SelectItem value="custom">{language === 'ar' ? 'تقرير مخصص' : 'Custom Report'}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-white/70 text-sm mb-2 block">{t.fromDate}</label>
                    <Input type="date" className="bg-white/10 border-white/20 text-white" />
                  </div>
                  <div>
                    <label className="text-white/70 text-sm mb-2 block">{t.toDate}</label>
                    <Input type="date" className="bg-white/10 border-white/20 text-white" />
                  </div>
                </div>
                <Button className="w-full bg-green-500 hover:bg-green-600">
                  <Download className="w-4 h-4 mr-2" />
                  {t.generateReport}
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:scale-105 transition-all duration-300">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600">
                    <Clock className="w-5 h-5 text-white" />
                  </div>
                  {t.scheduleReport}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-white/70 text-sm mb-2 block">
                    {language === 'ar' ? 'تكرار التقرير' : 'Report Frequency'}
                  </label>
                  <Select>
                    <SelectTrigger className="bg-white/10 border-white/20 text-white">
                      <SelectValue placeholder={language === 'ar' ? 'اختر التكرار' : 'Select frequency'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">{language === 'ar' ? 'يومي' : 'Daily'}</SelectItem>
                      <SelectItem value="weekly">{language === 'ar' ? 'أسبوعي' : 'Weekly'}</SelectItem>
                      <SelectItem value="monthly">{language === 'ar' ? 'شهري' : 'Monthly'}</SelectItem>
                      <SelectItem value="quarterly">{language === 'ar' ? 'ربع سنوي' : 'Quarterly'}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-white/70 text-sm mb-2 block">
                    {language === 'ar' ? 'البريد الإلكتروني' : 'Email Recipients'}
                  </label>
                  <Input
                    placeholder={language === 'ar' ? 'أدخل عناوين البريد الإلكتروني' : 'Enter email addresses'}
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>
                <Button className="w-full bg-blue-500 hover:bg-blue-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  {t.scheduleReport}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-6">
          {/* AI Insights */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-yellow-500 to-yellow-600">
                  <Lightbulb className="w-5 h-5 text-white" />
                </div>
                {t.aiInsights}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    type: 'opportunity',
                    icon: TrendingUp,
                    color: 'green',
                    title: language === 'ar' ? 'فرصة تحسين' : 'Improvement Opportunity',
                    description: language === 'ar'
                      ? 'يمكن تحسين مؤشر رضا العملاء بنسبة 15% من خلال تحسين وقت الاستجابة'
                      : 'Customer satisfaction can be improved by 15% through better response time'
                  },
                  {
                    type: 'risk',
                    icon: AlertTriangle,
                    color: 'red',
                    title: language === 'ar' ? 'تحذير من مخاطر' : 'Risk Alert',
                    description: language === 'ar'
                      ? 'انخفاض في إنتاجية الموظفين قد يؤثر على الأهداف الربعية'
                      : 'Declining employee productivity may impact quarterly targets'
                  },
                  {
                    type: 'recommendation',
                    icon: Star,
                    color: 'blue',
                    title: language === 'ar' ? 'توصية استراتيجية' : 'Strategic Recommendation',
                    description: language === 'ar'
                      ? 'ينصح بزيادة الاستثمار في التدريب لتحسين الأداء العام'
                      : 'Recommend increasing training investment to improve overall performance'
                  }
                ].map((insight, index) => (
                  <div key={index} className="flex items-start gap-4 p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-all">
                    <div className={`p-2 rounded-lg bg-gradient-to-r from-${insight.color}-500 to-${insight.color}-600`}>
                      <insight.icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-medium mb-1">{insight.title}</h4>
                      <p className="text-white/70 text-sm">{insight.description}</p>
                    </div>
                    <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'predictions' && (
        <div className="space-y-6">
          {/* Predictive Models */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                {t.forecastModels}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-white font-medium">{t.trendPrediction}</h4>
                  <div className="space-y-3">
                    {kpis.slice(0, 3).map((kpi, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div>
                          <div className="text-white text-sm font-medium">
                            {language === 'ar' ? kpi.name_ar : kpi.name}
                          </div>
                          <div className="text-white/60 text-xs">
                            {language === 'ar' ? 'التوقع للشهر القادم' : 'Next month prediction'}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-white font-medium">
                            {(kpi.current_value * (1 + (Math.random() * 0.2 - 0.1))).toFixed(0)}
                          </div>
                          <div className={`text-xs ${Math.random() > 0.5 ? 'text-green-400' : 'text-red-400'}`}>
                            {Math.random() > 0.5 ? '+' : '-'}{(Math.random() * 10).toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-white font-medium">{t.scenarioAnalysis}</h4>
                  <div className="space-y-3">
                    {[
                      { scenario: language === 'ar' ? 'السيناريو المتفائل' : 'Optimistic Scenario', probability: 35, impact: '+12%' },
                      { scenario: language === 'ar' ? 'السيناريو المتوقع' : 'Expected Scenario', probability: 50, impact: '+5%' },
                      { scenario: language === 'ar' ? 'السيناريو المتشائم' : 'Pessimistic Scenario', probability: 15, impact: '-3%' }
                    ].map((scenario, index) => (
                      <div key={index} className="p-3 bg-white/5 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white text-sm font-medium">{scenario.scenario}</span>
                          <span className="text-white/70 text-xs">{scenario.probability}%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <div className="w-full bg-white/10 rounded-full h-2 mr-3">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${scenario.probability}%` }}
                            />
                          </div>
                          <span className={`text-sm font-medium ${scenario.impact.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                            {scenario.impact}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'alerts' && (
        <div className="space-y-6">
          {/* Active Alerts */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-red-500 to-red-600">
                  <Bell className="w-5 h-5 text-white" />
                </div>
                {t.activeAlerts}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-medium mb-2">
                      {language === 'ar' ? 'لا توجد تنبيهات نشطة' : 'No Active Alerts'}
                    </h3>
                    <p className="text-white/60">
                      {language === 'ar' ? 'جميع المؤشرات تعمل بشكل طبيعي' : 'All indicators are performing normally'}
                    </p>
                  </div>
                ) : (
                  alerts.map((alert, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 bg-white/5 rounded-lg border-l-4 border-red-500">
                      <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="text-white font-medium mb-1">{alert.message}</h4>
                        <p className="text-white/70 text-sm mb-2">{alert.message_ar}</p>
                        <div className="flex items-center gap-4 text-xs text-white/50">
                          <span>{alert.created_at}</span>
                          <span className={`px-2 py-1 rounded ${
                            alert.severity === 'HIGH' ? 'bg-red-500/20 text-red-400' :
                            alert.severity === 'MEDIUM' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-blue-500/20 text-blue-400'
                          }`}>
                            {alert.severity}
                          </span>
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                        {language === 'ar' ? 'حل' : 'Resolve'}
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Alert Rules */}
          <Card className="hover:scale-105 transition-all duration-300">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <div className="p-2 rounded-lg bg-gradient-to-r from-orange-500 to-orange-600">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                {t.alertRules}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button className="w-full bg-orange-500 hover:bg-orange-600">
                  <Plus className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'إضافة قاعدة تنبيه جديدة' : 'Add New Alert Rule'}
                </Button>

                <div className="space-y-3">
                  {[
                    { name: language === 'ar' ? 'انخفاض الأداء' : 'Performance Drop', condition: '< 90%', status: 'active' },
                    { name: language === 'ar' ? 'تجاوز الهدف' : 'Target Exceeded', condition: '> 110%', status: 'active' },
                    { name: language === 'ar' ? 'عدم تحديث البيانات' : 'Data Not Updated', condition: '> 24h', status: 'inactive' }
                  ].map((rule, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <div>
                        <div className="text-white text-sm font-medium">{rule.name}</div>
                        <div className="text-white/60 text-xs">{rule.condition}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={rule.status === 'active' ? 'default' : 'secondary'}>
                          {rule.status === 'active' ? (language === 'ar' ? 'نشط' : 'Active') : (language === 'ar' ? 'غير نشط' : 'Inactive')}
                        </Badge>
                        <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                          <Edit className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSave}
        title={
          modalMode === 'create'
            ? (language === 'ar' ? 'إنشاء مؤشر جديد' : 'Create New KPI')
            : modalMode === 'edit'
            ? (language === 'ar' ? 'تعديل المؤشر' : 'Edit KPI')
            : (language === 'ar' ? 'عرض المؤشر' : 'View KPI')
        }
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
        mode={modalMode}
      />
    </div>
  )
}
