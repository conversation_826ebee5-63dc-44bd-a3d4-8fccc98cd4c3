/**
 * Announcements Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Megaphone,
  Calendar,
  User,
  AlertTriangle,
  Info,
  CheckCircle,
  Eye,
  Edit,
  Trash2,
  <PERSON><PERSON>,
  <PERSON>,
  // Clock, // TODO: Add time-based announcement scheduling
  Target
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { announcementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface AnnouncementsProps {
  language: 'ar' | 'en'
}

interface Announcement {
  id: number
  title: string
  titleAr: string
  content: string
  contentAr: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  targetAudience: string
  publishDate: string
  expiryDate: string
  status: 'draft' | 'published' | 'scheduled' | 'expired'
  publishedBy: string
  views: number
  readBy: number
  isPinned: boolean
}

const translations = {
  ar: {
    announcements: 'الإعلانات',
    addAnnouncement: 'إضافة إعلان',
    editAnnouncement: 'تعديل الإعلان',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الإعلان؟',
    searchPlaceholder: 'البحث في الإعلانات...',
    title: 'العنوان',
    content: 'المحتوى',
    priority: 'الأولوية',
    targetAudience: 'الجمهور المستهدف',
    publishDate: 'تاريخ النشر',
    expiryDate: 'تاريخ الانتهاء',
    status: 'الحالة',
    publishedBy: 'نشر بواسطة',
    views: 'المشاهدات',
    readBy: 'قرأ بواسطة',
    isPinned: 'مثبت',
    published: 'منشور',
    draft: 'مسودة',
    expired: 'منتهي الصلاحية',
    scheduled: 'مجدول',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل',
    audiences: {
      allEmployees: 'جميع الموظفين',
      management: 'الإدارة',
      hrTeam: 'فريق الموارد البشرية',
      itTeam: 'فريق تقنية المعلومات',
      financeTeam: 'فريق المالية',
      salesTeam: 'فريق المبيعات'
    }
  },
  en: {
    announcements: 'Announcements',
    addAnnouncement: 'Add Announcement',
    editAnnouncement: 'Edit Announcement',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this announcement?',
    searchPlaceholder: 'Search announcements...',
    title: 'Title',
    content: 'Content',
    priority: 'Priority',
    targetAudience: 'Target Audience',
    publishDate: 'Publish Date',
    expiryDate: 'Expiry Date',
    status: 'Status',
    publishedBy: 'Published By',
    views: 'Views',
    readBy: 'Read By',
    isPinned: 'Pinned',
    published: 'Published',
    draft: 'Draft',
    expired: 'Expired',
    scheduled: 'Scheduled',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent',
    audiences: {
      allEmployees: 'All Employees',
      management: 'Management',
      hrTeam: 'HR Team',
      itTeam: 'IT Team',
      financeTeam: 'Finance Team',
      salesTeam: 'Sales Team'
    }
  }
}

export default function Announcements({ language }: AnnouncementsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: announcements,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Announcement>({
    service: announcementService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      case 'medium':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'expired':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Announcement>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: Announcement) => (
        <div className="flex items-center gap-2">
          {getPriorityIcon(item.priority)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.contentAr?.substring(0, 50) + '...' : item.content?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.isPinned && <Pin className="h-4 w-4 text-yellow-400" />}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Announcement) => (
        <Badge variant="outline" className="text-white border-white/20">
          {String(t[item.priority as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'targetAudience',
      label: t.targetAudience,
      render: (item: Announcement) => (
        <div className="flex items-center gap-1">
          <Target className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {t.audiences[item.targetAudience as keyof typeof t.audiences] || item.targetAudience}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Announcement) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'publishDate',
      label: t.publishDate,
      sortable: true,
      render: (item: Announcement) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.publishDate}</span>
        </div>
      )
    },
    {
      key: 'publishedBy',
      label: t.publishedBy,
      render: (item: Announcement) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.publishedBy}</span>
        </div>
      )
    },
    {
      key: 'views',
      label: t.views,
      sortable: true,
      render: (item: Announcement) => (
        <div className="flex items-center gap-1">
          <Eye className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.views}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Announcement>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Announcement) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Announcement) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Announcement) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.published, value: 'published' },
        { label: t.draft, value: 'draft' },
        { label: t.scheduled, value: 'scheduled' },
        { label: t.expired, value: 'expired' }
      ]
    },
    {
      key: 'targetAudience',
      label: t.targetAudience,
      options: [
        { label: t.audiences.allEmployees, value: 'allEmployees' },
        { label: t.audiences.management, value: 'management' },
        { label: t.audiences.hrTeam, value: 'hrTeam' },
        { label: t.audiences.itTeam, value: 'itTeam' },
        { label: t.audiences.financeTeam, value: 'financeTeam' },
        { label: t.audiences.salesTeam, value: 'salesTeam' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'content',
      label: t.content,
      type: 'textarea',
      required: true
    },
    {
      name: 'contentAr',
      label: t.content + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'targetAudience',
      label: t.targetAudience,
      type: 'select',
      required: true,
      options: [
        { label: t.audiences.allEmployees, value: 'allEmployees' },
        { label: t.audiences.management, value: 'management' },
        { label: t.audiences.hrTeam, value: 'hrTeam' },
        { label: t.audiences.itTeam, value: 'itTeam' },
        { label: t.audiences.financeTeam, value: 'financeTeam' },
        { label: t.audiences.salesTeam, value: 'salesTeam' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.published, value: 'published' },
        { label: t.draft, value: 'draft' },
        { label: t.scheduled, value: 'scheduled' }
      ]
    },
    {
      name: 'publishDate',
      label: t.publishDate,
      type: 'date',
      required: true
    },
    {
      name: 'expiryDate',
      label: t.expiryDate,
      type: 'date'
    },
    {
      name: 'isPinned',
      label: t.isPinned,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Announcement>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.announcements}
        data={announcements}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addAnnouncement : modalMode === 'edit' ? t.editAnnouncement : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
