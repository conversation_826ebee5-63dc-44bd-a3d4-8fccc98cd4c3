/**
 * Documents Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  Upload,
  Download,
  Eye,
  Edit,
  Trash2,
  Share,
  File,
  Image,
  Video,
  Calendar,
  User,
  Lock,
  Globe,
  Star,
  Tag,
  Folder
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { documentService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface DocumentsProps {
  language: 'ar' | 'en'
}

interface Document {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  fileType: string
  fileSize: string
  filePath: string
  category: string
  permissions: 'public' | 'private' | 'restricted'
  uploadDate: string
  uploadedBy: string
  lastModified: string
  version: string
  downloads: number
  views: number
  isStarred: boolean
  tags: string[]
}

const translations = {
  ar: {
    documents: 'المستندات',
    addDocument: 'إضافة مستند',
    editDocument: 'تعديل المستند',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المستند؟',
    searchPlaceholder: 'البحث في المستندات...',
    name: 'اسم المستند',
    description: 'الوصف',
    fileType: 'نوع الملف',
    fileSize: 'حجم الملف',
    filePath: 'مسار الملف',
    category: 'الفئة',
    permissions: 'الصلاحيات',
    uploadDate: 'تاريخ الرفع',
    uploadedBy: 'رفع بواسطة',
    lastModified: 'آخر تعديل',
    version: 'الإصدار',
    downloads: 'التحميلات',
    views: 'المشاهدات',
    isStarred: 'مميز',
    tags: 'العلامات',
    public: 'عام',
    private: 'خاص',
    restricted: 'مقيد',
    categories: {
      policies: 'السياسات',
      procedures: 'الإجراءات',
      forms: 'النماذج',
      reports: 'التقارير',
      contracts: 'العقود',
      presentations: 'العروض التقديمية',
      images: 'الصور',
      videos: 'الفيديوهات',
      archives: 'الأرشيف'
    }
  },
  en: {
    documents: 'Documents',
    addDocument: 'Add Document',
    editDocument: 'Edit Document',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this document?',
    searchPlaceholder: 'Search documents...',
    name: 'Document Name',
    description: 'Description',
    fileType: 'File Type',
    fileSize: 'File Size',
    filePath: 'File Path',
    category: 'Category',
    permissions: 'Permissions',
    uploadDate: 'Upload Date',
    uploadedBy: 'Uploaded By',
    lastModified: 'Last Modified',
    version: 'Version',
    downloads: 'Downloads',
    views: 'Views',
    isStarred: 'Starred',
    tags: 'Tags',
    public: 'Public',
    private: 'Private',
    restricted: 'Restricted',
    categories: {
      policies: 'Policies',
      procedures: 'Procedures',
      forms: 'Forms',
      reports: 'Reports',
      contracts: 'Contracts',
      presentations: 'Presentations',
      images: 'Images',
      videos: 'Videos',
      archives: 'Archives'
    }
  }
}

export default function Documents({ language }: DocumentsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: documents,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Document>({
    service: documentService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-400" />
      case 'docx':
      case 'doc':
        return <FileText className="h-4 w-4 text-blue-400" />
      case 'xlsx':
      case 'xls':
        return <FileText className="h-4 w-4 text-green-400" />
      case 'pptx':
      case 'ppt':
        return <FileText className="h-4 w-4 text-orange-400" />
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <Image className="h-4 w-4 text-purple-400" />
      case 'mp4':
      case 'avi':
        return <Video className="h-4 w-4 text-pink-400" />
      default:
        return <File className="h-4 w-4 text-gray-400" />
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'public':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'private':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'restricted':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'public':
        return <Globe className="h-3 w-3" />
      case 'private':
        return <Lock className="h-3 w-3" />
      case 'restricted':
        return <User className="h-3 w-3" />
      default:
        return <File className="h-3 w-3" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<Document>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center gap-2">
          {getFileIcon(item.fileType)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.isStarred && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
        </div>
      )
    },
    {
      key: 'fileType',
      label: t.fileType,
      sortable: true,
      render: (item: Document) => (
        <Badge variant="outline" className="text-white border-white/20">
          {item.fileType}
        </Badge>
      )
    },
    {
      key: 'fileSize',
      label: t.fileSize,
      render: (item: Document) => (
        <span className="text-white/80">{item.fileSize}</span>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center gap-1">
          <Folder className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {t.categories[item.category as keyof typeof t.categories] || item.category}
          </span>
        </div>
      )
    },
    {
      key: 'permissions',
      label: t.permissions,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center gap-1">
          {getPermissionIcon(item.permissions)}
          <Badge className={getPermissionColor(item.permissions)}>
            {String(t[item.permissions as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'uploadedBy',
      label: t.uploadedBy,
      render: (item: Document) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.uploadedBy}</span>
        </div>
      )
    },
    {
      key: 'uploadDate',
      label: t.uploadDate,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.uploadDate}</span>
        </div>
      )
    },
    {
      key: 'views',
      label: t.views,
      sortable: true,
      render: (item: Document) => (
        <div className="flex items-center gap-1">
          <Eye className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.views}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Document>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Document) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Document) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Document) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.policies, value: 'policies' },
        { label: t.categories.procedures, value: 'procedures' },
        { label: t.categories.forms, value: 'forms' },
        { label: t.categories.reports, value: 'reports' },
        { label: t.categories.contracts, value: 'contracts' },
        { label: t.categories.presentations, value: 'presentations' },
        { label: t.categories.images, value: 'images' },
        { label: t.categories.videos, value: 'videos' },
        { label: t.categories.archives, value: 'archives' }
      ]
    },
    {
      key: 'permissions',
      label: t.permissions,
      options: [
        { label: t.public, value: 'public' },
        { label: t.private, value: 'private' },
        { label: t.restricted, value: 'restricted' }
      ]
    },
    {
      key: 'fileType',
      label: t.fileType,
      options: [
        { label: 'PDF', value: 'pdf' },
        { label: 'Word', value: 'docx' },
        { label: 'Excel', value: 'xlsx' },
        { label: 'PowerPoint', value: 'pptx' },
        { label: 'Image', value: 'jpg' },
        { label: 'Video', value: 'mp4' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'fileType',
      label: t.fileType,
      type: 'text',
      required: true
    },
    {
      name: 'fileSize',
      label: t.fileSize,
      type: 'text',
      required: true
    },
    {
      name: 'filePath',
      label: t.filePath,
      type: 'text',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.categories.policies, value: 'policies' },
        { label: t.categories.procedures, value: 'procedures' },
        { label: t.categories.forms, value: 'forms' },
        { label: t.categories.reports, value: 'reports' },
        { label: t.categories.contracts, value: 'contracts' },
        { label: t.categories.presentations, value: 'presentations' },
        { label: t.categories.images, value: 'images' },
        { label: t.categories.videos, value: 'videos' },
        { label: t.categories.archives, value: 'archives' }
      ]
    },
    {
      name: 'permissions',
      label: t.permissions,
      type: 'select',
      required: true,
      options: [
        { label: t.public, value: 'public' },
        { label: t.private, value: 'private' },
        { label: t.restricted, value: 'restricted' }
      ]
    },
    {
      name: 'version',
      label: t.version,
      type: 'text',
      required: true
    },
    {
      name: 'tags',
      label: t.tags,
      type: 'text',
      placeholder: 'Comma-separated tags'
    },
    {
      name: 'isStarred',
      label: t.isStarred,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Document>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.documents}
        data={documents}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addDocument : modalMode === 'edit' ? t.editDocument : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
