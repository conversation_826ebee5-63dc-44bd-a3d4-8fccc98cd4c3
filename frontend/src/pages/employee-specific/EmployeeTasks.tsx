/**
 * Employee Tasks Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  CheckSquare,
  Clock,
  Calendar,
  User,
  Flag,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Target,
  // TrendingUp, // TODO: Add task performance trends
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { employeeTaskService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeTasksProps {
  language: 'ar' | 'en'
}

interface EmployeeTask {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  dueDate: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'inProgress' | 'completed' | 'overdue'
  project: string
  projectAr: string
  assignedBy: string
  assignedByAr: string
  progress: number
  estimatedHours: number
  spentHours: number
  startDate?: string
  completedDate?: string
}

const translations = {
  ar: {
    employeeTasks: 'مهامي - الموظف',
    addTask: 'إضافة مهمة',
    editTask: 'تعديل المهمة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المهمة؟',
    searchPlaceholder: 'البحث في المهام...',
    name: 'اسم المهمة',
    description: 'الوصف',
    dueDate: 'تاريخ الاستحقاق',
    priority: 'الأولوية',
    status: 'الحالة',
    project: 'المشروع',
    assignedBy: 'مُعين من',
    progress: 'التقدم',
    estimatedHours: 'الساعات المقدرة',
    spentHours: 'الساعات المنفقة',
    startDate: 'تاريخ البداية',
    completedDate: 'تاريخ الإكمال',
    pending: 'معلق',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    overdue: 'متأخر',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض'
  },
  en: {
    employeeTasks: 'My Tasks - Employee',
    addTask: 'Add Task',
    editTask: 'Edit Task',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this task?',
    searchPlaceholder: 'Search tasks...',
    name: 'Task Name',
    description: 'Description',
    dueDate: 'Due Date',
    priority: 'Priority',
    status: 'Status',
    project: 'Project',
    assignedBy: 'Assigned By',
    progress: 'Progress',
    estimatedHours: 'Estimated Hours',
    spentHours: 'Spent Hours',
    startDate: 'Start Date',
    completedDate: 'Completed Date',
    pending: 'Pending',
    inProgress: 'In Progress',
    completed: 'Completed',
    overdue: 'Overdue',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  }
}

export default function EmployeeTasks({ language }: EmployeeTasksProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tasks,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<EmployeeTask>({
    service: employeeTaskService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3 w-3" />
      case 'inProgress':
        return <Clock className="h-3 w-3 animate-pulse" />
      case 'pending':
        return <Target className="h-3 w-3" />
      case 'overdue':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Flag className="h-3 w-3" />
      case 'medium':
        return <Flag className="h-3 w-3" />
      case 'low':
        return <Flag className="h-3 w-3" />
      default:
        return <Flag className="h-3 w-3" />
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const isOverdue = (dueDate: string, status: string) => {
    if (status === 'completed') return false
    const due = new Date(dueDate)
    const today = new Date()
    return due < today
  }

  // Table columns configuration
  const columns: TableColumn<EmployeeTask>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <CheckSquare className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
          {isOverdue(item.dueDate, item.status) && (
            <AlertTriangle className="h-4 w-4 text-red-400 ml-1" />
          )}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          {getPriorityIcon(item.priority)}
          <Badge className={getPriorityColor(item.priority)}>
            {t[item.priority as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(item.progress)}`}
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'project',
      label: t.project,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <BarChart3 className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.projectAr : item.project}
          </span>
        </div>
      )
    },
    {
      key: 'assignedBy',
      label: t.assignedBy,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.assignedByAr : item.assignedBy}
          </span>
        </div>
      )
    },
    {
      key: 'dueDate',
      label: t.dueDate,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className={`text-sm ${isOverdue(item.dueDate, item.status) ? 'text-red-400' : 'text-white/80'}`}>
            {item.dueDate}
          </span>
        </div>
      )
    },
    {
      key: 'hours',
      label: 'Hours',
      render: (item: EmployeeTask) => (
        <div>
          <div className="text-white text-sm">
            {item.spentHours}h / {item.estimatedHours}h
          </div>
          <div className="text-white/60 text-xs">
            {Math.round((item.spentHours / item.estimatedHours) * 100)}% used
          </div>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<EmployeeTask>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: EmployeeTask) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: EmployeeTask) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Complete',
      icon: CheckCircle,
      onClick: async (item: EmployeeTask) => {
        if (item.status !== 'completed') {
          await updateItem(item.id, {
            ...item,
            status: 'completed',
            progress: 100,
            completedDate: new Date().toISOString().split('T')[0]
          })
        }
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20',
      condition: (item: EmployeeTask) => item.status !== 'completed'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: EmployeeTask) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.overdue, value: 'overdue' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'dueDate',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.overdue, value: 'overdue' }
      ]
    },
    {
      name: 'project',
      label: t.project,
      type: 'text',
      required: true
    },
    {
      name: 'projectAr',
      label: t.project + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedBy',
      label: t.assignedBy,
      type: 'text',
      required: true
    },
    {
      name: 'assignedByAr',
      label: t.assignedBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'estimatedHours',
      label: t.estimatedHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'spentHours',
      label: t.spentHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date'
    },
    {
      name: 'completedDate',
      label: t.completedDate,
      type: 'date'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<EmployeeTask>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('employee-tasks')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.employeeTasks}
        data={tasks}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addTask : modalMode === 'edit' ? t.editTask : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
