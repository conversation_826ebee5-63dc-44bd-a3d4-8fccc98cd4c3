/**
 * Training Programs Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  GraduationCap,
  Eye,
  Edit,
  Trash2,
  Users,
  Clock,
  Calendar,
  BookOpen,
  Award,
  TrendingUp,
  User,
  MapPin
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { trainingProgramService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TrainingProgramsProps {
  language: 'ar' | 'en'
}

interface TrainingProgram {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  instructor: string
  instructorAr: string
  duration: number
  startDate: string
  endDate: string
  maxParticipants: number
  enrolledParticipants: number
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'
  category: string
  categoryAr: string
  level: 'beginner' | 'intermediate' | 'advanced'
  cost: number
  location: string
  locationAr: string
  isOnline: boolean
}

const translations = {
  ar: {
    trainingPrograms: 'برامج التدريب',
    addProgram: 'إضافة برنامج',
    editProgram: 'تعديل البرنامج',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا البرنامج؟',
    searchPlaceholder: 'البحث في برامج التدريب...',
    title: 'عنوان البرنامج',
    description: 'الوصف',
    instructor: 'المدرب',
    duration: 'المدة (بالساعات)',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    maxParticipants: 'الحد الأقصى للمشاركين',
    enrolledParticipants: 'المشاركون المسجلون',
    status: 'الحالة',
    category: 'الفئة',
    level: 'المستوى',
    cost: 'التكلفة',
    location: 'المكان',
    isOnline: 'عبر الإنترنت',
    upcoming: 'قادم',
    ongoing: 'جاري',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    beginner: 'مبتدئ',
    intermediate: 'متوسط',
    advanced: 'متقدم',
    categories: {
      marketing: 'التسويق',
      management: 'الإدارة',
      finance: 'المالية',
      technology: 'التكنولوجيا',
      hr: 'الموارد البشرية',
      sales: 'المبيعات'
    }
  },
  en: {
    trainingPrograms: 'Training Programs',
    addProgram: 'Add Program',
    editProgram: 'Edit Program',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this program?',
    searchPlaceholder: 'Search training programs...',
    title: 'Program Title',
    description: 'Description',
    instructor: 'Instructor',
    duration: 'Duration (hours)',
    startDate: 'Start Date',
    endDate: 'End Date',
    maxParticipants: 'Max Participants',
    enrolledParticipants: 'Enrolled Participants',
    status: 'Status',
    category: 'Category',
    level: 'Level',
    cost: 'Cost',
    location: 'Location',
    isOnline: 'Online',
    upcoming: 'Upcoming',
    ongoing: 'Ongoing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    beginner: 'Beginner',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    categories: {
      marketing: 'Marketing',
      management: 'Management',
      finance: 'Finance',
      technology: 'Technology',
      hr: 'Human Resources',
      sales: 'Sales'
    }
  }
}

export default function TrainingPrograms({ language }: TrainingProgramsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: programs,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<TrainingProgram>({
    service: trainingProgramService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'ongoing': return 'bg-green-100 text-green-800 border-green-200'
      case 'completed': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'text-green-400'
      case 'intermediate': return 'text-yellow-400'
      case 'advanced': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<TrainingProgram>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-2">
          <GraduationCap className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.isOnline && <BookOpen className="h-4 w-4 text-green-400" />}
        </div>
      )
    },
    {
      key: 'instructor',
      label: t.instructor,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.instructorAr : item.instructor}
          </span>
        </div>
      )
    },
    {
      key: 'duration',
      label: t.duration,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{item.duration}h</span>
        </div>
      )
    },
    {
      key: 'participants',
      label: 'المشاركون',
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-blue-400" />
          <span className="text-white font-medium">
            {item.enrolledParticipants}/{item.maxParticipants}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: TrainingProgram) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'level',
      label: t.level,
      sortable: true,
      render: (item: TrainingProgram) => (
        <span className={`font-medium ${getLevelColor(item.level)}`}>
          {String(t[item.level as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'startDate',
      label: t.startDate,
      sortable: true,
      render: (item: TrainingProgram) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.startDate}</span>
        </div>
      )
    },
    {
      key: 'cost',
      label: t.cost,
      sortable: true,
      render: (item: TrainingProgram) => (
        <span className="text-white font-medium">{formatCurrency(item.cost)}</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.upcoming, value: 'upcoming' },
        { label: t.ongoing, value: 'ongoing' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      key: 'level',
      label: t.level,
      options: [
        { label: t.beginner, value: 'beginner' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.marketing, value: 'marketing' },
        { label: t.categories.management, value: 'management' },
        { label: t.categories.finance, value: 'finance' },
        { label: t.categories.technology, value: 'technology' },
        { label: t.categories.hr, value: 'hr' },
        { label: t.categories.sales, value: 'sales' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'instructor',
      label: t.instructor,
      type: 'text',
      required: true
    },
    {
      name: 'instructorAr',
      label: t.instructor + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'duration',
      label: t.duration,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'maxParticipants',
      label: t.maxParticipants,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'enrolledParticipants',
      label: t.enrolledParticipants,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.upcoming, value: 'upcoming' },
        { label: t.ongoing, value: 'ongoing' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'level',
      label: t.level,
      type: 'select',
      required: true,
      options: [
        { label: t.beginner, value: 'beginner' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' }
      ]
    },
    {
      name: 'cost',
      label: t.cost,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'isOnline',
      label: t.isOnline,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<TrainingProgram>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.trainingPrograms}
        data={programs}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProgram : modalMode === 'edit' ? t.editProgram : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
