/**
 * Certifications Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Award,
  Eye,
  Edit,
  Trash2,
  Download,
  Calendar,
  User,
  CheckCircle,
  Clock,
  Building,
  FileText
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { certificationService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CertificationsProps {
  language: 'ar' | 'en'
}

interface Certification {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  issuingOrganization: string
  issuingOrganizationAr: string
  employee: string
  employeeAr: string
  employeeId: string
  issueDate: string
  expiryDate: string
  certificateNumber: string
  status: 'active' | 'expired' | 'pending' | 'revoked'
  category: string
  categoryAr: string
  level: 'basic' | 'intermediate' | 'advanced' | 'expert'
  verificationUrl?: string
  attachmentUrl?: string
}

const translations = {
  ar: {
    certifications: 'الشهادات المهنية',
    addCertification: 'إضافة شهادة',
    editCertification: 'تعديل الشهادة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    download: 'تحميل',
    confirmDelete: 'هل أنت متأكد من حذف هذه الشهادة؟',
    searchPlaceholder: 'البحث في الشهادات...',
    name: 'اسم الشهادة',
    description: 'الوصف',
    issuingOrganization: 'الجهة المصدرة',
    employee: 'الموظف',
    employeeId: 'رقم الموظف',
    issueDate: 'تاريخ الإصدار',
    expiryDate: 'تاريخ الانتهاء',
    certificateNumber: 'رقم الشهادة',
    status: 'الحالة',
    category: 'الفئة',
    level: 'المستوى',
    verificationUrl: 'رابط التحقق',
    attachmentUrl: 'رابط المرفق',
    active: 'نشط',
    expired: 'منتهي',
    pending: 'معلق',
    revoked: 'ملغي',
    basic: 'أساسي',
    intermediate: 'متوسط',
    advanced: 'متقدم',
    expert: 'خبير',
    categories: {
      management: 'الإدارة',
      finance: 'المالية',
      marketing: 'التسويق',
      technology: 'التكنولوجيا',
      quality: 'الجودة',
      hr: 'الموارد البشرية'
    }
  },
  en: {
    certifications: 'Professional Certifications',
    addCertification: 'Add Certification',
    editCertification: 'Edit Certification',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    download: 'Download',
    confirmDelete: 'Are you sure you want to delete this certification?',
    searchPlaceholder: 'Search certifications...',
    name: 'Certification Name',
    description: 'Description',
    issuingOrganization: 'Issuing Organization',
    employee: 'Employee',
    employeeId: 'Employee ID',
    issueDate: 'Issue Date',
    expiryDate: 'Expiry Date',
    certificateNumber: 'Certificate Number',
    status: 'Status',
    category: 'Category',
    level: 'Level',
    verificationUrl: 'Verification URL',
    attachmentUrl: 'Attachment URL',
    active: 'Active',
    expired: 'Expired',
    pending: 'Pending',
    revoked: 'Revoked',
    basic: 'Basic',
    intermediate: 'Intermediate',
    advanced: 'Advanced',
    expert: 'Expert',
    categories: {
      management: 'Management',
      finance: 'Finance',
      marketing: 'Marketing',
      technology: 'Technology',
      quality: 'Quality',
      hr: 'Human Resources'
    }
  }
}

export default function Certifications({ language }: CertificationsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: certifications,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Certification>({
    service: certificationService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'expired': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'revoked': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic': return 'text-blue-400'
      case 'intermediate': return 'text-yellow-400'
      case 'advanced': return 'text-orange-400'
      case 'expert': return 'text-purple-400'
      default: return 'text-gray-400'
    }
  }

  const getDaysToExpiry = (expiryDate: string) => {
    const today = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  // Table columns configuration
  const columns: TableColumn<Certification>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Certification) => (
        <div className="flex items-center gap-2">
          <Award className="h-4 w-4 text-yellow-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60 flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {item.certificateNumber}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'employee',
      label: t.employee,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <div>
            <div className="text-white/80">
              {language === 'ar' ? item.employeeAr : item.employee}
            </div>
            <div className="text-xs text-white/50">{item.employeeId}</div>
          </div>
        </div>
      )
    },
    {
      key: 'issuingOrganization',
      label: t.issuingOrganization,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.issuingOrganizationAr : item.issuingOrganization}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Certification) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'level',
      label: t.level,
      sortable: true,
      render: (item: Certification) => (
        <span className={`font-medium ${getLevelColor(item.level)}`}>
          {String(t[item.level as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: Certification) => (
        <span className="text-white/80">
          {language === 'ar' ? item.categoryAr : item.category}
        </span>
      )
    },
    {
      key: 'issueDate',
      label: t.issueDate,
      sortable: true,
      render: (item: Certification) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.issueDate}</span>
        </div>
      )
    },
    {
      key: 'expiryDate',
      label: t.expiryDate,
      sortable: true,
      render: (item: Certification) => (
        <div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-orange-400" />
            <span className="text-white/80">{item.expiryDate}</span>
          </div>
          {(() => {
            const daysToExpiry = getDaysToExpiry(item.expiryDate)
            const isExpiringSoon = daysToExpiry <= 90 && daysToExpiry > 0
            return isExpiringSoon && (
              <div className="text-xs text-orange-400 mt-1">
                {daysToExpiry} days left
              </div>
            )
          })()}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: any) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.download,
      icon: Download,
      onClick: (item: any) => {
        if (item.attachmentUrl) {
          window.open(item.attachmentUrl, '_blank')
        }
      },
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: any) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.expired, value: 'expired' },
        { label: t.pending, value: 'pending' },
        { label: t.revoked, value: 'revoked' }
      ]
    },
    {
      key: 'level',
      label: t.level,
      options: [
        { label: t.basic, value: 'basic' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' },
        { label: t.expert, value: 'expert' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.management, value: 'management' },
        { label: t.categories.finance, value: 'finance' },
        { label: t.categories.marketing, value: 'marketing' },
        { label: t.categories.technology, value: 'technology' },
        { label: t.categories.quality, value: 'quality' },
        { label: t.categories.hr, value: 'hr' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'issuingOrganization',
      label: t.issuingOrganization,
      type: 'text',
      required: true
    },
    {
      name: 'issuingOrganizationAr',
      label: t.issuingOrganization + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employee',
      label: t.employee,
      type: 'text',
      required: true
    },
    {
      name: 'employeeAr',
      label: t.employee + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employeeId',
      label: t.employeeId,
      type: 'text',
      required: true
    },
    {
      name: 'issueDate',
      label: t.issueDate,
      type: 'date',
      required: true
    },
    {
      name: 'expiryDate',
      label: t.expiryDate,
      type: 'date',
      required: true
    },
    {
      name: 'certificateNumber',
      label: t.certificateNumber,
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.expired, value: 'expired' },
        { label: t.pending, value: 'pending' },
        { label: t.revoked, value: 'revoked' }
      ]
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'level',
      label: t.level,
      type: 'select',
      required: true,
      options: [
        { label: t.basic, value: 'basic' },
        { label: t.intermediate, value: 'intermediate' },
        { label: t.advanced, value: 'advanced' },
        { label: t.expert, value: 'expert' }
      ]
    },
    {
      name: 'verificationUrl',
      label: t.verificationUrl,
      type: 'text'
    },
    {
      name: 'attachmentUrl',
      label: t.attachmentUrl,
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Certification>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.certifications}
        data={certifications}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCertification : modalMode === 'edit' ? t.editCertification : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
