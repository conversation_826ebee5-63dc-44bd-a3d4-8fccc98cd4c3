/**
 * Inventory Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  MapPin,
  // Calendar, // TODO: Add inventory scheduling features
  Eye,
  Edit,
  Trash2,
  Barcode,
  TrendingDown
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { productService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface InventoryProps {
  language: 'ar' | 'en'
}

interface InventoryItem {
  id: number
  name: string
  name_ar?: string
  sku: string
  description?: string
  description_ar?: string
  category: number
  category_name?: string
  brand?: string
  brand_ar?: string
  unit_price: number
  cost_price?: number
  quantity_in_stock: number
  minimum_stock_level: number
  maximum_stock_level?: number
  reorder_point: number
  unit_of_measure: string
  unit_of_measure_ar?: string
  barcode?: string
  weight?: number
  dimensions?: string
  status: 'active' | 'inactive' | 'discontinued'
  supplier?: number
  supplier_name?: string
  location?: string
  location_ar?: string
  expiry_date?: string
  batch_number?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    inventory: 'المخزون',
    addItem: 'إضافة صنف',
    editItem: 'تعديل الصنف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الصنف؟',
    searchPlaceholder: 'البحث في المخزون...',
    itemName: 'اسم الصنف',
    description: 'الوصف',
    sku: 'رمز المنتج',
    category: 'الفئة',
    unitPrice: 'سعر الوحدة',
    currentStock: 'المخزون الحالي',
    reorderLevel: 'مستوى إعادة الطلب',
    location: 'الموقع',
    lastUpdated: 'آخر تحديث',
    status: 'الحالة',
    totalValue: 'القيمة الإجمالية',
    inStock: 'متوفر',
    lowStock: 'مخزون منخفض',
    outOfStock: 'نفد المخزون',
    categories: {
      electronics: 'إلكترونيات',
      furniture: 'أثاث',
      supplies: 'مستلزمات',
      equipment: 'معدات',
      food: 'طعام',
      clothing: 'ملابس'
    }
  },
  en: {
    inventory: 'Inventory',
    addItem: 'Add Item',
    editItem: 'Edit Item',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this item?',
    searchPlaceholder: 'Search inventory...',
    itemName: 'Item Name',
    description: 'Description',
    sku: 'SKU',
    category: 'Category',
    unitPrice: 'Unit Price',
    currentStock: 'Current Stock',
    reorderLevel: 'Reorder Level',
    location: 'Location',
    lastUpdated: 'Last Updated',
    status: 'Status',
    totalValue: 'Total Value',
    inStock: 'In Stock',
    lowStock: 'Low Stock',
    outOfStock: 'Out of Stock',
    categories: {
      electronics: 'Electronics',
      furniture: 'Furniture',
      supplies: 'Supplies',
      equipment: 'Equipment',
      food: 'Food',
      clothing: 'Clothing'
    }
  }
}

export default function Inventory({ language }: InventoryProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: inventoryItems,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<InventoryItem>({
    service: productService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inStock':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'lowStock':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'outOfStock':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'inStock':
        return <CheckCircle className="h-3 w-3" />
      case 'lowStock':
        return <AlertTriangle className="h-3 w-3" />
      case 'outOfStock':
        return <TrendingDown className="h-3 w-3" />
      default:
        return <Package className="h-3 w-3" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<InventoryItem>[] = [
    {
      key: 'name',
      label: t.itemName,
      sortable: true,
      render: (item: InventoryItem) => (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.name_ar : item.name}
            </div>
            <div className="text-sm text-white/60">{item.description}</div>
          </div>
        </div>
      )
    },
    {
      key: 'sku',
      label: t.sku,
      render: (item: InventoryItem) => (
        <div className="flex items-center gap-1">
          <Barcode className="h-3 w-3 text-blue-400" />
          <span className="text-white font-mono text-sm">{item.sku}</span>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: InventoryItem) => (
        <Badge variant="outline" className="text-white border-white/20">
          {item.category_name || 'Unknown'}
        </Badge>
      )
    },
    {
      key: 'quantity_in_stock',
      label: t.currentStock,
      sortable: true,
      render: (item: InventoryItem) => {
        const isLowStock = item.quantity_in_stock <= item.minimum_stock_level
        return (
          <div className={`flex items-center gap-2 ${isLowStock ? 'text-yellow-400' : 'text-white'}`}>
            {isLowStock && <AlertTriangle className="h-3 w-3" />}
            <span className="font-medium">{item.quantity_in_stock}</span>
          </div>
        )
      }
    },
    {
      key: 'unit_price',
      label: t.unitPrice,
      sortable: true,
      render: (item: InventoryItem) => (
        <span className="text-white font-medium">
          {new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(item.unit_price)}
        </span>
      )
    },
    {
      key: 'total_value',
      label: t.totalValue,
      sortable: true,
      render: (item: InventoryItem) => {
        const totalValue = item.unit_price * item.quantity_in_stock
        return (
          <span className="text-white font-medium">
            {new Intl.NumberFormat('ar-SA', {
              style: 'currency',
              currency: 'SAR'
            }).format(totalValue)}
          </span>
        )
      }
    },
    {
      key: 'location',
      label: t.location,
      render: (item: InventoryItem) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.location}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: InventoryItem) => {
        // Calculate dynamic status based on stock levels
        let dynamicStatus = 'inStock'
        if (item.quantity_in_stock === 0) {
          dynamicStatus = 'outOfStock'
        } else if (item.quantity_in_stock <= item.minimum_stock_level) {
          dynamicStatus = 'lowStock'
        }

        return (
          <Badge className={getStatusColor(dynamicStatus)}>
            {getStatusIcon(dynamicStatus)}
            <span className="ml-1">{String(t[dynamicStatus as keyof typeof t])}</span>
          </Badge>
        )
      }
    }
  ]

  // Table actions configuration
  const actions: TableAction<InventoryItem>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: InventoryItem) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: InventoryItem) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: InventoryItem) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: 'All Categories', value: '' },
        { label: 'Electronics', value: '1' },
        { label: 'Clothing', value: '2' },
        { label: 'Books', value: '3' },
        { label: 'Home & Garden', value: '4' },
        { label: 'Sports', value: '5' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: 'All Status', value: '' },
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Discontinued', value: 'discontinued' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.itemName,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: t.itemName + ' (عربي)',
      type: 'text'
    },
    {
      name: 'sku',
      label: t.sku,
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea'
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: 'Electronics', value: '1' },
        { label: 'Clothing', value: '2' },
        { label: 'Books', value: '3' },
        { label: 'Home & Garden', value: '4' },
        { label: 'Sports', value: '5' }
      ]
    },
    {
      name: 'brand',
      label: 'Brand',
      type: 'text'
    },
    {
      name: 'unit_price',
      label: t.unitPrice,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'cost_price',
      label: 'Cost Price',
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'quantity_in_stock',
      label: t.currentStock,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'minimum_stock_level',
      label: t.reorderLevel,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'reorder_point',
      label: 'Reorder Point',
      type: 'number',
      min: 0
    },
    {
      name: 'unit_of_measure',
      label: 'Unit of Measure',
      type: 'text'
    },
    {
      name: 'location',
      label: t.location,
      type: 'text'
    },
    {
      name: 'barcode',
      label: 'Barcode',
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Inactive', value: 'inactive' },
        { label: 'Discontinued', value: 'discontinued' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<InventoryItem>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for inventory reports
        const response = await fetch(`http://localhost:8001/api/pdf/generate/asset-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `inventory-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.inventory}
        data={inventoryItems}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addItem : modalMode === 'edit' ? t.editItem : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
