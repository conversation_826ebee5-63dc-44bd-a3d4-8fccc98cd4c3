/**
 * Finance Budgets Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calculator,
  Target,
  Eye,
  Edit,
  Trash2,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  Building
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { financeBudgetService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface FinanceBudgetsProps {
  language: 'ar' | 'en'
}

interface FinanceBudget {
  id: number
  name: string
  nameAr: string
  department: string
  departmentAr: string
  allocated: number
  spent: number
  remaining: number
  utilization: number
  status: 'active' | 'completed' | 'atRisk' | 'overBudget'
  period: string
  category: 'operational' | 'marketing' | 'capital' | 'research'
  startDate: string
  endDate: string
  approvedBy: string
  approvedByAr: string
  notes?: string
  notesAr?: string
}

const translations = {
  ar: {
    financeBudgets: 'إدارة الميزانيات - المالية',
    addBudget: 'إضافة ميزانية',
    editBudget: 'تعديل الميزانية',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه الميزانية؟',
    searchPlaceholder: 'البحث في الميزانيات...',
    name: 'اسم الميزانية',
    department: 'القسم',
    allocated: 'المخصص',
    spent: 'المنفق',
    remaining: 'المتبقي',
    utilization: 'الاستخدام',
    status: 'الحالة',
    period: 'الفترة',
    category: 'الفئة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    approvedBy: 'معتمد من',
    notes: 'ملاحظات',
    active: 'نشط',
    completed: 'مكتمل',
    atRisk: 'في خطر',
    overBudget: 'تجاوز الميزانية',
    operational: 'تشغيلي',
    marketing: 'تسويق',
    capital: 'رأسمالي',
    research: 'بحث وتطوير'
  },
  en: {
    financeBudgets: 'Finance Budgets',
    addBudget: 'Add Budget',
    editBudget: 'Edit Budget',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this budget?',
    searchPlaceholder: 'Search budgets...',
    name: 'Budget Name',
    department: 'Department',
    allocated: 'Allocated',
    spent: 'Spent',
    remaining: 'Remaining',
    utilization: 'Utilization',
    status: 'Status',
    period: 'Period',
    category: 'Category',
    startDate: 'Start Date',
    endDate: 'End Date',
    approvedBy: 'Approved By',
    notes: 'Notes',
    active: 'Active',
    completed: 'Completed',
    atRisk: 'At Risk',
    overBudget: 'Over Budget',
    operational: 'Operational',
    marketing: 'Marketing',
    capital: 'Capital',
    research: 'Research & Development'
  }
}

export default function FinanceBudgets({ language }: FinanceBudgetsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: budgets,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FinanceBudget>({
    service: financeBudgetService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'atRisk':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'overBudget':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'operational':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'marketing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'capital':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'research':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 95) return 'text-red-400'
    if (utilization >= 85) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getUtilizationBgColor = (utilization: number) => {
    if (utilization >= 95) return 'bg-red-500'
    if (utilization >= 85) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<FinanceBudget>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <DollarSign className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">{item.period}</div>
          </div>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : item.department}
          </span>
        </div>
      )
    },
    {
      key: 'allocated',
      label: t.allocated,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="text-white font-medium">
          {formatCurrency(item.allocated)}
        </div>
      )
    },
    {
      key: 'spent',
      label: t.spent,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="text-white/80">
          {formatCurrency(item.spent)}
        </div>
      )
    },
    {
      key: 'remaining',
      label: t.remaining,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="text-white/80">
          {formatCurrency(item.remaining)}
        </div>
      )
    },
    {
      key: 'utilization',
      label: t.utilization,
      sortable: true,
      render: (item: FinanceBudget) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getUtilizationBgColor(item.utilization)}`}
              style={{ width: `${Math.min(item.utilization, 100)}%` }}
            ></div>
          </div>
          <span className={`text-sm font-medium ${getUtilizationColor(item.utilization)}`}>
            {item.utilization.toFixed(1)}%
          </span>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: FinanceBudget) => (
        <Badge className={getCategoryColor(item.category)}>
          {t[item.category as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: FinanceBudget) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FinanceBudget>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: FinanceBudget) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: FinanceBudget) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: FinanceBudget) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.completed, value: 'completed' },
        { label: t.atRisk, value: 'atRisk' },
        { label: t.overBudget, value: 'overBudget' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.operational, value: 'operational' },
        { label: t.marketing, value: 'marketing' },
        { label: t.capital, value: 'capital' },
        { label: t.research, value: 'research' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'allocated',
      label: t.allocated,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'spent',
      label: t.spent,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'period',
      label: t.period,
      type: 'text',
      required: true,
      placeholder: 'e.g., Q1 2024, 2024, Jan 2024'
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.operational, value: 'operational' },
        { label: t.marketing, value: 'marketing' },
        { label: t.capital, value: 'capital' },
        { label: t.research, value: 'research' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.completed, value: 'completed' },
        { label: t.atRisk, value: 'atRisk' },
        { label: t.overBudget, value: 'overBudget' }
      ]
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'approvedBy',
      label: t.approvedBy,
      type: 'text',
      required: true
    },
    {
      name: 'approvedByAr',
      label: t.approvedBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    },
    {
      name: 'notesAr',
      label: t.notes + ' (عربي)',
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FinanceBudget>) => {
    try {
      // Calculate remaining and utilization based on allocated and spent
      if (data.allocated && data.spent) {
        data.remaining = data.allocated - data.spent
        data.utilization = (data.spent / data.allocated) * 100
      }

      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.financeBudgets}
        data={budgets}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addBudget : modalMode === 'edit' ? t.editBudget : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
