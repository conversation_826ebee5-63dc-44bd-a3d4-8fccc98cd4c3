/**
 * Finance Customers Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  DollarSign,
  CreditCard,
  Eye,
  Edit,
  Trash2,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  FileText,
  Calculator,
  User,
  Building
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { financeCustomerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface FinanceCustomersProps {
  language: 'ar' | 'en'
}

interface FinanceCustomer {
  id: number
  name: string
  nameAr: string
  creditLimit: number
  currentBalance: number
  availableCredit: number
  lastPayment: string
  lastPaymentAmount: number
  paymentTerms: 'net30' | 'net60' | 'prepaid' | 'cod'
  paymentTermsAr: string
  creditStatus: 'good' | 'warning' | 'critical' | 'blocked'
  totalRevenue: number
  outstandingAmount: number
  overdueAmount: number
  invoiceCount: number
  avgPaymentDays: number
  customerType: 'corporate' | 'individual' | 'government'
  industry?: string
  industryAr?: string
  contactPerson?: string
  contactPersonAr?: string
}

const translations = {
  ar: {
    financeCustomers: 'العملاء - منظور مالي',
    addCustomer: 'إضافة عميل',
    editCustomer: 'تعديل العميل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا العميل؟',
    searchPlaceholder: 'البحث في العملاء...',
    name: 'اسم العميل',
    creditLimit: 'حد الائتمان',
    currentBalance: 'الرصيد الحالي',
    availableCredit: 'الائتمان المتاح',
    lastPayment: 'آخر دفعة',
    lastPaymentAmount: 'مبلغ آخر دفعة',
    paymentTerms: 'شروط الدفع',
    creditStatus: 'حالة الائتمان',
    totalRevenue: 'إجمالي الإيرادات',
    outstandingAmount: 'المبلغ المعلق',
    overdueAmount: 'المبلغ المتأخر',
    invoiceCount: 'عدد الفواتير',
    avgPaymentDays: 'متوسط أيام الدفع',
    customerType: 'نوع العميل',
    industry: 'الصناعة',
    contactPerson: 'الشخص المسؤول',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    blocked: 'محظور',
    net30: 'صافي 30 يوم',
    net60: 'صافي 60 يوم',
    prepaid: 'مدفوع مقدماً',
    cod: 'الدفع عند التسليم',
    corporate: 'شركة',
    individual: 'فرد',
    government: 'حكومي'
  },
  en: {
    financeCustomers: 'Finance Customers',
    addCustomer: 'Add Customer',
    editCustomer: 'Edit Customer',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this customer?',
    searchPlaceholder: 'Search customers...',
    name: 'Customer Name',
    creditLimit: 'Credit Limit',
    currentBalance: 'Current Balance',
    availableCredit: 'Available Credit',
    lastPayment: 'Last Payment',
    lastPaymentAmount: 'Last Payment Amount',
    paymentTerms: 'Payment Terms',
    creditStatus: 'Credit Status',
    totalRevenue: 'Total Revenue',
    outstandingAmount: 'Outstanding Amount',
    overdueAmount: 'Overdue Amount',
    invoiceCount: 'Invoice Count',
    avgPaymentDays: 'Avg Payment Days',
    customerType: 'Customer Type',
    industry: 'Industry',
    contactPerson: 'Contact Person',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    blocked: 'Blocked',
    net30: 'Net 30',
    net60: 'Net 60',
    prepaid: 'Prepaid',
    cod: 'COD',
    corporate: 'Corporate',
    individual: 'Individual',
    government: 'Government'
  }
}

export default function FinanceCustomers({ language }: FinanceCustomersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FinanceCustomer>({
    service: financeCustomerService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getCreditStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'blocked':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCreditStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />
      case 'blocked':
        return <Clock className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getCustomerTypeColor = (type: string) => {
    switch (type) {
      case 'corporate':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'individual':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'government':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCreditUtilization = (current: number, limit: number) => {
    return Math.round((current / limit) * 100)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<FinanceCustomer>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
            {(language === 'ar' ? item.nameAr : item.name).charAt(0)}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.contactPersonAr : item.contactPerson}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'customerType',
      label: t.customerType,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <Badge className={getCustomerTypeColor(item.customerType)}>
          {t[item.customerType as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'creditLimit',
      label: t.creditLimit,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="text-white font-medium">
          {formatCurrency(item.creditLimit)}
        </div>
      )
    },
    {
      key: 'currentBalance',
      label: t.currentBalance,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="text-white/80">
          {formatCurrency(item.currentBalance)}
        </div>
      )
    },
    {
      key: 'creditUtilization',
      label: 'Credit Utilization',
      render: (item: FinanceCustomer) => {
        const utilization = getCreditUtilization(item.currentBalance, item.creditLimit)
        return (
          <div className="flex items-center gap-2">
            <div className="w-16 bg-white/20 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  utilization > 90 ? 'bg-red-500' :
                  utilization > 70 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(utilization, 100)}%` }}
              ></div>
            </div>
            <span className={`text-sm font-medium ${
              utilization > 90 ? 'text-red-400' :
              utilization > 70 ? 'text-yellow-400' : 'text-green-400'
            }`}>
              {utilization}%
            </span>
          </div>
        )
      }
    },
    {
      key: 'creditStatus',
      label: t.creditStatus,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="flex items-center gap-1">
          {getCreditStatusIcon(item.creditStatus)}
          <Badge className={getCreditStatusColor(item.creditStatus)}>
            {t[item.creditStatus as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'paymentTerms',
      label: t.paymentTerms,
      render: (item: FinanceCustomer) => (
        <span className="text-white/80">
          {t[item.paymentTerms as keyof typeof t]}
        </span>
      )
    },
    {
      key: 'totalRevenue',
      label: t.totalRevenue,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="text-green-400 font-medium">
          {formatCurrency(item.totalRevenue)}
        </div>
      )
    },
    {
      key: 'lastPayment',
      label: t.lastPayment,
      sortable: true,
      render: (item: FinanceCustomer) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastPayment}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FinanceCustomer>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: FinanceCustomer) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: FinanceCustomer) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: FinanceCustomer) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'creditStatus',
      label: t.creditStatus,
      options: [
        { label: t.good, value: 'good' },
        { label: t.warning, value: 'warning' },
        { label: t.critical, value: 'critical' },
        { label: t.blocked, value: 'blocked' }
      ]
    },
    {
      key: 'paymentTerms',
      label: t.paymentTerms,
      options: [
        { label: t.net30, value: 'net30' },
        { label: t.net60, value: 'net60' },
        { label: t.prepaid, value: 'prepaid' },
        { label: t.cod, value: 'cod' }
      ]
    },
    {
      key: 'customerType',
      label: t.customerType,
      options: [
        { label: t.corporate, value: 'corporate' },
        { label: t.individual, value: 'individual' },
        { label: t.government, value: 'government' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'creditLimit',
      label: t.creditLimit,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'currentBalance',
      label: t.currentBalance,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'lastPaymentAmount',
      label: t.lastPaymentAmount,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'paymentTerms',
      label: t.paymentTerms,
      type: 'select',
      required: true,
      options: [
        { label: t.net30, value: 'net30' },
        { label: t.net60, value: 'net60' },
        { label: t.prepaid, value: 'prepaid' },
        { label: t.cod, value: 'cod' }
      ]
    },
    {
      name: 'paymentTermsAr',
      label: t.paymentTerms + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'creditStatus',
      label: t.creditStatus,
      type: 'select',
      required: true,
      options: [
        { label: t.good, value: 'good' },
        { label: t.warning, value: 'warning' },
        { label: t.critical, value: 'critical' },
        { label: t.blocked, value: 'blocked' }
      ]
    },
    {
      name: 'customerType',
      label: t.customerType,
      type: 'select',
      required: true,
      options: [
        { label: t.corporate, value: 'corporate' },
        { label: t.individual, value: 'individual' },
        { label: t.government, value: 'government' }
      ]
    },
    {
      name: 'totalRevenue',
      label: t.totalRevenue,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'outstandingAmount',
      label: t.outstandingAmount,
      type: 'number',
      min: 0
    },
    {
      name: 'overdueAmount',
      label: t.overdueAmount,
      type: 'number',
      min: 0
    },
    {
      name: 'invoiceCount',
      label: t.invoiceCount,
      type: 'number',
      min: 0
    },
    {
      name: 'avgPaymentDays',
      label: t.avgPaymentDays,
      type: 'number',
      min: 0
    },
    {
      name: 'industry',
      label: t.industry,
      type: 'text'
    },
    {
      name: 'industryAr',
      label: t.industry + ' (عربي)',
      type: 'text'
    },
    {
      name: 'contactPerson',
      label: t.contactPerson,
      type: 'text'
    },
    {
      name: 'contactPersonAr',
      label: t.contactPerson + ' (عربي)',
      type: 'text'
    },
    {
      name: 'lastPayment',
      label: t.lastPayment,
      type: 'date',
      required: true
    }
  ]

  // Event handlers
  const handleCreate = () => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FinanceCustomer>) => {
    try {
      // Calculate available credit based on credit limit and current balance
      if (data.creditLimit && data.currentBalance) {
        data.availableCredit = data.creditLimit - data.currentBalance
      }

      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.financeCustomers}
        data={customers}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
