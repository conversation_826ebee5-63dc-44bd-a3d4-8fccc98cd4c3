import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import type { RootState } from '../../store'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  PieChart,
  BarChart3,
  Calculator,
  Wallet,
  Receipt,
  Target,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  Plus,
  FileText,
  Calendar,
  Filter
} from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart
} from 'recharts'

interface FinanceManagerDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    financeOverview: 'نظرة عامة على المالية',
    budgetManagement: 'إدارة الميزانية',
    expenseTracking: 'تتبع المصروفات',
    revenueAnalysis: 'تحليل الإيرادات',
    financialReports: 'التقارير المالية',
    quickActions: 'إجراءات سريعة',
    totalRevenue: 'إجمالي الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    budgetUtilization: 'استخدام الميزانية',
    pendingInvoices: 'الفواتير المعلقة',
    cashFlow: 'التدفق النقدي',
    monthlyBudget: 'الميزانية الشهرية',
    quarterlyTarget: 'الهدف الربعي',
    expenseCategories: 'فئات المصروفات',
    recentTransactions: 'المعاملات الحديثة',
    budgetAlerts: 'تنبيهات الميزانية',
    createBudget: 'إنشاء ميزانية',
    processPayment: 'معالجة دفعة',
    generateReport: 'إنشاء تقرير',
    reviewExpenses: 'مراجعة المصروفات',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    increase: 'زيادة',
    decrease: 'انخفاض',
    onTrack: 'على المسار الصحيح',
    overBudget: 'تجاوز الميزانية',
    underBudget: 'أقل من الميزانية'
  },
  en: {
    welcome: 'Welcome',
    financeOverview: 'Finance Overview',
    budgetManagement: 'Budget Management',
    expenseTracking: 'Expense Tracking',
    revenueAnalysis: 'Revenue Analysis',
    financialReports: 'Financial Reports',
    quickActions: 'Quick Actions',
    totalRevenue: 'Total Revenue',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    budgetUtilization: 'Budget Utilization',
    pendingInvoices: 'Pending Invoices',
    cashFlow: 'Cash Flow',
    monthlyBudget: 'Monthly Budget',
    quarterlyTarget: 'Quarterly Target',
    expenseCategories: 'Expense Categories',
    recentTransactions: 'Recent Transactions',
    budgetAlerts: 'Budget Alerts',
    createBudget: 'Create Budget',
    processPayment: 'Process Payment',
    generateReport: 'Generate Report',
    reviewExpenses: 'Review Expenses',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    increase: 'Increase',
    decrease: 'Decrease',
    onTrack: 'On Track',
    overBudget: 'Over Budget',
    underBudget: 'Under Budget'
  }
}

export default function FinanceManagerDashboard({ language }: FinanceManagerDashboardProps) {
  const { user } = useSelector((state: RootState) => state.auth)
  const navigate = useNavigate()
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Finance-specific metrics
  const [financeMetrics, setFinanceMetrics] = useState({
    totalRevenue: 2850000,
    totalExpenses: 1920000,
    netProfit: 930000,
    budgetUtilization: 78.5,
    pendingInvoices: 45,
    cashFlow: 1250000,
    monthlyBudget: 2500000,
    quarterlyTarget: 7500000,
    expenseGrowth: 12.5,
    revenueGrowth: 18.3
  })

  // Chart data states
  const [revenueData, setRevenueData] = useState<any[]>([])
  const [expenseData, setExpenseData] = useState<any[]>([])
  const [budgetData, setBudgetData] = useState<any[]>([])
  const [cashFlowData, setCashFlowData] = useState<any[]>([])
  const [expenseCategoryData, setExpenseCategoryData] = useState<any[]>([])
  const [monthlyTrendsData, setMonthlyTrendsData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // Chart colors
  const chartColors = {
    primary: '#3B82F6',
    secondary: '#10B981',
    accent: '#F59E0B',
    danger: '#EF4444',
    purple: '#8B5CF6',
    cyan: '#06B6D4'
  }

  // Fetch chart data
  const fetchChartData = async () => {
    setLoading(true)
    try {
      // Simulate API calls - replace with actual API endpoints
      await Promise.all([
        fetchRevenueData(),
        fetchExpenseData(),
        fetchBudgetData(),
        fetchCashFlowData(),
        fetchExpenseCategoryData(),
        fetchMonthlyTrendsData()
      ])
    } catch (error) {
      console.error('Error fetching chart data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRevenueData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { month: 'يناير', revenue: 2400000, target: 2500000 },
      { month: 'فبراير', revenue: 2600000, target: 2500000 },
      { month: 'مارس', revenue: 2850000, target: 2500000 },
      { month: 'أبريل', revenue: 2750000, target: 2500000 },
      { month: 'مايو', revenue: 2950000, target: 2500000 },
      { month: 'يونيو', revenue: 3100000, target: 2500000 }
    ]
    setRevenueData(data)
  }

  const fetchExpenseData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { month: 'يناير', expenses: 1800000, budget: 2000000 },
      { month: 'فبراير', expenses: 1900000, budget: 2000000 },
      { month: 'مارس', expenses: 1920000, budget: 2000000 },
      { month: 'أبريل', expenses: 1850000, budget: 2000000 },
      { month: 'مايو', expenses: 1980000, budget: 2000000 },
      { month: 'يونيو', expenses: 2050000, budget: 2000000 }
    ]
    setExpenseData(data)
  }

  const fetchBudgetData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { department: 'التسويق', allocated: 500000, spent: 420000, remaining: 80000 },
      { department: 'التطوير', allocated: 800000, spent: 650000, remaining: 150000 },
      { department: 'المبيعات', allocated: 600000, spent: 480000, remaining: 120000 },
      { department: 'الموارد البشرية', allocated: 300000, spent: 250000, remaining: 50000 },
      { department: 'العمليات', allocated: 400000, spent: 320000, remaining: 80000 }
    ]
    setBudgetData(data)
  }

  const fetchCashFlowData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { month: 'يناير', inflow: 2400000, outflow: 1800000, net: 600000 },
      { month: 'فبراير', inflow: 2600000, outflow: 1900000, net: 700000 },
      { month: 'مارس', inflow: 2850000, outflow: 1920000, net: 930000 },
      { month: 'أبريل', inflow: 2750000, outflow: 1850000, net: 900000 },
      { month: 'مايو', inflow: 2950000, outflow: 1980000, net: 970000 },
      { month: 'يونيو', inflow: 3100000, outflow: 2050000, net: 1050000 }
    ]
    setCashFlowData(data)
  }

  const fetchExpenseCategoryData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { category: 'الرواتب', amount: 800000, percentage: 41.7 },
      { category: 'التسويق', amount: 420000, percentage: 21.9 },
      { category: 'العمليات', amount: 320000, percentage: 16.7 },
      { category: 'التطوير', amount: 250000, percentage: 13.0 },
      { category: 'أخرى', amount: 130000, percentage: 6.8 }
    ]
    setExpenseCategoryData(data)
  }

  const fetchMonthlyTrendsData = async () => {
    // Mock data - replace with actual API call
    const data = [
      { month: 'يناير', revenue: 2400000, expenses: 1800000, profit: 600000 },
      { month: 'فبراير', revenue: 2600000, expenses: 1900000, profit: 700000 },
      { month: 'مارس', revenue: 2850000, expenses: 1920000, profit: 930000 },
      { month: 'أبريل', revenue: 2750000, expenses: 1850000, profit: 900000 },
      { month: 'مايو', revenue: 2950000, expenses: 1980000, profit: 970000 },
      { month: 'يونيو', revenue: 3100000, expenses: 2050000, profit: 1050000 }
    ]
    setMonthlyTrendsData(data)
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchChartData()
    setRefreshing(false)
  }

  // Load data on component mount
  useEffect(() => {
    fetchChartData()
  }, [])

  const handleNavigation = (path: string) => {
    navigate(path)
  }

  const handleGenerateReport = () => {
    navigate('/finance/reports')
  }

  const financialCards = [
    {
      title: t.totalRevenue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.totalRevenue),
      change: `+${financeMetrics.revenueGrowth}%`,
      trend: 'up',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.totalExpenses,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.totalExpenses),
      change: `+${financeMetrics.expenseGrowth}%`,
      trend: 'up',
      icon: TrendingDown,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.netProfit,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.netProfit),
      change: '+24.7%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.budgetUtilization,
      value: `${financeMetrics.budgetUtilization}%`,
      change: t.onTrack,
      trend: 'stable',
      icon: Target,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.pendingInvoices,
      value: financeMetrics.pendingInvoices.toString(),
      change: '-8',
      trend: 'down',
      icon: Receipt,
      color: 'from-orange-500 to-orange-600'
    },
    {
      title: t.cashFlow,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.cashFlow),
      change: '+15.2%',
      trend: 'up',
      icon: Wallet,
      color: 'from-cyan-500 to-cyan-600'
    }
  ]

  const quickActions = [
    { title: t.createBudget, icon: Calculator, href: '/finance/budgets', color: 'from-blue-500 to-blue-600' },
    { title: t.reviewExpenses, icon: Receipt, href: '/finance/expenses', color: 'from-red-500 to-red-600' },
    { title: t.generateReport, icon: FileText, href: '/finance/reports', color: 'from-green-500 to-green-600' },
    { title: t.processPayment, icon: CreditCard, href: '/finance/payments', color: 'from-purple-500 to-purple-600' }
  ]

  const recentTransactions = [
    {
      id: 1,
      type: 'income',
      description: 'Payment from Client ABC',
      descriptionAr: 'دفعة من العميل ABC',
      amount: 125000,
      date: '2024-01-20',
      category: 'Revenue',
      categoryAr: 'إيرادات'
    },
    {
      id: 2,
      type: 'expense',
      description: 'Office Supplies Purchase',
      descriptionAr: 'شراء مستلزمات مكتبية',
      amount: -15000,
      date: '2024-01-19',
      category: 'Operations',
      categoryAr: 'عمليات'
    },
    {
      id: 3,
      type: 'expense',
      description: 'Software License Renewal',
      descriptionAr: 'تجديد رخصة البرمجيات',
      amount: -45000,
      date: '2024-01-18',
      category: 'Technology',
      categoryAr: 'تكنولوجيا'
    }
  ]

  const budgetAlerts = [
    {
      id: 1,
      type: 'warning',
      message: 'Marketing budget is 85% utilized',
      messageAr: 'ميزانية التسويق مستخدمة بنسبة 85%',
      category: 'Marketing',
      percentage: 85
    },
    {
      id: 2,
      type: 'danger',
      message: 'IT budget exceeded by 12%',
      messageAr: 'ميزانية تقنية المعلومات تجاوزت بنسبة 12%',
      category: 'IT',
      percentage: 112
    }
  ]

  const getStatusColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-400'
      case 'down':
        return 'text-red-400'
      default:
        return 'text-blue-400'
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-500/10'
      case 'danger':
        return 'border-l-red-500 bg-red-500/10'
      default:
        return 'border-l-blue-500 bg-blue-500/10'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.first_name} {user?.last_name}
          </h1>
          <p className="text-white/70">لوحة تحكم مدير المالية - إدارة شاملة للشؤون المالية</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button
            variant="outline"
            className="glass-button"
            onClick={handleGenerateReport}
          >
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Financial Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {financialCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="glass-card border-white/20 bg-white/5">
          <TabsTrigger value="overview" className="text-white data-[state=active]:bg-white/20">
            {language === 'ar' ? 'نظرة عامة' : 'Overview'}
          </TabsTrigger>
          <TabsTrigger value="revenue" className="text-white data-[state=active]:bg-white/20">
            {language === 'ar' ? 'الإيرادات' : 'Revenue'}
          </TabsTrigger>
          <TabsTrigger value="expenses" className="text-white data-[state=active]:bg-white/20">
            {language === 'ar' ? 'المصروفات' : 'Expenses'}
          </TabsTrigger>
          <TabsTrigger value="budget" className="text-white data-[state=active]:bg-white/20">
            {language === 'ar' ? 'الميزانية' : 'Budget'}
          </TabsTrigger>
          <TabsTrigger value="cashflow" className="text-white data-[state=active]:bg-white/20">
            {language === 'ar' ? 'التدفق النقدي' : 'Cash Flow'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Overview Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Trends Chart */}
            <Card className="glass-card border-white/20">
              <CardHeader>
                <CardTitle className="text-white text-xl flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {language === 'ar' ? 'الاتجاهات الشهرية' : 'Monthly Trends'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={monthlyTrendsData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                    <YAxis stroke="rgba(255,255,255,0.7)" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                      formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(value), '']}
                    />
                    <Legend />
                    <Bar dataKey="revenue" fill={chartColors.primary} name={language === 'ar' ? 'الإيرادات' : 'Revenue'} />
                    <Bar dataKey="expenses" fill={chartColors.danger} name={language === 'ar' ? 'المصروفات' : 'Expenses'} />
                    <Line type="monotone" dataKey="profit" stroke={chartColors.secondary} strokeWidth={3} name={language === 'ar' ? 'الربح' : 'Profit'} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Expense Categories Pie Chart */}
            <Card className="glass-card border-white/20">
              <CardHeader>
                <CardTitle className="text-white text-xl flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  {language === 'ar' ? 'توزيع المصروفات' : 'Expense Distribution'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={expenseCategoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {expenseCategoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={Object.values(chartColors)[index % Object.values(chartColors).length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                      formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(value), '']}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {/* Revenue Analysis */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                {language === 'ar' ? 'تحليل الإيرادات' : 'Revenue Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                    formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(value), '']}
                  />
                  <Legend />
                  <Area type="monotone" dataKey="revenue" stackId="1" stroke={chartColors.primary} fill={chartColors.primary} fillOpacity={0.6} name={language === 'ar' ? 'الإيرادات الفعلية' : 'Actual Revenue'} />
                  <Area type="monotone" dataKey="target" stackId="2" stroke={chartColors.accent} fill={chartColors.accent} fillOpacity={0.3} name={language === 'ar' ? 'الهدف' : 'Target'} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-6">
          {/* Expense Analysis */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <TrendingDown className="h-5 w-5" />
                {language === 'ar' ? 'تحليل المصروفات' : 'Expense Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={expenseData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                    formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(value), '']}
                  />
                  <Legend />
                  <Bar dataKey="expenses" fill={chartColors.danger} name={language === 'ar' ? 'المصروفات الفعلية' : 'Actual Expenses'} />
                  <Bar dataKey="budget" fill={chartColors.accent} name={language === 'ar' ? 'الميزانية المخصصة' : 'Budget Allocated'} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="budget" className="space-y-6">
          {/* Budget Analysis */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <Target className="h-5 w-5" />
                {language === 'ar' ? 'تحليل الميزانية' : 'Budget Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={budgetData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis type="number" stroke="rgba(255,255,255,0.7)" />
                  <YAxis dataKey="department" type="category" stroke="rgba(255,255,255,0.7)" width={100} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                    formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(value), '']}
                  />
                  <Legend />
                  <Bar dataKey="allocated" fill={chartColors.primary} name={language === 'ar' ? 'المخصص' : 'Allocated'} />
                  <Bar dataKey="spent" fill={chartColors.danger} name={language === 'ar' ? 'المنفق' : 'Spent'} />
                  <Bar dataKey="remaining" fill={chartColors.secondary} name={language === 'ar' ? 'المتبقي' : 'Remaining'} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cashflow" className="space-y-6">
          {/* Cash Flow Analysis */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                {language === 'ar' ? 'تحليل التدفق النقدي' : 'Cash Flow Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={cashFlowData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="month" stroke="rgba(255,255,255,0.7)" />
                  <YAxis stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                    formatter={(value: any) => [new Intl.NumberFormat('ar-SA', {
                      style: 'currency',
                      currency: 'SAR'
                    }).format(value), '']}
                  />
                  <Legend />
                  <Line type="monotone" dataKey="inflow" stroke={chartColors.secondary} strokeWidth={3} name={language === 'ar' ? 'التدفق الداخل' : 'Cash Inflow'} />
                  <Line type="monotone" dataKey="outflow" stroke={chartColors.danger} strokeWidth={3} name={language === 'ar' ? 'التدفق الخارج' : 'Cash Outflow'} />
                  <Line type="monotone" dataKey="net" stroke={chartColors.primary} strokeWidth={4} name={language === 'ar' ? 'صافي التدفق' : 'Net Cash Flow'} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  onClick={() => handleNavigation(action.href)}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.recentTransactions}</CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="glass-button"
                onClick={() => handleNavigation('/finance/expenses')}
              >
                <Eye className="h-4 w-4 mr-2" />
                {t.viewDetails}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg ${
                          transaction.type === 'income' ? 'bg-green-500/20' : 'bg-red-500/20'
                        }`}>
                          {transaction.type === 'income' ?
                            <TrendingUp className="h-4 w-4 text-green-400" /> :
                            <TrendingDown className="h-4 w-4 text-red-400" />
                          }
                        </div>
                        <h4 className="text-white font-medium">
                          {language === 'ar' ? transaction.descriptionAr : transaction.description}
                        </h4>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-white/80">
                        <span>{transaction.date}</span>
                        <span>{language === 'ar' ? transaction.categoryAr : transaction.category}</span>
                      </div>
                    </div>
                    <div className={`text-lg font-bold ${
                      transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(Math.abs(transaction.amount))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Alerts */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {t.budgetAlerts}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {budgetAlerts.map((alert) => (
              <div key={alert.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getAlertColor(alert.type)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-white font-medium mb-1">
                      {language === 'ar' ? alert.messageAr : alert.message}
                    </p>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          alert.percentage > 100 ? 'bg-red-500' :
                          alert.percentage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(alert.percentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className={`text-lg font-bold ml-4 ${
                    alert.percentage > 100 ? 'text-red-400' :
                    alert.percentage > 80 ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    {alert.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
