import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import { LazyErrorBoundary } from '../utils/lazyLoad'

// Import lazy-loaded components for regular admin
import {
  Unauthorized,
  AdminDashboard,
  Employees,
  Departments,
  Reports,
  Settings,
  LeaveManagement,
  Attendance,
  Performance,
  Payroll,
  Projects,
  Tasks,
  ProjectReports,
  FinanceBudgets,
  FinanceReports,
  Assets,
  Suppliers,
  PurchaseOrders,
  Messages,
  Announcements,
  Documents,
  Meetings,
  PersonalProfile,
  PersonalMessages,
  PersonalCalendar,
  EmployeeProfile,
  EmployeeLeave,
  EmployeeTasks,
  Inventory,
  UserManagement,
  SalesOrders,
  Quotations,
  SalesPipeline,
  Calendar,
  BusinessIntelligence,
  VendorManagement,
  Customers,
  Products,
  WorkflowAutomation,
  ReportGenerator,
  SupportDashboard,
  TicketManagement,
  KnowledgeBase,
  LiveChat,
  LiveChatManagement,
  CustomerFeedback,
  AgentPerformance,
  AIChatAssistant,
  AutomationDashboard,
  CustomerSupportHub
} from './lazyRoutes'

// Import test components
import ExportTest from '../components/test/ExportTest'
import PerformanceDashboard from '../components/performance/PerformanceDashboard'

interface AdminRoutesProps {
  language: 'ar' | 'en'
}

export default function AdminRoutes({ language }: AdminRoutesProps) {
  return (
    <LazyErrorBoundary>
      <Routes>
      {/* Dashboard - Root and /admin/dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <AdminDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/dashboard" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <AdminDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Core Management */}
      <Route path="/admin/employees" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Employees language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/departments" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Departments language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/reports" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Reports language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/settings" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Settings language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/performance-monitor" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <PerformanceDashboard />
        </RoleBasedRoute>
      } />

      {/* HR Management */}
      <Route path="/admin/hr/leave" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <LeaveManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/attendance" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Attendance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/performance" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Performance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/hr/payroll" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Payroll language={language} />
        </RoleBasedRoute>
      } />

      {/* Sales Management */}
      <Route path="/admin/sales/orders" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <SalesOrders language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/sales/quotations" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Quotations language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/sales/pipeline" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <SalesPipeline language={language} />
        </RoleBasedRoute>
      } />

      {/* CRM & Products */}
      <Route path="/admin/customers" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Customers language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/products" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Products language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/inventory" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Inventory language={language} />
        </RoleBasedRoute>
      } />

      {/* Business Intelligence (Limited for Admin) */}
      <Route path="/admin/business-intelligence" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <BusinessIntelligence language={language} />
        </RoleBasedRoute>
      } />

      {/* Workflow Automation (Limited for Admin) */}
      <Route path="/admin/workflows" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <WorkflowAutomation language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/report-generator" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <ReportGenerator language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features */}
      <Route path="/admin/profile" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <PersonalProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/messages" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/calendar" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/admin/communication/messages" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/announcements" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Announcements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/documents" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Documents language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/communication/meetings" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* User Management (Limited for Admin) */}
      <Route path="/admin/users" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <UserManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Vendor Management */}
      <Route path="/admin/vendors" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <VendorManagement language={language} />
        </RoleBasedRoute>
      } />

      {/* Customer Service */}
      <Route path="/admin/support" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <SupportDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/dashboard" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <SupportDashboard />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/tickets" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <TicketManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/knowledge-base" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <KnowledgeBase />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/live-chat" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <LiveChat language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/live-chat-management" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <LiveChatManagement language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/feedback" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <CustomerFeedback language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/agents" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <AgentPerformance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/ai-chat" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <AIChatAssistant language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/automation" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <AutomationDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/admin/support/hub" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <CustomerSupportHub language={language} />
        </RoleBasedRoute>
      } />

      {/* Test Routes */}
      <Route path="/admin/test/export" element={
        <RoleBasedRoute requiredRole="admin" fallbackPath="/admin/unauthorized">
          <ExportTest />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/admin/unauthorized" element={<Unauthorized language={language} />} />

      {/* Restricted Routes - Redirect to unauthorized */}
      <Route path="/admin/system" element={<Navigate to="/admin/unauthorized" replace />} />
      <Route path="/admin/security" element={<Navigate to="/admin/unauthorized" replace />} />
      <Route path="/admin/ai" element={<Navigate to="/admin/unauthorized" replace />} />
      <Route path="/admin/analytics" element={<Navigate to="/admin/unauthorized" replace />} />
      <Route path="/admin/compliance" element={<Navigate to="/admin/unauthorized" replace />} />

      {/* Catch all - redirect to admin dashboard */}
      <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </LazyErrorBoundary>
  )
}
