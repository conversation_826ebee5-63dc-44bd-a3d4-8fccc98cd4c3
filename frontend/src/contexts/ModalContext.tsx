/**
 * Modal Context
 * Centralized state management for all modals in the application
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react'

// Modal types for the application
export type ModalType =
  | 'kpi-create'
  | 'kpi-edit'
  | 'kpi-view'
  | 'kpi-value-add'
  | 'kpi-value-edit'
  | 'kpi-category'
  | 'kpi-target'
  | 'kpi-alert'
  | 'kpi-export'
  | 'kpi-import'
  | 'kpi-delete-confirm'

// Modal state interface
interface ModalState {
  isOpen: boolean
  type: ModalType | null
  data: Record<string, unknown> | null
  meta: Record<string, unknown>
}

// Modal context interface
interface ModalContextType {
  modalState: ModalState
  openModal: (type: ModalType, data?: Record<string, unknown> | null, meta?: Record<string, unknown>) => void
  closeModal: () => void
  updateModalData: (data: Record<string, unknown> | null) => void
  updateModalMeta: (meta: Record<string, unknown>) => void
}

// Create the context with default values
const ModalContext = createContext<ModalContextType>({
  modalState: {
    isOpen: false,
    type: null,
    data: null,
    meta: {}
  },
  openModal: () => {},
  closeModal: () => {},
  updateModalData: () => {},
  updateModalMeta: () => {}
})

// Modal provider props
interface ModalProviderProps {
  children: ReactNode
}

// Modal provider component
export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  // State for the current modal
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    type: null,
    data: null,
    meta: {}
  })

  // Open a modal with the given type and data
  const openModal = useCallback((type: ModalType, data: Record<string, unknown> | null = null, meta: Record<string, unknown> = {}) => {
    setModalState({
      isOpen: true,
      type,
      data,
      meta
    })
  }, [])

  // Close the current modal
  const closeModal = useCallback(() => {
    // First set isOpen to false to trigger animations
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }))
    
    // Then clear the modal data after a short delay
    setTimeout(() => {
      setModalState({
        isOpen: false,
        type: null,
        data: null,
        meta: {}
      })
    }, 300) // Match this with your animation duration
  }, [])

  // Update the data of the current modal
  const updateModalData = useCallback((data: Record<string, unknown> | null) => {
    setModalState(prev => ({
      ...prev,
      data
    }))
  }, [])

  // Update the metadata of the current modal
  const updateModalMeta = useCallback((meta: Record<string, unknown>) => {
    setModalState(prev => ({
      ...prev,
      meta: {
        ...prev.meta,
        ...meta
      }
    }))
  }, [])

  // Context value
  const value = {
    modalState,
    openModal,
    closeModal,
    updateModalData,
    updateModalMeta
  }

  return (
    <ModalContext.Provider value={value}>
      {children}
    </ModalContext.Provider>
  )
}

// Custom hook to use the modal context
export const useModal = () => {
  const context = useContext(ModalContext)
  
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider')
  }
  
  return context
}

// Modal consumer component for class components
export const ModalConsumer = ModalContext.Consumer
