/**
 * Type Guards and Type Predicates
 * Comprehensive type safety utilities for runtime type checking
 */

import { Employee, Department, EmployeeFormData } from '../types/employee'
import { ApiResponse, ApiErrorResponse } from '../types/api'

// Basic type guards
export const isString = (value: unknown): value is string => {
  return typeof value === 'string'
}

export const isNumber = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value)
}

export const isBoolean = (value: unknown): value is boolean => {
  return typeof value === 'boolean'
}

export const isObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

export const isArray = (value: unknown): value is unknown[] => {
  return Array.isArray(value)
}

export const isFunction = (value: unknown): value is Function => {
  return typeof value === 'function'
}

export const isDefined = <T>(value: T | undefined | null): value is T => {
  return value !== undefined && value !== null
}

export const isNonEmptyString = (value: unknown): value is string => {
  return isString(value) && value.trim().length > 0
}

export const isValidEmail = (value: unknown): value is string => {
  if (!isString(value)) return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(value)
}

export const isValidPhone = (value: unknown): value is string => {
  if (!isString(value)) return false
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))
}

export const isValidDate = (value: unknown): value is string => {
  if (!isString(value)) return false
  const date = new Date(value)
  return !isNaN(date.getTime())
}

// API response type guards
export const isApiResponse = <T>(value: unknown): value is ApiResponse<T> => {
  return (
    isObject(value) &&
    'data' in value &&
    'status' in value &&
    isNumber(value.status) &&
    'success' in value &&
    isBoolean(value.success)
  )
}

export const isApiErrorResponse = (value: unknown): value is ApiErrorResponse => {
  return (
    isObject(value) &&
    'error' in value &&
    isObject(value.error) &&
    'code' in value.error &&
    'message' in value.error &&
    isString(value.error.code) &&
    isString(value.error.message) &&
    'status' in value &&
    isNumber(value.status)
  )
}

// Employee type guards
export const isEmployee = (value: unknown): value is Employee => {
  return (
    isObject(value) &&
    'id' in value &&
    'employee_id' in value &&
    'first_name' in value &&
    'last_name' in value &&
    'email' in value &&
    'phone' in value &&
    'department_id' in value &&
    'position' in value &&
    'hire_date' in value &&
    'employment_status' in value &&
    'gender' in value &&
    isString(value.employee_id) &&
    isString(value.first_name) &&
    isString(value.last_name) &&
    isValidEmail(value.email) &&
    isString(value.phone) &&
    isString(value.position) &&
    isValidDate(value.hire_date) &&
    ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN', 'CONSULTANT', 'TEMPORARY'].includes(value.employment_status as string) &&
    ['M', 'F', 'OTHER', 'PREFER_NOT_TO_SAY'].includes(value.gender as string)
  )
}

export const isEmployeeFormData = (value: unknown): value is EmployeeFormData => {
  return (
    isObject(value) &&
    'employee_id' in value &&
    'first_name' in value &&
    'last_name' in value &&
    'email' in value &&
    'phone' in value &&
    'department_id' in value &&
    'position' in value &&
    'hire_date' in value &&
    'employment_status' in value &&
    'gender' in value &&
    isString(value.employee_id) &&
    isString(value.first_name) &&
    isString(value.last_name) &&
    isValidEmail(value.email) &&
    isString(value.phone) &&
    isString(value.position) &&
    isString(value.hire_date) &&
    ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERN', 'CONSULTANT', 'TEMPORARY'].includes(value.employment_status as string) &&
    ['M', 'F', 'OTHER', 'PREFER_NOT_TO_SAY'].includes(value.gender as string)
  )
}

export const isDepartment = (value: unknown): value is Department => {
  return (
    isObject(value) &&
    'id' in value &&
    'name' in value &&
    'code' in value &&
    'employee_count' in value &&
    'status' in value &&
    isString(value.name) &&
    isString(value.code) &&
    isNumber(value.employee_count) &&
    ['active', 'inactive', 'pending', 'archived'].includes(value.status as string)
  )
}

// Error type guards
export const isError = (value: unknown): value is Error => {
  return value instanceof Error
}

export const isHttpError = (value: unknown): value is { status: number; message: string } => {
  return (
    isObject(value) &&
    'status' in value &&
    'message' in value &&
    isNumber(value.status) &&
    isString(value.message)
  )
}

// Form validation type guards
export const isValidFormData = (value: unknown): value is Record<string, string | number | boolean> => {
  if (!isObject(value)) return false
  
  return Object.values(value).every(val => 
    isString(val) || isNumber(val) || isBoolean(val)
  )
}

// Array type guards with element validation
export const isArrayOf = <T>(
  value: unknown,
  elementGuard: (item: unknown) => item is T
): value is T[] => {
  return isArray(value) && value.every(elementGuard)
}

export const isEmployeeArray = (value: unknown): value is Employee[] => {
  return isArrayOf(value, isEmployee)
}

export const isDepartmentArray = (value: unknown): value is Department[] => {
  return isArrayOf(value, isDepartment)
}

// Utility type assertions
export const assertIsString = (value: unknown, fieldName: string): asserts value is string => {
  if (!isString(value)) {
    throw new Error(`Expected ${fieldName} to be a string, got ${typeof value}`)
  }
}

export const assertIsNumber = (value: unknown, fieldName: string): asserts value is number => {
  if (!isNumber(value)) {
    throw new Error(`Expected ${fieldName} to be a number, got ${typeof value}`)
  }
}

export const assertIsObject = (value: unknown, fieldName: string): asserts value is Record<string, unknown> => {
  if (!isObject(value)) {
    throw new Error(`Expected ${fieldName} to be an object, got ${typeof value}`)
  }
}

export const assertIsEmployee = (value: unknown): asserts value is Employee => {
  if (!isEmployee(value)) {
    throw new Error('Invalid employee data structure')
  }
}

export const assertIsDepartment = (value: unknown): asserts value is Department => {
  if (!isDepartment(value)) {
    throw new Error('Invalid department data structure')
  }
}

// Safe type casting utilities
export const safeParseInt = (value: unknown, defaultValue: number = 0): number => {
  if (isNumber(value)) return Math.floor(value)
  if (isString(value)) {
    const parsed = parseInt(value, 10)
    return isNaN(parsed) ? defaultValue : parsed
  }
  return defaultValue
}

export const safeParseFloat = (value: unknown, defaultValue: number = 0): number => {
  if (isNumber(value)) return value
  if (isString(value)) {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? defaultValue : parsed
  }
  return defaultValue
}

export const safeString = (value: unknown, defaultValue: string = ''): string => {
  if (isString(value)) return value
  if (isNumber(value)) return value.toString()
  if (isBoolean(value)) return value.toString()
  return defaultValue
}

export const safeBoolean = (value: unknown, defaultValue: boolean = false): boolean => {
  if (isBoolean(value)) return value
  if (isString(value)) {
    const lower = value.toLowerCase()
    return lower === 'true' || lower === '1' || lower === 'yes'
  }
  if (isNumber(value)) return value !== 0
  return defaultValue
}

// Deep object validation
export const hasRequiredFields = <T extends Record<string, unknown>>(
  obj: unknown,
  requiredFields: (keyof T)[]
): obj is T => {
  if (!isObject(obj)) return false
  
  return requiredFields.every(field =>
    field in obj && isDefined((obj as Record<string, unknown>)[field])
  )
}

// Browser API type guards
export const isNavigatorWithConnection = (nav: Navigator): nav is Navigator & { 
  connection?: { effectiveType?: string; downlink?: number }
} => {
  return 'connection' in nav
}

export const isPerformanceWithMemory = (perf: Performance): perf is Performance & {
  memory?: { usedJSHeapSize: number; totalJSHeapSize: number }
} => {
  return 'memory' in perf
}

// Event type guards
export const isKeyboardEvent = (event: Event): event is KeyboardEvent => {
  return event instanceof KeyboardEvent
}

export const isMouseEvent = (event: Event): event is MouseEvent => {
  return event instanceof MouseEvent
}

export const isFormEvent = (event: Event): event is Event & { target: HTMLFormElement } => {
  return event.target instanceof HTMLFormElement
}

export const isInputEvent = (event: Event): event is Event & { target: HTMLInputElement } => {
  return event.target instanceof HTMLInputElement
}

// React component type guards
export const isReactElement = (value: unknown): value is React.ReactElement => {
  return (
    isObject(value) &&
    'type' in value &&
    'props' in value &&
    '$$typeof' in value
  )
}

export const isComponentWithProps = <P extends Record<string, unknown>>(
  component: unknown,
  propsGuard: (props: unknown) => props is P
): component is React.ComponentType<P> => {
  return isFunction(component)
}
