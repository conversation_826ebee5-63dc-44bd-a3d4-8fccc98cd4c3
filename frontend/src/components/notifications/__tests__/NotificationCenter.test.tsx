/**
 * NotificationCenter Component Tests
 * Comprehensive tests for the notification center functionality
 */

import React from 'react'
import { screen, waitFor } from '@testing-library/dom'
import userEvent from '@testing-library/user-event'
import { renderWithProviders, createMockNotification } from '../../../test-utils/test-utils'
import NotificationCenter from '../NotificationCenter'
import { markAsRead, removeNotification, markAllAsRead, clearError } from '../../../store/slices/notificationSlice'

// Mock the store actions
const deleteNotification = jest.fn()
const clearAllNotifications = jest.fn()

jest.mock('../../../store/slices/notificationSlice', () => ({
  markAsRead: jest.fn(),
  removeNotification: jest.fn(),
  markAllAsRead: jest.fn(),
  clearError: jest.fn(),
  setFilter: jest.fn(),
  setCategory: jest.fn(),
  deleteNotification: jest.fn(),
  clearAllNotifications: jest.fn(),
}))

describe('NotificationCenter Component', () => {
  const mockNotifications = [
    createMockNotification({
      id: '1',
      title: 'Welcome Message',
      message: 'Welcome to the system',
      type: 'success',
      read: false,
      timestamp: Date.now() - 1000 * 60 * 5, // 5 minutes ago
    }),
    createMockNotification({
      id: '2',
      title: 'Task Assignment',
      message: 'You have been assigned a new task',
      type: 'info',
      read: true,
      timestamp: Date.now() - 1000 * 60 * 60, // 1 hour ago
      actions: [
        { label: 'View Task', action: 'view_task' },
        { label: 'Accept', action: 'accept_task' },
      ],
    }),
    createMockNotification({
      id: '3',
      title: 'System Alert',
      message: 'System maintenance scheduled',
      type: 'warning',
      read: false,
      timestamp: Date.now() - 1000 * 60 * 60 * 24, // 1 day ago
    }),
  ]

  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    language: 'en' as const,
  }

  const mockInitialState = {
    auth: {
      user: {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: { id: 'employee', name: 'Employee', nameAr: 'موظف' },
      },
      token: 'mock-token',
      isAuthenticated: true,
      loading: false,
      error: null,
    },
    notifications: {
      notifications: mockNotifications,
      unreadCount: 2,
      settings: {
        enableSound: true,
        enableDesktop: true,
        enableEmail: false,
        autoMarkRead: false,
        groupSimilar: true,
      },
    },
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    it('should render when open', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      expect(screen.getByText('Notifications')).toBeInTheDocument()
      expect(screen.getByText('Welcome Message')).toBeInTheDocument()
      expect(screen.getByText('Task Assignment')).toBeInTheDocument()
      expect(screen.getByText('System Alert')).toBeInTheDocument()
    })

    it('should not render when closed', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} isOpen={false} />, {
        initialState: mockInitialState,
      })

      expect(screen.queryByText('Notifications')).not.toBeInTheDocument()
    })

    it('should render in Arabic', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} language="ar" />, {
        initialState: mockInitialState,
      })

      expect(screen.getByText('الإشعارات')).toBeInTheDocument()
      expect(screen.getByText('تحديد الكل كمقروء')).toBeInTheDocument()
    })

    it('should show unread count badge', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      expect(screen.getByText('2')).toBeInTheDocument() // Unread count
    })
  })

  describe('Notification Display', () => {
    it('should display notification details correctly', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      expect(screen.getByText('Welcome Message')).toBeInTheDocument()
      expect(screen.getByText('Welcome to the system')).toBeInTheDocument()
      expect(screen.getByText('5 minutes ago')).toBeInTheDocument()
    })

    it('should show notification type icons', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Check for notification type indicators (icons)
      const notifications = screen.getAllByRole('checkbox')
      expect(notifications).toHaveLength(3)
    })

    it('should display notification actions', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      expect(screen.getByText('View Task')).toBeInTheDocument()
      expect(screen.getByText('Accept')).toBeInTheDocument()
    })

    it('should show different styles for read/unread notifications', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const welcomeNotification = screen.getByText('Welcome Message').closest('div')
      const taskNotification = screen.getByText('Task Assignment').closest('div')

      // Unread notification should have different opacity
      expect(welcomeNotification).not.toHaveClass('opacity-75')
      expect(taskNotification).toHaveClass('opacity-75')
    })
  })

  describe('Filtering', () => {
    it('should filter by read status', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Click unread filter
      await user.click(screen.getByText('Unread'))

      // Should only show unread notifications
      expect(screen.getByText('Welcome Message')).toBeInTheDocument()
      expect(screen.getByText('System Alert')).toBeInTheDocument()
      expect(screen.queryByText('Task Assignment')).not.toBeInTheDocument()
    })

    it('should filter by notification type', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Click warning filter
      await user.click(screen.getByText('Warning'))

      // Should only show warning notifications
      expect(screen.getByText('System Alert')).toBeInTheDocument()
      expect(screen.queryByText('Welcome Message')).not.toBeInTheDocument()
      expect(screen.queryByText('Task Assignment')).not.toBeInTheDocument()
    })

    it('should search notifications', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const searchInput = screen.getByPlaceholderText(/search notifications/i)
      await user.type(searchInput, 'task')

      // Should only show notifications containing "task"
      expect(screen.getByText('Task Assignment')).toBeInTheDocument()
      expect(screen.queryByText('Welcome Message')).not.toBeInTheDocument()
      expect(screen.queryByText('System Alert')).not.toBeInTheDocument()
    })

    it('should combine filters', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Filter by unread and search for "welcome"
      await user.click(screen.getByText('Unread'))

      const searchInput = screen.getByPlaceholderText(/search notifications/i)
      await user.type(searchInput, 'welcome')

      // Should only show unread notifications containing "welcome"
      expect(screen.getByText('Welcome Message')).toBeInTheDocument()
      expect(screen.queryByText('Task Assignment')).not.toBeInTheDocument()
      expect(screen.queryByText('System Alert')).not.toBeInTheDocument()
    })
  })

  describe('Notification Actions', () => {
    it('should mark notification as read when clicked', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const welcomeNotification = screen.getByText('Welcome Message')
      await user.click(welcomeNotification)

      expect(store.dispatch).toHaveBeenCalledWith(markAsRead('1'))
    })

    it('should mark notification as read via button', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Find and click the mark as read button for the first notification
      const markReadButtons = screen.getAllByRole('button', { name: '' })
      const markReadButton = markReadButtons.find(button =>
        button.querySelector('svg') && button.closest('div')?.textContent?.includes('Welcome Message')
      )

      if (markReadButton) {
        await user.click(markReadButton)
        expect(store.dispatch).toHaveBeenCalledWith(markAsRead('1'))
      }
    })

    it('should delete notification', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Find and click the delete button for the first notification
      const deleteButtons = screen.getAllByRole('button', { name: '' })
      const deleteButton = deleteButtons.find(button =>
        button.querySelector('svg') &&
        button.closest('div')?.textContent?.includes('Welcome Message') &&
        button.className.includes('text-red-400')
      )

      if (deleteButton) {
        await user.click(deleteButton)
        expect(store.dispatch).toHaveBeenCalledWith(removeNotification('1'))
      }
    })

    it('should mark all as read', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      await user.click(screen.getByText('Mark All Read'))

      expect(store.dispatch).toHaveBeenCalledWith(markAllAsRead())
    })

    it('should clear all notifications', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      await user.click(screen.getByText('Clear All'))

      expect(store.dispatch).toHaveBeenCalledWith(markAllAsRead())
    })
  })

  describe('Bulk Actions', () => {
    it('should select multiple notifications', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[0])
      await user.click(checkboxes[1])

      // Should show bulk action buttons
      expect(screen.getByText(/mark read \(2\)/i)).toBeInTheDocument()
      expect(screen.getByText(/delete \(2\)/i)).toBeInTheDocument()
    })

    it('should perform bulk mark as read', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[0])
      await user.click(checkboxes[1])

      await user.click(screen.getByText(/mark read \(2\)/i))

      expect(store.dispatch).toHaveBeenCalledWith(markAsRead('1'))
      expect(store.dispatch).toHaveBeenCalledWith(markAsRead('2'))
    })

    it('should perform bulk delete', async () => {
      const user = userEvent.setup()
      const { store } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const checkboxes = screen.getAllByRole('checkbox')
      await user.click(checkboxes[0])
      await user.click(checkboxes[1])

      await user.click(screen.getByText(/delete \(2\)/i))

      expect(store.dispatch).toHaveBeenCalledWith(removeNotification('1'))
      expect(store.dispatch).toHaveBeenCalledWith(removeNotification('2'))
    })
  })

  describe('Settings', () => {
    it('should toggle settings panel', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const settingsButton = screen.getByRole('button', { name: /settings/i })
      await user.click(settingsButton)

      expect(screen.getByText('Notification Settings')).toBeInTheDocument()
      expect(screen.getByText('Enable Sound')).toBeInTheDocument()
      expect(screen.getByText('Desktop Notifications')).toBeInTheDocument()
    })
  })

  describe('Empty States', () => {
    it('should show empty state when no notifications', () => {
      const emptyState = {
        ...mockInitialState,
        notifications: {
          ...mockInitialState.notifications,
          notifications: [],
          unreadCount: 0,
        },
      }

      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: emptyState,
      })

      expect(screen.getByText('No notifications')).toBeInTheDocument()
    })

    it('should show empty state when no filtered results', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const searchInput = screen.getByPlaceholderText(/search notifications/i)
      await user.type(searchInput, 'nonexistent')

      expect(screen.getByText('No notifications')).toBeInTheDocument()
    })
  })

  describe('Time Formatting', () => {
    it('should format relative time correctly', () => {
      const now = Date.now()
      const customNotifications = [
        createMockNotification({
          id: '1',
          title: 'Just now',
          timestamp: now - 30000, // 30 seconds ago
        }),
        createMockNotification({
          id: '2',
          title: 'Minutes ago',
          timestamp: now - 1000 * 60 * 30, // 30 minutes ago
        }),
        createMockNotification({
          id: '3',
          title: 'Hours ago',
          timestamp: now - 1000 * 60 * 60 * 2, // 2 hours ago
        }),
      ]

      const customState = {
        ...mockInitialState,
        notifications: {
          ...mockInitialState.notifications,
          notifications: customNotifications,
        },
      }

      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: customState,
      })

      expect(screen.getByText('Just now')).toBeInTheDocument()
      expect(screen.getByText('30 minutes ago')).toBeInTheDocument()
      expect(screen.getByText('2 hours ago')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      const checkboxes = screen.getAllByRole('checkbox')
      expect(checkboxes).toHaveLength(3)

      const buttons = screen.getAllByRole('button')
      expect(buttons.length).toBeGreaterThan(0)
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()
      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Tab through interactive elements
      await user.tab()
      expect(document.activeElement).toBeInTheDocument()
    })
  })

  describe('RTL Support', () => {
    it('should apply RTL styles for Arabic', () => {
      renderWithProviders(<NotificationCenter {...defaultProps} language="ar" />, {
        initialState: mockInitialState,
      })

      const container = screen.getByText('الإشعارات').closest('div')
      expect(container).toHaveClass('left-0')
    })
  })

  describe('Performance', () => {
    it('should handle large number of notifications', () => {
      const largeNotificationList = Array.from({ length: 1000 }, (_, i) =>
        createMockNotification({
          id: `notification-${i}`,
          title: `Notification ${i}`,
          message: `Message ${i}`,
        })
      )

      const largeState = {
        ...mockInitialState,
        notifications: {
          ...mockInitialState.notifications,
          notifications: largeNotificationList,
          unreadCount: 500,
        },
      }

      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: largeState,
      })

      expect(screen.getByText('Notifications')).toBeInTheDocument()
      expect(screen.getByText('500')).toBeInTheDocument() // Unread count
    })

    it('should not re-render unnecessarily', () => {
      const { rerender } = renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: mockInitialState,
      })

      // Re-render with same props
      rerender(<NotificationCenter {...defaultProps} />)

      // Component should still be functional
      expect(screen.getByText('Notifications')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle missing notification data gracefully', () => {
      const invalidNotifications = [
        {
          id: '1',
          // Missing required fields
        } as any,
      ]

      const invalidState = {
        ...mockInitialState,
        notifications: {
          ...mockInitialState.notifications,
          notifications: invalidNotifications,
        },
      }

      renderWithProviders(<NotificationCenter {...defaultProps} />, {
        initialState: invalidState,
      })

      // Should not crash
      expect(screen.getByText('Notifications')).toBeInTheDocument()
    })
  })
})
