import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { KPITarget, KPI } from '@/services/kpiService'

interface TargetModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (targetData: Partial<KPITarget>) => Promise<void>
  target?: KPITarget | null
  kpis: KPI[]
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    createTarget: 'إنشاء هدف جديد',
    editTarget: 'تعديل الهدف',
    kpi: 'مؤشر الأداء',
    targetValue: 'القيمة المستهدفة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    description: 'الوصف',
    priority: 'الأولوية',
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    selectKPI: 'اختر مؤشر الأداء',
    high: 'عالية',
    medium: 'متوسطة',
    low: 'منخفضة'
  },
  en: {
    createTarget: 'Create New Target',
    editTarget: 'Edit Target',
    kpi: 'KPI',
    targetValue: 'Target Value',
    startDate: 'Start Date',
    endDate: 'End Date',
    description: 'Description',
    priority: 'Priority',
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    selectKPI: 'Select KPI',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  }
}

export default function TargetModal({ isOpen, onClose, onSave, target, kpis, language }: TargetModalProps) {
  const [formData, setFormData] = useState({
    kpi_id: '',
    target_value: '',
    start_date: '',
    end_date: '',
    description: '',
    priority: 'MEDIUM' as 'HIGH' | 'MEDIUM' | 'LOW',
    is_active: true
  })

  const [loading, setLoading] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  useEffect(() => {
    if (target) {
      setFormData({
        kpi_id: target.kpi_id || '',
        target_value: target.target_value?.toString() || '',
        start_date: target.start_date || '',
        end_date: target.end_date || '',
        description: target.description || '',
        priority: (target.priority as 'LOW' | 'MEDIUM' | 'HIGH') || 'MEDIUM',
        is_active: target.is_active ?? true
      })
    } else {
      // Reset form for new target
      const today = new Date().toISOString().split('T')[0]
      const nextMonth = new Date()
      nextMonth.setMonth(nextMonth.getMonth() + 1)
      const endDate = nextMonth.toISOString().split('T')[0]

      setFormData({
        kpi_id: '',
        target_value: '',
        start_date: today,
        end_date: endDate,
        description: '',
        priority: 'MEDIUM',
        is_active: true
      })
    }
  }, [target, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const submitData = {
        ...formData,
        target_value: parseFloat(formData.target_value) || 0
      }
      await onSave(submitData)
      onClose()
    } catch (error) {
      console.error('Error saving target:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`kpi-modal glass-card border-white/20 max-w-lg ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-white text-xl">
            {target ? t.editTarget : t.createTarget}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="kpi_id" className="text-white">{t.kpi}</Label>
            <Select value={formData.kpi_id} onValueChange={(value) => handleInputChange('kpi_id', value)}>
              <SelectTrigger className="glass-input">
                <SelectValue placeholder={t.selectKPI} />
              </SelectTrigger>
              <SelectContent className="glass-card border-white/20">
                {kpis.map((kpi) => (
                  <SelectItem key={kpi.id} value={kpi.id}>
                    {language === 'ar' ? kpi.name_ar : kpi.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="target_value" className="text-white">{t.targetValue}</Label>
              <Input
                id="target_value"
                type="number"
                step="0.01"
                value={formData.target_value}
                onChange={(e) => handleInputChange('target_value', e.target.value)}
                className="glass-input"
                required
              />
            </div>
            <div>
              <Label htmlFor="priority" className="text-white">{t.priority}</Label>
              <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                <SelectTrigger className="glass-input">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="HIGH">{t.high}</SelectItem>
                  <SelectItem value="MEDIUM">{t.medium}</SelectItem>
                  <SelectItem value="LOW">{t.low}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start_date" className="text-white">{t.startDate}</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => handleInputChange('start_date', e.target.value)}
                className="glass-input"
                required
              />
            </div>
            <div>
              <Label htmlFor="end_date" className="text-white">{t.endDate}</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => handleInputChange('end_date', e.target.value)}
                className="glass-input"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="text-white">{t.description}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="glass-input"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} className="glass-button">
              {t.cancel}
            </Button>
            <Button type="submit" disabled={loading} className="glass-button">
              {loading ? t.saving : t.save}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
