import React, { useState } from 'react'
import { UnifiedExportButton } from '../common/UnifiedExportButton'
import { Button } from '@/components/ui/button'
import CustomerReport from '../reports/CustomerReport'

// Customer data interface
interface CustomerData {
  id: number
  first_name: string
  last_name: string
  email: string
  phone: string
  customer_type: 'business' | 'enterprise' | 'individual'
  status: 'active' | 'inactive' | 'pending' | 'prospect'
  company_name?: string
  total_spent: number
  total_orders: number
  last_contact_date: string
}

/**
 * Test component to verify export functionality works
 */
export const ExportTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([])
  const [showCustomerReport, setShowCustomerReport] = useState(false)

  const sampleEmployeeData = [
    { id: 1, name: '<PERSON>', position: 'Manager', department: 'HR', salary: 15000 },
    { id: 2, name: '<PERSON><PERSON>', position: 'Developer', department: 'IT', salary: 12000 },
    { id: 3, name: '<PERSON>', position: 'Analyst', department: 'Finance', salary: 10000 }
  ]

  const sampleCustomerData: CustomerData[] = [
    {
      id: 1,
      first_name: '<PERSON>',
      last_name: 'Al-<PERSON>',
      email: '<EMAIL>',
      phone: '+966501234567',
      customer_type: 'business',
      status: 'active',
      company_name: 'Tech Solutions Ltd',
      total_spent: 125000,
      total_orders: 15,
      last_contact_date: '2024-01-15'
    },
    {
      id: 2,
      first_name: 'Fatima',
      last_name: 'Al-Zahra',
      email: '<EMAIL>',
      phone: '+966507654321',
      customer_type: 'enterprise',
      status: 'active',
      company_name: 'Enterprise Corp',
      total_spent: 350000,
      total_orders: 42,
      last_contact_date: '2024-01-20'
    },
    {
      id: 3,
      first_name: 'Mohammed',
      last_name: 'Al-Saud',
      email: '<EMAIL>',
      phone: '+966509876543',
      customer_type: 'individual',
      status: 'prospect',
      company_name: 'Individual Customer',
      total_spent: 5000,
      total_orders: 2,
      last_contact_date: '2024-01-10'
    }
  ]

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const testCSVExport = () => {
    addTestResult('Testing CSV export...')
    try {
      const csvData = generateCSV(sampleEmployeeData)
      downloadFile(csvData, 'test-export.csv', 'text/csv')
      addTestResult('✅ CSV export successful')
    } catch (error) {
      addTestResult(`❌ CSV export failed: ${error}`)
    }
  }



  const testCustomerReport = () => {
    addTestResult('Testing Customer HTML report...')
    try {
      const htmlData = generateCustomerHTMLReport(sampleCustomerData, 'en')
      downloadFile(htmlData, 'customer-report.html', 'text/html')
      addTestResult('✅ Customer HTML report generated successfully')
    } catch (error) {
      addTestResult(`❌ Customer report failed: ${error}`)
    }
  }

  const testCustomerReportArabic = () => {
    addTestResult('Testing Arabic Customer report...')
    try {
      const htmlData = generateCustomerHTMLReport(sampleCustomerData, 'ar')
      downloadFile(htmlData, 'customer-report-ar.html', 'text/html')
      addTestResult('✅ Arabic Customer report generated successfully')
    } catch (error) {
      addTestResult(`❌ Arabic Customer report failed: ${error}`)
    }
  }

  const generateCSV = (data: any[]): string => {
    if (!data || data.length === 0) return 'No data available'

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header =>
          JSON.stringify((row as any)[header] || '')
        ).join(',')
      )
    ].join('\n')

    return csvContent
  }



  const generateCustomerHTMLReport = (data: CustomerData[], language: 'ar' | 'en'): string => {
    const isRTL = language === 'ar'
    const translations = {
      ar: {
        customerReport: 'تقرير العملاء',
        totalCustomers: 'إجمالي العملاء',
        activeCustomers: 'العملاء النشطون',
        totalRevenue: 'إجمالي الإيرادات',
        customer: 'العميل',
        type: 'النوع',
        status: 'الحالة',
        contact: 'الاتصال',
        revenue: 'الإيرادات',
        individual: 'فردي',
        business: 'تجاري',
        enterprise: 'مؤسسي',
        active: 'نشط',
        inactive: 'غير نشط',
        prospect: 'محتمل'
      },
      en: {
        customerReport: 'Customer Report',
        totalCustomers: 'Total Customers',
        activeCustomers: 'Active Customers',
        totalRevenue: 'Total Revenue',
        customer: 'Customer',
        type: 'Type',
        status: 'Status',
        contact: 'Contact',
        revenue: 'Revenue',
        individual: 'Individual',
        business: 'Business',
        enterprise: 'Enterprise',
        active: 'Active',
        inactive: 'Inactive',
        prospect: 'Prospect'
      }
    }

    const t = translations[language]
    const totalCustomers = data.length
    const activeCustomers = data.filter(c => c.status === 'active').length
    const totalRevenue = data.reduce((sum, c) => sum + (c.total_spent || 0), 0)

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
        style: 'currency',
        currency: 'SAR'
      }).format(amount)
    }

    return `<!DOCTYPE html>
<html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
<head>
    <meta charset="UTF-8">
    <title>${t.customerReport}</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0; padding: 20px;
            direction: ${isRTL ? 'rtl' : 'ltr'};
        }
        .container {
            max-width: 1200px; margin: 0 auto;
            background: white; border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 40px; text-align: center;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .summary {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px; padding: 30px; background: #f8fafc;
        }
        .summary-card {
            background: white; padding: 25px; border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
        }
        .summary-card h3 { color: #64748b; font-size: 0.9rem; margin-bottom: 8px; }
        .summary-card p { color: #1e293b; font-size: 2rem; font-weight: 700; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; padding: 15px; text-align: ${isRTL ? 'right' : 'left'};
        }
        td { padding: 12px; border-bottom: 1px solid #e2e8f0; }
        tr:hover { background-color: #f8fafc; }
        .status-badge {
            padding: 4px 12px; border-radius: 20px; font-size: 0.8rem;
            font-weight: 600; text-transform: uppercase;
        }
        .status-active { background: #dcfce7; color: #166534; }
        .status-inactive { background: #f1f5f9; color: #475569; }
        .status-prospect { background: #dbeafe; color: #1d4ed8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${t.customerReport}</h1>
            <p>${new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>${t.totalCustomers}</h3>
                <p>${totalCustomers}</p>
            </div>
            <div class="summary-card">
                <h3>${t.activeCustomers}</h3>
                <p>${activeCustomers}</p>
            </div>
            <div class="summary-card">
                <h3>${t.totalRevenue}</h3>
                <p>${formatCurrency(totalRevenue)}</p>
            </div>
        </div>

        <div style="padding: 30px;">
            <table>
                <thead>
                    <tr>
                        <th>${t.customer}</th>
                        <th>${t.type}</th>
                        <th>${t.status}</th>
                        <th>${t.contact}</th>
                        <th>${t.revenue}</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(customer => `
                        <tr>
                            <td><strong>${customer.first_name} ${customer.last_name}</strong><br><small>${customer.company_name || ''}</small></td>
                            <td>${t[customer.customer_type as keyof typeof t] || customer.customer_type}</td>
                            <td><span class="status-badge status-${customer.status}">${t[customer.status as keyof typeof t] || customer.status}</span></td>
                            <td>${customer.email}<br>${customer.phone}</td>
                            <td><strong>${formatCurrency(customer.total_spent)}</strong></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>`
  }

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold mb-4">Export Functionality Test</h2>
        <p className="text-gray-600 mb-4">
          Test the export functionality with sample data. This works without backend connection.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Customer Export Tests - Main Focus */}
          <div className="space-y-2">
            <h3 className="font-semibold text-blue-600">📊 Customer Export (EN)</h3>
            <UnifiedExportButton
              dataType="customers"
              data={sampleCustomerData}
              language="en"
              showAdvanced={true}
            />
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold text-blue-600">📊 Customer Export (AR)</h3>
            <UnifiedExportButton
              dataType="customers"
              data={sampleCustomerData}
              language="ar"
              showAdvanced={true}
            />
          </div>

          {/* Employee Export Tests */}
          <div className="space-y-2">
            <h3 className="font-semibold">👥 Employee Export (EN)</h3>
            <UnifiedExportButton
              dataType="test-employees"
              data={sampleEmployeeData}
              language="en"
              showAdvanced={true}
            />
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">👥 Employee Export (AR)</h3>
            <UnifiedExportButton
              dataType="test-employees"
              data={sampleEmployeeData}
              language="ar"
              showAdvanced={true}
            />
          </div>

          {/* Manual Tests */}
          <div className="space-y-2">
            <h3 className="font-semibold">📄 Manual CSV Test</h3>
            <Button onClick={testCSVExport} className="w-full">
              Test CSV Export
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">📄 Customer HTML (EN)</h3>
            <Button onClick={testCustomerReport} className="w-full">
              Customer HTML Report
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">📄 Customer HTML (AR)</h3>
            <Button onClick={testCustomerReportArabic} className="w-full">
              تقرير العملاء
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">🎯 Professional Preview</h3>
            <Button
              onClick={() => setShowCustomerReport(!showCustomerReport)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              {showCustomerReport ? 'Hide Preview' : 'Show Customer Report'}
            </Button>
          </div>
        </div>

        {/* Sample Data Display */}
        <div className="mb-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-2">Sample Employee Data:</h3>
            <div className="bg-gray-100 p-4 rounded overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Position</th>
                    <th className="text-left p-2">Department</th>
                    <th className="text-left p-2">Salary</th>
                  </tr>
                </thead>
                <tbody>
                  {sampleEmployeeData.map(item => (
                    <tr key={item.id}>
                      <td className="p-2">{item.name}</td>
                      <td className="p-2">{item.position}</td>
                      <td className="p-2">{item.department}</td>
                      <td className="p-2">{item.salary.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Sample Customer Data:</h3>
            <div className="bg-gray-100 p-4 rounded overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left p-2">Name</th>
                    <th className="text-left p-2">Type</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Revenue</th>
                  </tr>
                </thead>
                <tbody>
                  {sampleCustomerData.map(customer => (
                    <tr key={customer.id}>
                      <td className="p-2">{customer.first_name} {customer.last_name}</td>
                      <td className="p-2 capitalize">{customer.customer_type}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          customer.status === 'active' ? 'bg-green-100 text-green-800' :
                          customer.status === 'prospect' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {customer.status}
                        </span>
                      </td>
                      <td className="p-2">{customer.total_spent.toLocaleString()} SAR</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-semibold">Test Results:</h3>
            <Button onClick={clearResults} variant="outline" size="sm">
              Clear Results
            </Button>
          </div>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-60 overflow-y-auto">
            {testResults.length === 0 ? (
              <div className="text-gray-500">No tests run yet...</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index}>{result}</div>
              ))
            )}
          </div>
        </div>

        {/* Professional Customer Report Preview */}
        {showCustomerReport && (
          <div className="mt-8">
            <h3 className="text-xl font-bold mb-4 text-blue-600">🎯 Professional Customer Report</h3>

            {/* English Version */}
            <div className="border-2 border-blue-200 rounded-lg overflow-hidden bg-blue-50 p-4 mb-6">
              <h4 className="text-lg font-bold mb-2 text-blue-600">English Version</h4>
              <div className="bg-white rounded-lg shadow-lg">
                <CustomerReport
                  customers={sampleCustomerData}
                  language="en"
                  title="Customer Analysis Report"
                  companyName="Enterprise Management System"
                  generatedBy="System Administrator"
                />
              </div>
            </div>

            {/* Arabic Version */}
            <div className="border-2 border-green-200 rounded-lg overflow-hidden bg-green-50 p-4">
              <h4 className="text-lg font-bold mb-2 text-green-600">Arabic Version (RTL)</h4>
              <div className="bg-white rounded-lg shadow-lg">
                <CustomerReport
                  customers={sampleCustomerData}
                  language="ar"
                  title="تقرير تحليل العملاء"
                  companyName="نظام إدارة المؤسسات"
                  generatedBy="مدير النظام"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ExportTest
