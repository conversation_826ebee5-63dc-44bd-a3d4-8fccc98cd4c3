import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Mail, Send, Users, AlertCircle, CheckCircle } from 'lucide-react'

interface EmailPDFModalProps {
  isOpen: boolean
  onClose: () => void
  pdfHistoryId: string
  pdfType: string
  language?: 'ar' | 'en'
}

interface Recipient {
  email: string
  name?: string
  message?: string
}

export const EmailPDFModal: React.FC<EmailPDFModalProps> = ({
  isOpen,
  onClose,
  pdfHistoryId,
  pdfType,
  language = 'en'
}) => {
  const [recipients, setRecipients] = useState<Recipient[]>([])
  const [currentEmail, setCurrentEmail] = useState('')
  const [currentName, setCurrentName] = useState('')
  const [subject, setSubject] = useState('')
  const [message, setMessage] = useState('')
  const [ccRecipients, setCcRecipients] = useState('')
  const [bccRecipients, setBccRecipients] = useState('')
  const [priority, setPriority] = useState('normal')
  const [emailTemplate, setEmailTemplate] = useState('default')
  const [isLoading, setIsLoading] = useState(false)
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [statusMessage, setStatusMessage] = useState('')

  const addRecipient = () => {
    if (currentEmail && currentEmail.includes('@')) {
      const newRecipient: Recipient = {
        email: currentEmail,
        name: currentName || undefined
      }
      setRecipients([...recipients, newRecipient])
      setCurrentEmail('')
      setCurrentName('')
    }
  }

  const removeRecipient = (index: number) => {
    setRecipients(recipients.filter((_, i) => i !== index))
  }

  const handleSendEmail = async () => {
    if (recipients.length === 0) {
      setStatus('error')
      setStatusMessage(language === 'ar' ? 'يرجى إضافة مستلم واحد على الأقل' : 'Please add at least one recipient')
      return
    }

    setIsLoading(true)
    setStatus('idle')

    try {
      const response = await fetch('http://localhost:8001/api/pdf/email/send/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
        },
        body: JSON.stringify({
          pdf_history_id: pdfHistoryId,
          recipients: recipients.map(r => r.email),
          subject: subject || (language === 'ar' ? `تقرير ${pdfType}` : `${pdfType} Report`),
          message,
          language,
          email_template: emailTemplate,
          cc_recipients: ccRecipients.split(',').map(email => email.trim()).filter(Boolean),
          bcc_recipients: bccRecipients.split(',').map(email => email.trim()).filter(Boolean),
          priority
        })
      })

      const result = await response.json()

      if (result.success) {
        setStatus('success')
        setStatusMessage(language === 'ar' ? 'تم إرسال البريد الإلكتروني بنجاح' : 'Email sent successfully')
        setTimeout(() => {
          onClose()
          resetForm()
        }, 2000)
      } else {
        setStatus('error')
        setStatusMessage(result.error || (language === 'ar' ? 'فشل في إرسال البريد الإلكتروني' : 'Failed to send email'))
      }
    } catch (error) {
      setStatus('error')
      setStatusMessage(language === 'ar' ? 'خطأ في الشبكة' : 'Network error')
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setRecipients([])
    setCurrentEmail('')
    setCurrentName('')
    setSubject('')
    setMessage('')
    setCcRecipients('')
    setBccRecipients('')
    setPriority('normal')
    setEmailTemplate('default')
    setStatus('idle')
    setStatusMessage('')
  }

  const handleClose = () => {
    onClose()
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {language === 'ar' ? 'إرسال PDF عبر البريد الإلكتروني' : 'Send PDF via Email'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status Message */}
          {status !== 'idle' && (
            <div className={`flex items-center gap-2 p-3 rounded-lg ${
              status === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
            }`}>
              {status === 'success' ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
              <span className="text-sm">{statusMessage}</span>
            </div>
          )}

          {/* Recipients */}
          <div className="space-y-3">
            <Label>{language === 'ar' ? 'المستلمون' : 'Recipients'}</Label>

            {/* Add Recipient */}
            <div className="flex gap-2">
              <Input
                placeholder={language === 'ar' ? 'البريد الإلكتروني' : 'Email address'}
                value={currentEmail}
                onChange={(e) => setCurrentEmail(e.target.value)}
                className="flex-1"
              />
              <Input
                placeholder={language === 'ar' ? 'الاسم (اختياري)' : 'Name (optional)'}
                value={currentName}
                onChange={(e) => setCurrentName(e.target.value)}
                className="flex-1"
              />
              <Button onClick={addRecipient} disabled={!currentEmail.includes('@')}>
                {language === 'ar' ? 'إضافة' : 'Add'}
              </Button>
            </div>

            {/* Recipients List */}
            {recipients.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {recipients.map((recipient, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    <span>{recipient.name || recipient.email}</span>
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeRecipient(index)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Subject */}
          <div className="space-y-2">
            <Label>{language === 'ar' ? 'الموضوع' : 'Subject'}</Label>
            <Input
              placeholder={language === 'ar' ? `تقرير ${pdfType}` : `${pdfType} Report`}
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
            />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <Label>{language === 'ar' ? 'الرسالة (اختياري)' : 'Message (optional)'}</Label>
            <Textarea
              placeholder={language === 'ar' ? 'رسالة مخصصة...' : 'Custom message...'}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
            />
          </div>

          {/* Advanced Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Priority */}
            <div className="space-y-2">
              <Label>{language === 'ar' ? 'الأولوية' : 'Priority'}</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">{language === 'ar' ? 'منخفضة' : 'Low'}</SelectItem>
                  <SelectItem value="normal">{language === 'ar' ? 'عادية' : 'Normal'}</SelectItem>
                  <SelectItem value="high">{language === 'ar' ? 'عالية' : 'High'}</SelectItem>
                  <SelectItem value="urgent">{language === 'ar' ? 'عاجلة' : 'Urgent'}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Email Template */}
            <div className="space-y-2">
              <Label>{language === 'ar' ? 'قالب البريد' : 'Email Template'}</Label>
              <Select value={emailTemplate} onValueChange={setEmailTemplate}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">{language === 'ar' ? 'افتراضي' : 'Default'}</SelectItem>
                  <SelectItem value="formal">{language === 'ar' ? 'رسمي' : 'Formal'}</SelectItem>
                  <SelectItem value="invoice">{language === 'ar' ? 'فاتورة' : 'Invoice'}</SelectItem>
                  <SelectItem value="report">{language === 'ar' ? 'تقرير' : 'Report'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* CC/BCC */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>{language === 'ar' ? 'نسخة كربونية (CC)' : 'CC Recipients'}</Label>
              <Input
                placeholder={language === 'ar' ? '<EMAIL>, <EMAIL>' : '<EMAIL>, <EMAIL>'}
                value={ccRecipients}
                onChange={(e) => setCcRecipients(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>{language === 'ar' ? 'نسخة مخفية (BCC)' : 'BCC Recipients'}</Label>
              <Input
                placeholder={language === 'ar' ? '<EMAIL>, <EMAIL>' : '<EMAIL>, <EMAIL>'}
                value={bccRecipients}
                onChange={(e) => setBccRecipients(e.target.value)}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              {language === 'ar' ? 'إلغاء' : 'Cancel'}
            </Button>
            <Button
              onClick={handleSendEmail}
              disabled={isLoading || recipients.length === 0}
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              {isLoading
                ? (language === 'ar' ? 'جاري الإرسال...' : 'Sending...')
                : (language === 'ar' ? 'إرسال' : 'Send Email')
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
