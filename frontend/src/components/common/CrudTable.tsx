/**
 * Generic CRUD Table Component
 * Reusable table with sorting, filtering, and actions for all entities
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  Plus,
  RefreshCw,
  Download,
  Upload,
  ChevronLeft,
  ChevronRight,
  ChevronDown
} from 'lucide-react'
import { UnifiedExportButton } from './UnifiedExportButton'

export interface TableColumn<T = Record<string, unknown>> {
  key: string
  label: string
  sortable?: boolean
  filterable?: boolean
  render?: (item: T) => React.ReactNode
  width?: string
  align?: 'left' | 'center' | 'right'
}

export interface TableAction<T = Record<string, unknown>> {
  label: string
  icon: React.ComponentType<{ className?: string }>
  onClick: (item: T) => void
  variant?: 'default' | 'destructive' | 'outline' | 'ghost'
  className?: string
  show?: (item: T) => boolean
}

export interface FilterOption {
  key: string
  label: string
  options: { value: string; label: string }[]
}

export interface CrudTableProps<T = Record<string, unknown>> {
  title: string
  data: T[]
  columns: TableColumn<T>[]
  actions?: TableAction<T>[]
  filters?: FilterOption[]
  loading?: boolean
  searchPlaceholder?: string
  language: 'ar' | 'en'

  // CRUD operations
  onCreate?: () => void
  onRefresh?: () => void
  onExport?: (format?: 'csv' | 'excel' | 'pdf') => void
  onImport?: (file: File) => void

  // Pagination
  currentPage?: number
  totalPages?: number
  pageSize?: number
  total?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void

  // Search and filter
  searchQuery?: string
  onSearchChange?: (query: string) => void
  activeFilters?: Record<string, string>
  onFilterChange?: (filters: Record<string, string>) => void

  // Sorting
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void

  // Selection
  selectable?: boolean
  selectedItems?: T[]
  onSelectionChange?: (items: T[]) => void

  // Bulk actions
  bulkActions?: TableAction[]
}

const translations = {
  ar: {
    search: 'بحث...',
    filter: 'تصفية',
    create: 'إنشاء جديد',
    refresh: 'تحديث',
    export: 'تصدير',
    import: 'استيراد',
    actions: 'الإجراءات',
    noData: 'لا توجد بيانات',
    loading: 'جاري التحميل...',
    page: 'صفحة',
    of: 'من',
    items: 'عنصر',
    selected: 'محدد',
    selectAll: 'تحديد الكل',
    clearSelection: 'إلغاء التحديد',
    bulkActions: 'إجراءات مجمعة',
    previous: 'السابق',
    next: 'التالي'
  },
  en: {
    search: 'Search...',
    filter: 'Filter',
    create: 'Create New',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    actions: 'Actions',
    noData: 'No data available',
    loading: 'Loading...',
    page: 'Page',
    of: 'of',
    items: 'items',
    selected: 'selected',
    selectAll: 'Select All',
    clearSelection: 'Clear Selection',
    bulkActions: 'Bulk Actions',
    previous: 'Previous',
    next: 'Next'
  }
}

export default function CrudTable<T = Record<string, unknown>>({
  title,
  data,
  columns,
  actions = [],
  filters = [],
  loading = false,
  searchPlaceholder,
  language,
  onCreate,
  onRefresh,
  onExport,
  onImport,
  currentPage = 1,
  totalPages = 1,
  pageSize = 20,
  total = 0,
  onPageChange,
  onPageSizeChange,
  searchQuery = '',
  onSearchChange,
  activeFilters = {},
  onFilterChange,
  sortBy,
  sortOrder,
  onSortChange,
  selectable = false,
  selectedItems = [],
  onSelectionChange,
  bulkActions = []
}: CrudTableProps<T>) {
  const [showFilters, setShowFilters] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Handle sort
  const handleSort = (columnKey: string) => {
    if (!onSortChange) return

    const newSortOrder = sortBy === columnKey && sortOrder === 'asc' ? 'desc' : 'asc'
    onSortChange(columnKey, newSortOrder)
  }

  // Handle selection
  const handleSelectAll = () => {
    if (!onSelectionChange) return

    if (selectedItems.length === data.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(data)
    }
  }

  const handleSelectItem = (item: T) => {
    if (!onSelectionChange) return

    const isSelected = selectedItems.some(selected => (selected as Record<string, unknown>).id === (item as Record<string, unknown>).id)
    if (isSelected) {
      onSelectionChange(selectedItems.filter(selected => (selected as Record<string, unknown>).id !== (item as Record<string, unknown>).id))
    } else {
      onSelectionChange([...selectedItems, item])
    }
  }

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && onImport) {
      onImport(file)
    }
    // Reset input
    event.target.value = ''
  }

  return (
    <Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <CardTitle className="text-white text-xl">{title}</CardTitle>

          {/* Header Actions */}
          <div className="flex flex-wrap items-center gap-2">
            {onCreate && (
              <Button onClick={onCreate} className="glass-button">
                <Plus className="h-4 w-4 mr-2" />
                {t.create}
              </Button>
            )}

            {onRefresh && (
              <Button onClick={onRefresh} variant="outline" className="glass-button" disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                {t.refresh}
              </Button>
            )}

            {/* Unified Export Button - replaces old export dropdown */}
            <UnifiedExportButton
              dataType={(title || 'data').toLowerCase().replace(/\s+/g, '-')}
              data={data}
              className="glass-button"
              variant="outline"
              showAdvanced={true}
              language={language}
            />

            {onImport && (
              <div>
                <input
                  type="file"
                  id="file-import"
                  className="hidden"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileImport}
                />
                <Button
                  onClick={() => document.getElementById('file-import')?.click()}
                  variant="outline"
                  className="glass-button"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {t.import}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          {onSearchChange && (
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
                <Input
                  placeholder={searchPlaceholder || t.search}
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="glass-input pl-10"
                />
              </div>
            </div>
          )}

          {/* Filter Toggle */}
          {filters.length > 0 && (
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              className="glass-button"
            >
              <Filter className="h-4 w-4 mr-2" />
              {t.filter}
            </Button>
          )}
        </div>

        {/* Filters */}
        {showFilters && filters.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-white/5 rounded-lg">
            {filters.map((filter) => (
              <div key={filter.key}>
                <label className="text-white text-sm mb-2 block">{filter.label}</label>
                <Select
                  value={activeFilters[filter.key] || 'all'}
                  onValueChange={(value) => {
                    const newValue = value === 'all' ? '' : value
                    onFilterChange?.({ ...activeFilters, [filter.key]: newValue })
                  }}
                >
                  <SelectTrigger className="glass-input">
                    <SelectValue placeholder={`Select ${filter.label}`} />
                  </SelectTrigger>
                  <SelectContent className="glass-card border-white/20">
                    <SelectItem value="all">All</SelectItem>
                    {filter.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        )}

        {/* Selection and Bulk Actions */}
        {selectable && selectedItems.length > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-500/20 rounded-lg">
            <span className="text-white text-sm">
              {selectedItems.length} {t.selected}
            </span>
            <div className="flex items-center gap-2">
              {bulkActions.map((action, actionIndex) => {
                const IconComponent = action.icon
                return (
                  <Button
                    key={actionIndex}
                    onClick={() => action.onClick(selectedItems as unknown as Record<string, unknown>)}
                    variant={action.variant || 'outline'}
                    size="sm"
                    className="glass-button"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {action.label}
                  </Button>
                )
              })}
              <Button
                onClick={() => onSelectionChange?.([])}
                variant="outline"
                size="sm"
                className="glass-button"
              >
                {t.clearSelection}
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-white/60 mr-2" />
            <span className="text-white/60">{t.loading}</span>
          </div>
        ) : data.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-white/60">{t.noData}</p>
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-white/20">
                    {selectable && (
                      <TableHead className="w-12">
                        <input
                          type="checkbox"
                          checked={selectedItems.length === data.length && data.length > 0}
                          onChange={handleSelectAll}
                          className="rounded border-white/20"
                        />
                      </TableHead>
                    )}
                    {columns.map((column) => (
                      <TableHead
                        key={column.key}
                        className={`text-white ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}`}
                        style={{ width: column.width }}
                      >
                        {column.sortable ? (
                          <Button
                            variant="ghost"
                            onClick={() => handleSort(column.key)}
                            className="text-white hover:text-white hover:bg-white/10 p-0 h-auto font-medium"
                          >
                            {column.label}
                            {sortBy === column.key && (
                              sortOrder === 'asc' ? (
                                <SortAsc className="h-4 w-4 ml-1" />
                              ) : (
                                <SortDesc className="h-4 w-4 ml-1" />
                              )
                            )}
                          </Button>
                        ) : (
                          column.label
                        )}
                      </TableHead>
                    ))}
                    {actions.length > 0 && (
                      <TableHead className="text-white text-center w-24">{t.actions}</TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((item, index) => (
                    <TableRow key={(item as Record<string, unknown>).id as string || index} className="border-white/10 hover:bg-white/5">
                      {selectable && (
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedItems.some(selected => (selected as Record<string, unknown>).id === (item as Record<string, unknown>).id)}
                            onChange={() => handleSelectItem(item)}
                            className="rounded border-white/20"
                          />
                        </TableCell>
                      )}
                      {columns.map((column) => (
                        <TableCell
                          key={column.key}
                          className={`text-white/90 ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}`}
                        >
                          {column.render ? (column.render(item) || '-') :
                            (typeof (item as Record<string, unknown>)[column.key] === 'object' && (item as Record<string, unknown>)[column.key] !== null)
                              ? JSON.stringify((item as Record<string, unknown>)[column.key])
                              : String((item as Record<string, unknown>)[column.key] || '-')
                          }
                        </TableCell>
                      ))}
                      {actions.length > 0 && (
                        <TableCell className="text-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="glass-card border-white/20">
                              {actions.map((action, actionIndex) => {
                                if (action.show && !action.show(item)) return null

                                const IconComponent = action.icon

                                return (
                                  <DropdownMenuItem
                                    key={actionIndex}
                                    onClick={() => action.onClick(item)}
                                    className={`text-white ${action.className || ''} ${action.variant === 'destructive' ? 'text-red-400 hover:text-red-300 hover:bg-red-500/10' : 'hover:bg-white/10'}`}
                                  >
                                    <IconComponent className="h-4 w-4 mr-2" />
                                    {action.label}
                                  </DropdownMenuItem>
                                )
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-white/60 text-sm">
                  {t.page} {currentPage} {t.of} {totalPages} ({total} {t.items})
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    onClick={() => onPageChange?.(currentPage - 1)}
                    disabled={currentPage === 1}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    {t.previous}
                  </Button>

                  <Button
                    onClick={() => onPageChange?.(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                  >
                    {t.next}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
