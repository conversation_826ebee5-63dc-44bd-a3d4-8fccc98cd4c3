/**
 * Navigation Configuration
 * Centralized navigation structure for different user roles
 */

import {
  Home,
  Users,
  Building,
  BarChart3,
  Settings,
  Calendar,
  Clock,
  TrendingUp,
  DollarSign,
  FolderOpen,
  Briefcase,
  Package,
  Truck,
  ShoppingCart,
  MessageSquare,
  Megaphone,
  FileText,
  Video,
  User,
  Boxes,
  ClipboardList,
  Workflow,
  Zap,
  Activity,
  Target,
  Shield,
  Server,
  Brain,
  Award,
  Headphones,
  HelpCircle,
  Star,
  Bot,
  type LucideIcon
} from 'lucide-react'

export interface NavigationItem {
  name: string
  icon: LucideIcon
  href?: string
  id?: string
  children?: NavigationItem[]
}

export interface NavigationConfig {
  [role: string]: NavigationItem[]
}

// Navigation translations interface
export interface NavigationTranslations {
  [key: string]: string
}

export const getRolePrefix = (role: string): string => {
  switch (role) {
    case 'super_admin': return '/admin'
    case 'admin': return '/admin'
    case 'hr_manager': return '/hr'
    case 'finance_manager': return '/finance'
    case 'sales_manager': return '/sales'
    case 'department_manager': return '/department'
    case 'employee': return '/employee'
    default: return '/employee'
  }
}

export const createNavigationConfig = (t: NavigationTranslations): NavigationConfig => ({
  super_admin: [
    { name: t.dashboard, icon: Home, href: '/' },
    { name: t.employees, icon: Users, href: '/admin/employees' },
    { name: t.departments, icon: Building, href: '/admin/departments' },
    { name: t.customers, icon: Users, href: '/admin/customers' },
    {
      name: t.hrManagement,
      icon: Users,
      id: 'hr',
      children: [
        { name: t.leaveManagement, icon: Calendar, href: '/admin/hr/leave' },
        { name: t.attendance, icon: Clock, href: '/admin/hr/attendance' },
        { name: t.performance, icon: TrendingUp, href: '/admin/hr/performance' },
        { name: t.payroll, icon: DollarSign, href: '/admin/hr/payroll' },
      ]
    },
    {
      name: t.sales,
      icon: ShoppingCart,
      id: 'sales',
      children: [
        { name: t.salesOrders, icon: ShoppingCart, href: '/admin/sales/orders' },
        { name: t.quotations, icon: FileText, href: '/admin/sales/quotations' },
        { name: t.salesPipeline, icon: TrendingUp, href: '/admin/sales/pipeline' },
      ]
    },
    {
      name: t.inventory,
      icon: Package,
      id: 'inventory',
      children: [
        { name: t.products, icon: Package, href: '/admin/products' },
        { name: t.inventory, icon: Boxes, href: '/admin/inventory' },
      ]
    },
    {
      name: t.analytics,
      icon: BarChart3,
      id: 'analytics',
      children: [
        { name: t.analytics, icon: BarChart3, href: '/admin/analytics' },
        { name: t.businessIntelligence, icon: TrendingUp, href: '/admin/business-intelligence' },
        { name: t.advancedDashboard, icon: Activity, href: '/admin/advanced-dashboard' },
      ]
    },
    {
      name: t.systemAdministration,
      icon: Server,
      id: 'system-admin',
      children: [
        { name: t.systemAdministration, icon: Server, href: '/admin/system' },
        { name: t.securityCenter, icon: Shield, href: '/admin/security' },
        { name: t.aiManagement, icon: Brain, href: '/admin/ai' },
        { name: t.complianceCenter, icon: Award, href: '/admin/compliance' },
      ]
    },
    {
      name: t.kpiManagement,
      icon: Target,
      id: 'kpi',
      children: [
        { name: t.kpiDashboard, icon: BarChart3, href: '/admin/kpi/dashboard' },
        { name: t.kpiManagement, icon: Target, href: '/admin/kpi/management' },
      ]
    },
    {
      name: t.automation,
      icon: Zap,
      id: 'automation',
      children: [
        { name: t.workflows, icon: Workflow, href: '/admin/workflows' },
        { name: t.reportGenerator, icon: FileText, href: '/admin/report-generator' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.messages, icon: MessageSquare, href: '/admin/communication/messages' },
        { name: t.announcements, icon: Megaphone, href: '/admin/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/admin/communication/documents' },
        { name: t.meetings, icon: Video, href: '/admin/communication/meetings' },
      ]
    },
    { name: t.reports, icon: BarChart3, href: '/admin/reports' },
    { name: t.settings, icon: Settings, href: '/admin/settings' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/admin/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/admin/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/admin/calendar' },
      ]
    },
  ],

  admin: [
    { name: t.dashboard, icon: Home, href: '/' },
    { name: t.employees, icon: Users, href: '/admin/employees' },
    { name: t.departments, icon: Building, href: '/admin/departments' },
    { name: t.customers, icon: Users, href: '/admin/customers' },
    {
      name: t.hrManagement,
      icon: Users,
      id: 'hr',
      children: [
        { name: t.leaveManagement, icon: Calendar, href: '/admin/hr/leave' },
        { name: t.attendance, icon: Clock, href: '/admin/hr/attendance' },
        { name: t.performance, icon: TrendingUp, href: '/admin/hr/performance' },
        { name: t.payroll, icon: DollarSign, href: '/admin/hr/payroll' },
      ]
    },
    {
      name: t.sales,
      icon: ShoppingCart,
      id: 'sales',
      children: [
        { name: t.salesOrders, icon: ShoppingCart, href: '/admin/sales/orders' },
        { name: t.quotations, icon: FileText, href: '/admin/sales/quotations' },
        { name: t.salesPipeline, icon: TrendingUp, href: '/admin/sales/pipeline' },
      ]
    },
    {
      name: t.inventory,
      icon: Package,
      id: 'inventory',
      children: [
        { name: t.products, icon: Package, href: '/admin/products' },
        { name: t.inventory, icon: Boxes, href: '/admin/inventory' },
      ]
    },
    {
      name: t.analytics,
      icon: BarChart3,
      id: 'analytics',
      children: [
        { name: t.businessIntelligence, icon: TrendingUp, href: '/admin/business-intelligence' },
      ]
    },
    {
      name: t.automation,
      icon: Zap,
      id: 'automation',
      children: [
        { name: t.workflows, icon: Workflow, href: '/admin/workflows' },
        { name: t.reportGenerator, icon: FileText, href: '/admin/report-generator' },
      ]
    },
    {
      name: t.customerService,
      icon: Headphones,
      id: 'customer-service',
      children: [
        { name: t.customerSupportHub, icon: Headphones, href: '/admin/support/hub' },
        { name: t.supportDashboard, icon: BarChart3, href: '/admin/support/dashboard' },
        { name: t.ticketManagement, icon: ClipboardList, href: '/admin/support/tickets' },
        { name: t.knowledgeBaseManagement, icon: HelpCircle, href: '/admin/support/knowledge-base' },
        { name: t.liveChat, icon: MessageSquare, href: '/admin/support/live-chat' },
        { name: t.customerFeedback, icon: Star, href: '/admin/support/feedback' },
        { name: t.agentPerformance, icon: Award, href: '/admin/support/agents' },
        { name: t.aiChatAssistant, icon: Bot, href: '/admin/support/ai-chat' },
        { name: t.automationDashboard, icon: Settings, href: '/admin/support/automation' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.messages, icon: MessageSquare, href: '/admin/communication/messages' },
        { name: t.announcements, icon: Megaphone, href: '/admin/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/admin/communication/documents' },
        { name: t.meetings, icon: Video, href: '/admin/communication/meetings' },
      ]
    },
    { name: t.reports, icon: BarChart3, href: '/admin/reports' },
    { name: t.settings, icon: Settings, href: '/admin/settings' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/admin/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/admin/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/admin/calendar' },
      ]
    },
  ],

  hr_manager: [
    { name: t.dashboard, icon: Home, href: '/hr/dashboard' },
    { name: t.employees, icon: Users, href: '/hr/employees' },
    { name: t.departments, icon: Building, href: '/hr/departments' },
    {
      name: t.hrManagement,
      icon: Users,
      id: 'hr',
      children: [
        { name: t.leaveManagement, icon: Calendar, href: '/hr/leave' },
        { name: t.attendance, icon: Clock, href: '/hr/attendance' },
        { name: t.performance, icon: TrendingUp, href: '/hr/performance' },
        { name: t.payroll, icon: DollarSign, href: '/hr/payroll' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.messages, icon: MessageSquare, href: '/hr/communication/messages' },
        { name: t.announcements, icon: Megaphone, href: '/hr/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/hr/communication/documents' },
        { name: t.meetings, icon: Video, href: '/hr/communication/meetings' },
      ]
    },
    { name: t.reports, icon: BarChart3, href: '/hr/reports' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/hr/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/hr/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/hr/calendar' },
      ]
    },
  ],

  sales_manager: [
    { name: t.dashboard, icon: Home, href: '/sales/dashboard' },
    { name: t.customers, icon: Users, href: '/sales/customers' },
    {
      name: t.sales,
      icon: ShoppingCart,
      id: 'sales',
      children: [
        { name: t.salesOrders, icon: ShoppingCart, href: '/sales/orders' },
        { name: t.quotations, icon: FileText, href: '/sales/quotations' },
        { name: t.salesPipeline, icon: TrendingUp, href: '/sales/pipeline' },
      ]
    },
    { name: t.products, icon: Package, href: '/sales/products' },
    { name: t.analytics, icon: BarChart3, href: '/sales/analytics' },
    { name: t.reports, icon: BarChart3, href: '/sales/reports' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/sales/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/sales/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/sales/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/sales/communication/announcements' },
      ]
    },
  ],

  finance_manager: [
    { name: t.dashboard, icon: Home, href: '/finance/dashboard' },
    {
      name: t.financialManagement,
      icon: DollarSign,
      id: 'finance',
      children: [
        { name: t.budgets, icon: DollarSign, href: '/finance/budgets' },
        { name: t.expenses, icon: TrendingUp, href: '/finance/expenses' },
        { name: t.financialReports, icon: BarChart3, href: '/finance/reports' },
      ]
    },
    {
      name: t.assetManagement,
      icon: Package,
      id: 'assets',
      children: [
        { name: t.assets, icon: Package, href: '/finance/assets' },
        { name: t.suppliers, icon: Truck, href: '/finance/suppliers' },
        { name: t.purchaseOrders, icon: ShoppingCart, href: '/finance/purchase-orders' },
      ]
    },
    { name: t.inventory, icon: Boxes, href: '/finance/inventory' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/finance/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/finance/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/finance/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/finance/communication/announcements' },
      ]
    },
  ],

  department_manager: [
    { name: t.dashboard, icon: Home, href: '/department/dashboard' },
    {
      name: t.projectManagement,
      icon: Briefcase,
      id: 'projects',
      children: [
        { name: t.projects, icon: FolderOpen, href: '/department/projects' },
        { name: t.tasks, icon: BarChart3, href: '/department/tasks' },
        { name: t.projectReports, icon: BarChart3, href: '/department/reports' },
      ]
    },
    { name: t.departments, icon: Building, href: '/department/departments' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/department/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/department/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/department/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/department/communication/announcements' },
      ]
    },
  ],

  employee: [
    { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/employee/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/employee/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/employee/calendar' },
      ]
    },
    {
      name: t.myWork,
      icon: Briefcase,
      id: 'work',
      children: [
        { name: t.employeeLeave, icon: Calendar, href: '/employee/leave' },
        { name: t.myTasks, icon: ClipboardList, href: '/employee/tasks' },
        { name: t.assignedProjects, icon: FolderOpen, href: '/employee/projects' },
      ]
    },
    {
      name: t.companyInfo,
      icon: Building,
      id: 'company',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/employee/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/employee/communication/documents' },
      ]
    },
  ],
})

export const getNavigationForRole = (userRole: string, t: NavigationTranslations): NavigationItem[] => {
  const config = createNavigationConfig(t)
  return config[userRole] || config.employee
}
