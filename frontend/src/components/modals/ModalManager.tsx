/**
 * Modal Manager
 * Centralized component that renders all modals based on the modal context state
 */

import React from 'react'
import { useModal } from '@/contexts/ModalContext'
import KPIModal from '@/components/kpi/KPIModal'
import ValueModal from '@/components/kpi/ValueModal'
import KPIValueModal from '@/components/kpi/KPIValueModal'
import CategoryModal from '@/components/kpi/CategoryModal'
import TargetModal from '@/components/kpi/TargetModal'
import DeleteConfirmModal from '@/components/common/DeleteConfirmModal'
import ExportModal from '@/components/common/ExportModal'
import ImportModal from '@/components/common/ImportModal'

interface ModalManagerProps {
  language: 'ar' | 'en'
}

// Type definitions for modal meta data
interface ModalMeta {
  onSave?: (...args: any[]) => Promise<void>
  onConfirm?: () => Promise<void>
  categories?: any[]
  value?: any
  title?: string
  message?: string
  itemName?: string
  formats?: Array<'csv' | 'excel' | 'pdf'>
  onImport?: (file: File) => Promise<{ success: number; errors: any[] }>
  acceptedFormats?: string[]
}

export const ModalManager: React.FC<ModalManagerProps> = ({ language }) => {
  const { modalState, closeModal, updateModalData } = useModal()
  const { isOpen, type, data, meta } = modalState

  // Don't render anything if no modal is open
  if (!isOpen || !type) {
    return null
  }

  // Render the appropriate modal based on type
  const modalMeta = meta as ModalMeta

  switch (type) {
    case 'kpi-create':
    case 'kpi-edit':
    case 'kpi-view':
      return (
        <KPIModal
          isOpen={isOpen}
          onClose={closeModal}
          onSave={modalMeta.onSave || (() => Promise.resolve())}
          kpi={type === 'kpi-create' ? null : (data as any)}
          categories={modalMeta.categories || []}
          language={language}
          mode={type === 'kpi-create' ? 'create' : type === 'kpi-edit' ? 'edit' : 'view'}
        />
      )

    case 'kpi-value-add':
    case 'kpi-value-edit':
      return (
        <KPIValueModal
          isOpen={isOpen}
          onClose={closeModal}
          onSave={modalMeta.onSave || (() => Promise.resolve())}
          kpi={data as any}
          value={type === 'kpi-value-edit' ? modalMeta.value : null}
          language={language}
          mode={type === 'kpi-value-edit' ? 'edit' : 'create'}
        />
      )

    case 'kpi-category':
      return (
        <CategoryModal
          isOpen={isOpen}
          onClose={closeModal}
          onSave={meta.onSave || (() => Promise.resolve())}
          category={data}
          language={language}
        />
      )

    case 'kpi-target':
      return (
        <TargetModal
          isOpen={isOpen}
          onClose={closeModal}
          onSave={meta.onSave || (() => Promise.resolve())}
          kpi={data}
          language={language}
        />
      )

    case 'kpi-delete-confirm':
      return (
        <DeleteConfirmModal
          isOpen={isOpen}
          onClose={closeModal}
          onConfirm={meta.onConfirm || (() => Promise.resolve())}
          title={meta.title || 'Confirm Delete'}
          message={meta.message || 'Are you sure you want to delete this item?'}
          itemName={meta.itemName || ''}
          language={language}
        />
      )

    case 'kpi-export':
      return (
        <ExportModal
          isOpen={isOpen}
          onClose={closeModal}
          onExport={meta.onExport || (() => Promise.resolve())}
          title={meta.title || 'Export Data'}
          formats={meta.formats || ['csv', 'excel', 'pdf']}
          language={language}
        />
      )

    case 'kpi-import':
      return (
        <ImportModal
          isOpen={isOpen}
          onClose={closeModal}
          onImport={meta.onImport || (() => Promise.resolve())}
          title={meta.title || 'Import Data'}
          acceptedFormats={meta.acceptedFormats || ['.csv', '.xlsx']}
          language={language}
        />
      )

    default:
      console.warn(`Unknown modal type: ${type}`)
      return null
  }
}

export default ModalManager
