INFO 2025-07-16 04:55:25,396 autoreload 60917 8360664832 Watching for file changes with StatReloader
WARNING 2025-07-16 04:55:38,440 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:55:38,441 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:56:08,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:56:08,397 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:56:38,405 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:56:38,407 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:57:08,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:57:08,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:57:38,396 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:57:38,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:58:08,398 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:58:08,399 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:58:38,395 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:58:38,396 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:59:08,580 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:59:08,582 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 04:59:38,399 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 04:59:38,400 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:00:08,407 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:00:08,408 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:00:38,428 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:00:38,429 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:01:08,407 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:01:08,408 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:01:38,405 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:01:38,406 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:02:08,401 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:02:08,404 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:02:38,401 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:02:38,403 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:02:40,705 basehttp 60917 6173765632 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:40,706 middleware 60917 6173765632 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 05:02:40,958 basehttp 60917 6173765632 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 05:02:40,996 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:02:40,996 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,004 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,014 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:02:41,020 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:02:41,022 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:02:41,030 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:02:41,367 basehttp 60917 6190592000 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,367 basehttp 60917 6207418368 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:02:41,423 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:02:41,429 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
WARNING 2025-07-16 05:03:08,413 log 60917 6156939264 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:03:08,414 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:03:11,014 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,823 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:03:37,823 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:03:37,839 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:03:37,843 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:03:37,878 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,880 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:03:37,887 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:03:37,889 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:03:38,192 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:03:38,204 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
WARNING 2025-07-16 05:03:38,399 log 60917 6207418368 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:03:38,399 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:04:07,884 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:04:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:04:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:04:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:04:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:04:38,400 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:05:07,879 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:05:08,398 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:05:08,398 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:05:37,883 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:05:38,396 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:05:38,396 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:06:07,880 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:06:08,397 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:06:08,397 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:06:37,881 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:06:38,398 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:06:38,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:07:07,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:07:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:07:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:07:37,887 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:07:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:07:38,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:07,878 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:08:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:08:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:37,882 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:08:38,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:08:38,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:08:38,412 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:09:07,884 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:09:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:09:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:09:37,887 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:09:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:09:38,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:10:07,885 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:10:08,399 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:10:08,399 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:10:37,892 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:10:38,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:10:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:11:07,886 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:11:08,400 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:11:08,400 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:11:37,889 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:11:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:11:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:12:07,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:12:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:12:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:12:37,905 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:12:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:12:38,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:07,902 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:13:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:13:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:37,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:13:38,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:13:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:13:38,574 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:14:07,896 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:14:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:14:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:14:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:14:38,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:14:38,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:15:07,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:15:08,401 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:15:08,401 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:15:37,926 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:15:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:15:38,407 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:16:07,906 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:16:08,411 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:16:08,411 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:16:37,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:16:38,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:16:38,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:17:07,893 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:17:08,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:17:08,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:17:37,921 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:17:38,431 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:17:38,432 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:07,891 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:18:08,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:18:08,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:37,888 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:18:38,402 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:18:38,402 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:18:38,796 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:19:07,889 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:19:08,403 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:19:08,403 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:19:37,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:19:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:19:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:20:07,904 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:20:08,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:20:08,404 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:20:37,922 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:20:38,408 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:20:38,408 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:21:07,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:21:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:21:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:21:37,906 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:21:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:21:38,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:22:07,893 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:22:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:22:08,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:22:37,897 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:22:38,404 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:22:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:07,895 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:23:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:23:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:37,897 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:23:38,407 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:23:38,407 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:23:38,963 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:24:07,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:24:08,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:24:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:24:37,895 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:24:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:24:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:25:07,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:25:08,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:25:08,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:25:37,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:25:38,405 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:25:38,405 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:07,898 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:08,409 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:26:08,411 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:37,894 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:38,406 log 60917 6173765632 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 05:26:38,406 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,223 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:26:51,247 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:26:51,296 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:26:51,297 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:26:51,312 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:26:51,312 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 05:26:51,530 log 60917 6207418368 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:26:51,530 basehttp 60917 6207418368 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,545 basehttp 60917 6207418368 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,547 log 60917 6224244736 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:26:51,547 basehttp 60917 6224244736 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,550 middleware 60917 6207418368 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 05:26:51,557 middleware 60917 6224244736 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 05:26:51,564 auth_views 60917 6207418368 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 05:26:51,564 log 60917 6207418368 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 05:26:51,565 basehttp 60917 6207418368 "POST /api/auth/refresh/ HTTP/1.1" 500 52
ERROR 2025-07-16 05:26:51,568 auth_views 60917 6224244736 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 05:26:51,568 log 60917 6224244736 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 05:26:51,568 basehttp 60917 6224244736 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 05:26:51,571 basehttp 60917 6207418368 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 05:26:51,573 basehttp 60917 6224244736 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,575 log 60917 6207418368 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,575 basehttp 60917 6207418368 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:26:51,578 log 60917 6224244736 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,578 basehttp 60917 6224244736 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,580 basehttp 60917 6241071104 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 05:26:51,580 basehttp 60917 6257897472 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:26:51,586 log 60917 6257897472 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,587 log 60917 6241071104 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 05:26:51,588 basehttp 60917 6257897472 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:26:51,589 basehttp 60917 6241071104 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 05:26:51,615 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:26:51,638 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:27:21,298 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:27:51,296 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:28:21,274 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:28:51,279 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:29:21,271 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:29:51,269 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:12,866 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:12,882 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:12,934 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:12,955 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:12,963 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:13,043 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:13,250 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:13,263 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:25,895 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:25,916 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:30:25,960 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:25,976 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:26,000 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:30:26,007 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:30:26,353 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:26,382 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:30:55,986 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:25,979 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,529 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:31:45,547 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:31:45,636 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,687 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:31:45,736 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:31:45,765 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:31:45,991 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:31:46,014 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:15,632 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,789 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:21,810 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:21,846 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,866 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:21,888 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:21,905 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:22,242 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:22,263 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:35,517 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:35,549 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:35,596 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:35,840 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:35,859 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:35,914 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:36,355 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:36,365 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:48,359 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:48,376 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:48,685 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:48,723 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:48,743 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:48,798 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:48,810 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:48,830 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:59,222 basehttp 60917 6190592000 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:59,257 basehttp 60917 6190592000 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:32:59,294 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:59,305 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:59,314 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:32:59,349 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:32:59,633 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:32:59,755 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:33:29,290 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:33:59,294 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:34:29,292 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:34:59,293 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:35:29,291 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:35:59,295 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,555 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:19,573 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:19,640 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,656 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:19,664 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:19,679 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:19,952 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:19,964 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:31,098 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:31,115 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:31,155 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:31,158 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:31,230 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:31,240 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:31,565 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:31,590 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 220
INFO 2025-07-16 05:36:39,576 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:39,593 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:36:39,668 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:39,678 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:39,694 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:36:39,706 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:36:40,004 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:36:40,023 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:37:09,647 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:37:39,646 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:38:09,646 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:38:39,648 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:09,654 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:39,645 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,106 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:39:57,117 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:39:57,196 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,226 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:39:57,265 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:39:57,277 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:39:57,554 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:39:57,596 basehttp 60917 6156939264 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:40:27,178 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:40:57,178 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:41:27,186 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:41:57,182 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:42:27,185 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:42:57,183 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:43:05,301 autoreload 65504 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 05:43:27,184 basehttp 60917 6156939264 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:44:31,963 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:44:31,972 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:44:31,993 log 60917 6156939264 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:44:31,994 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 05:44:31,998 log 60917 6173765632 Unauthorized: /api/auth/user/
WARNING 2025-07-16 05:44:31,999 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 05:46:04,466 basehttp 60917 6156939264 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,468 middleware 60917 6156939264 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 05:46:04,700 basehttp 60917 6156939264 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 05:46:04,728 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,729 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,730 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,731 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:04,740 basehttp 60917 6156939264 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:04,740 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:04,748 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:04,759 basehttp 60917 6190592000 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:05,073 basehttp 60917 6190592000 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:05,074 basehttp 60917 6173765632 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:05,079 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:46:05,086 basehttp 60917 6190592000 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:46:25,629 basehttp 60917 6190592000 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,629 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,651 basehttp 60917 6190592000 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:25,662 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:25,705 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:46:25,711 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:46:34,737 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,878 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:44,881 basehttp 60917 6190592000 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:46:44,886 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:46:44,891 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:46:44,926 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,931 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:44,934 basehttp 60917 6190592000 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:46:44,937 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:46:45,233 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:46:45,249 basehttp 60917 6173765632 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:47:14,929 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:47:44,109 basehttp 60917 6156939264 "OPTIONS /api/kpis/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6173765632 "OPTIONS /api/kpis/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6207418368 "OPTIONS /api/kpi-dashboard/ HTTP/1.1" 200 0
INFO 2025-07-16 05:47:44,109 basehttp 60917 6190592000 "OPTIONS /api/kpi-dashboard/ HTTP/1.1" 200 0
WARNING 2025-07-16 05:47:44,113 log 60917 6190592000 Not Found: /api/kpi-dashboard/
WARNING 2025-07-16 05:47:44,114 log 60917 6207418368 Not Found: /api/kpis/
WARNING 2025-07-16 05:47:44,114 basehttp 60917 6207418368 "GET /api/kpis/?page=1&page_size=20 HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,114 basehttp 60917 6190592000 "GET /api/kpi-dashboard/ HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,117 log 60917 6190592000 Not Found: /api/kpi-dashboard/
WARNING 2025-07-16 05:47:44,118 log 60917 6207418368 Not Found: /api/kpis/
WARNING 2025-07-16 05:47:44,118 basehttp 60917 6190592000 "GET /api/kpi-dashboard/ HTTP/1.1" 404 179
WARNING 2025-07-16 05:47:44,118 basehttp 60917 6207418368 "GET /api/kpis/?page=1&page_size=20 HTTP/1.1" 404 179
INFO 2025-07-16 05:47:44,928 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:48:14,938 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,110 basehttp 60917 6156939264 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,113 basehttp 60917 6173765632 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,159 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:50:26,165 basehttp 60917 6156939264 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:50:26,194 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,195 basehttp 60917 6173765632 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,198 basehttp 60917 6156939264 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,199 basehttp 60917 6190592000 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,202 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,206 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:50:26,209 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:50:26,212 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:50:26,511 basehttp 60917 6207418368 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,511 basehttp 60917 6173765632 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 05:50:26,518 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 05:50:26,529 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:50:56,214 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:10,254 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,254 basehttp 60917 6207418368 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,279 basehttp 60917 6207418368 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:10,293 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:10,333 basehttp 60917 6173765632 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 05:51:10,340 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:26,210 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:36,851 basehttp 60917 6173765632 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 8983
INFO 2025-07-16 05:51:56,204 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,100 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:51:58,112 basehttp 60917 6173765632 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 05:51:58,136 basehttp 60917 6173765632 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:51:58,136 basehttp 60917 6207418368 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,151 basehttp 60917 6173765632 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 05:51:58,152 basehttp 60917 6207418368 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 05:51:58,456 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:51:58,466 basehttp 60917 6207418368 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 05:57:29,159 autoreload 60917 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 05:57:29,159 autoreload 65504 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 05:57:29,666 autoreload 66063 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 05:57:29,782 autoreload 66062 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:01:42,060 autoreload 66342 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:02:28,280 autoreload 66417 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:03:01,168 autoreload 66508 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:04:52,573 basehttp 66508 6156185600 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,576 middleware 66508 6156185600 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 06:04:52,826 basehttp 66508 6156185600 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 06:04:52,854 basehttp 66508 6156185600 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,857 basehttp 66508 6173011968 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,857 basehttp 66508 6189838336 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:52,864 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:04:52,869 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:04:52,872 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:04:52,877 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:04:53,237 basehttp 66508 6156185600 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:53,237 basehttp 66508 6173011968 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:04:53,258 basehttp 66508 6156185600 "GET /api/dashboard-stats/ HTTP/1.1" 200 219
INFO 2025-07-16 06:04:53,263 basehttp 66508 6156185600 "GET /api/dashboard-stats/ HTTP/1.1" 200 220
INFO 2025-07-16 06:05:17,579 basehttp 66508 6156185600 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,580 basehttp 66508 6173011968 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,610 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:17,621 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:17,668 basehttp 66508 6173011968 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:05:17,675 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:22,865 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:05:30,664 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:05:52,882 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:06:22,878 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:06:52,876 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:06,515 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:22,868 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,772 basehttp 66508 6173011968 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:07:25,773 basehttp 66508 6156185600 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:07:25,784 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:07:25,790 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:07:25,817 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,820 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:07:25,824 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:07:25,826 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:07:26,121 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:26,129 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:26,196 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:48,880 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:07:55,837 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:08:25,847 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:08:55,837 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:09:25,841 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:09:55,845 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:10:25,824 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:10:39,813 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9227
INFO 2025-07-16 06:10:55,830 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:11:25,829 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:11:55,826 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:12:25,829 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:12:55,844 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:13:25,837 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:13:55,831 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:14:25,828 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:14:55,838 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:25,838 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,077 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:15:27,082 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:15:27,144 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,161 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:15:27,181 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:15:27,200 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:15:27,424 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:27,431 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:27,491 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9286
INFO 2025-07-16 06:15:57,136 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:27,136 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,326 basehttp 66508 6173011968 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:16:49,336 basehttp 66508 6173011968 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:16:49,362 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,364 basehttp 66508 6173011968 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:16:49,374 basehttp 66508 6189838336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:16:49,375 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:16:49,666 basehttp 66508 6189838336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:16:49,678 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:16:49,742 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:17:12,223 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:17:19,361 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:17:49,362 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:18:19,314 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9321
INFO 2025-07-16 06:18:19,390 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:18:49,368 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:19,378 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,592 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:19:25,598 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:19:25,624 basehttp 66508 6189838336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,628 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:19:25,628 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:19:25,632 basehttp 66508 6189838336 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:19:25,930 basehttp 66508 6189838336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:25,942 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:26,000 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:19:55,645 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:20:25,639 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:20:55,638 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:25,662 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,756 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:21:39,761 basehttp 66508 6156185600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:21:39,796 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,799 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:21:39,808 basehttp 66508 6173011968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:21:39,811 basehttp 66508 6156185600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:21:40,099 basehttp 66508 6156185600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:21:40,109 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:21:40,169 basehttp 66508 6173011968 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:22:09,804 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:22:39,803 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:23:09,802 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:23:39,814 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:24:09,805 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:24:39,804 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:25:09,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:25:39,811 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:26:09,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:26:19,194 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,420 log 68078 8360664832 Bad Request: /api/employees/
ERROR 2025-07-16 06:26:19,420 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/departments/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/dashboard-stats/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/user-profile/
ERROR 2025-07-16 06:26:19,421 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,421 log 68078 8360664832 Bad Request: /api/activities/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/roles/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/user-profiles/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,422 log 68078 8360664832 Bad Request: /api/performance-reviews/
ERROR 2025-07-16 06:26:19,422 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/payroll/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/recruitment/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,423 log 68078 8360664832 Bad Request: /api/training/
ERROR 2025-07-16 06:26:19,423 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/invoices/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/cost-centers/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/export/employees/
ERROR 2025-07-16 06:26:19,424 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,424 log 68078 8360664832 Bad Request: /api/export/departments/
ERROR 2025-07-16 06:26:19,425 exception 68078 8360664832 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/http/request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-07-16 06:26:19,425 log 68078 8360664832 Bad Request: /api/export/attendance/
INFO 2025-07-16 06:26:39,813 basehttp 66508 6156185600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:26:41,850 autoreload 66062 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:26:41,867 autoreload 66508 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:26:42,258 autoreload 68125 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:26:42,382 autoreload 68124 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:27:09,827 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:27:19,649 log 68155 8360664832 Internal Server Error: /api/export/departments/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend/ems/views.py", line 219, in department_export
    department.manager_ar or '',
AttributeError: 'Department' object has no attribute 'manager_ar'
INFO 2025-07-16 06:27:39,813 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:09,813 basehttp 68125 6162182144 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:22,238 autoreload 68124 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:22,240 autoreload 68125 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:22,667 autoreload 68190 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:22,769 autoreload 68189 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:37,481 autoreload 68190 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:37,484 autoreload 68189 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:37,903 autoreload 68214 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:37,953 autoreload 68213 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:39,831 basehttp 68214 6159609856 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:28:53,639 autoreload 68213 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:53,778 autoreload 68214 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:28:54,079 autoreload 68231 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:28:54,116 autoreload 68232 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:29:09,824 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,112 basehttp 68232 6191001600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:29:15,119 basehttp 68232 6191001600 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:29:15,156 basehttp 68232 6207827968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,158 basehttp 68232 6191001600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:29:15,160 basehttp 68232 6224654336 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:29:15,165 basehttp 68232 6191001600 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:29:15,460 basehttp 68232 6191001600 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:15,469 basehttp 68232 6224654336 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:15,523 basehttp 68232 6224654336 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:29:45,178 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:30:15,180 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:30:45,166 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:31:15,159 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:31:45,177 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:32:15,477 basehttp 68232 6191001600 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:32:27,414 autoreload 68232 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:27,777 autoreload 68231 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:27,894 autoreload 68421 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:28,354 autoreload 68422 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:41,783 autoreload 68421 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:41,952 autoreload 68422 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:32:42,197 autoreload 68441 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:42,367 autoreload 68442 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:32:45,188 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:15,173 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:45,164 basehttp 68441 6168555520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:33:55,347 autoreload 68441 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:33:55,347 autoreload 68442 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:33:55,798 autoreload 68544 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:33:55,915 autoreload 68543 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:34:15,192 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:34:45,174 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:15,171 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:45,173 basehttp 68544 6160248832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:35:50,164 autoreload 68544 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/middleware.py changed, reloading.
INFO 2025-07-16 06:35:50,568 autoreload 68673 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:11,538 autoreload 68673 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:11,663 autoreload 68543 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:11,930 autoreload 68683 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:12,129 autoreload 68684 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:15,185 basehttp 68683 6166474752 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:36:44,227 autoreload 68684 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:44,229 autoreload 68683 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 06:36:44,652 autoreload 68714 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:44,716 autoreload 68713 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:36:45,178 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:37:15,166 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:37:45,171 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search='; DROP TABLE employees; --
WARNING 2025-07-16 06:38:02,026 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=1' UNION SELECT * FROM auth_user --
WARNING 2025-07-16 06:38:02,026 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,026 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=admin'; DELETE FROM employees WHERE '1'='1
WARNING 2025-07-16 06:38:02,027 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=<script>alert('XSS')</script>
WARNING 2025-07-16 06:38:02,029 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=javascript:alert('XSS')
WARNING 2025-07-16 06:38:02,029 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,029 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=<img src=x onerror=alert('XSS')>
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,030 middleware 68786 8360664832 Suspicious query parameter from 127.0.0.1: search=eval('alert(1)')
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Bad Request: /api/employees/
WARNING 2025-07-16 06:38:02,030 log 68786 8360664832 Unauthorized: /api/employees/
WARNING 2025-07-16 06:38:02,031 log 68786 8360664832 Unauthorized: /api/departments/
WARNING 2025-07-16 06:38:02,031 log 68786 8360664832 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 06:38:02,032 log 68786 8360664832 Unauthorized: /api/user-profile/
INFO 2025-07-16 06:38:02,032 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,033 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,033 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,138 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,139 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,139 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,242 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,243 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,244 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,350 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,351 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,351 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,457 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,458 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,459 log 68786 8360664832 Internal Server Error: /api/auth/login/
INFO 2025-07-16 06:38:02,560 middleware 68786 8360664832 Auth attempt from 127.0.0.1 to /api/auth/login/
ERROR 2025-07-16 06:38:02,561 auth_views 68786 8360664832 Login error: Unsupported media type "multipart/form-data; boundary=BoUnDaRyStRiNg; charset=utf-8" in request.
ERROR 2025-07-16 06:38:02,561 log 68786 8360664832 Internal Server Error: /api/auth/login/
WARNING 2025-07-16 06:38:02,680 log 68786 8360664832 Unauthorized: /api/employees/
INFO 2025-07-16 06:38:15,162 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:38:45,169 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:15,169 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:20,498 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:20,885 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:21,114 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:21,260 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,081 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,314 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:22,890 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:24,071 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:24,938 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,022 basehttp 68714 6205026304 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,024 basehttp 68714 6221852672 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,058 basehttp 68714 6205026304 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:39:50,064 basehttp 68714 6205026304 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:39:50,094 basehttp 68714 6205026304 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,096 basehttp 68714 6221852672 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,099 basehttp 68714 6238679040 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,099 basehttp 68714 6255505408 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,103 basehttp 68714 6205026304 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:39:50,103 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:50,112 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:39:50,113 basehttp 68714 6205026304 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:39:50,397 basehttp 68714 6205026304 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,398 basehttp 68714 6221852672 "OPTIONS /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,412 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,445 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:39:50,480 basehttp 68714 6205026304 "OPTIONS /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 0
INFO 2025-07-16 06:39:50,497 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:40:20,111 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:40:50,105 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:20,113 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:34,803 basehttp 68714 6221852672 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:41:34,803 basehttp 68714 6205026304 "OPTIONS /api/dashboard-stats/ HTTP/1.1" 200 0
INFO 2025-07-16 06:41:34,842 basehttp 68714 6221852672 "GET /api/dashboard-stats/ HTTP/1.1" 200 218
INFO 2025-07-16 06:41:45,972 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:41:45,979 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:41:46,007 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:46,010 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:41:46,014 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:41:46,017 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:41:46,302 basehttp 68714 6221852672 "OPTIONS /api/departments/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 06:41:46,302 basehttp 68714 6205026304 "OPTIONS /api/departments/?page=1&page_size=20 HTTP/1.1" 200 0
INFO 2025-07-16 06:41:46,324 basehttp 68714 6221852672 "GET /api/departments/?page=1&page_size=20 HTTP/1.1" 200 3425
INFO 2025-07-16 06:41:46,347 basehttp 68714 6221852672 "GET /api/departments/?page=1&page_size=20 HTTP/1.1" 200 3425
INFO 2025-07-16 06:42:16,022 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:42:46,031 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:43:16,027 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 06:43:19,252 log 69158 8360664832 Unauthorized: /api/employees/
WARNING 2025-07-16 06:43:19,258 middleware 69158 8360664832 Suspicious query parameter from 127.0.0.1: search='; DROP TABLE employees; --
WARNING 2025-07-16 06:43:19,258 log 69158 8360664832 Bad Request: /api/employees/
INFO 2025-07-16 06:43:46,014 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:44:16,010 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:44:46,024 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,222 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:06,276 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:06,291 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,298 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:06,301 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:06,306 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:36,302 basehttp 68714 6221852672 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,440 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:50,446 basehttp 68714 6221852672 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 06:45:50,474 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,475 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:50,483 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:45:50,484 basehttp 68714 6221852672 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 06:45:50,805 basehttp 68714 6221852672 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:45:50,815 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:45:50,868 basehttp 68714 6205026304 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:46:20,487 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:46:50,480 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:47:20,481 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 06:47:37,882 log 69300 8360664832 Internal Server Error: /api/employees/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/__init__.py", line 2053, in get_prep_value
    return int(value)
ValueError: invalid literal for int() with base 10: 'Customer Service'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "/Users/<USER>/Desktop/EMS/backend/ems/views.py", line 44, in get_queryset
    queryset = queryset.filter(department=department)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1436, in filter
    return self._filter_or_exclude(False, args, kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1454, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/query.py", line 1461, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1545, in add_q
    clause, _ = self._add_q(q_object, self.used_aliases)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1576, in _add_q
    child_clause, needed_inner = self.build_filter(
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1491, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/sql/query.py", line 1318, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/lookups.py", line 27, in __init__
    self.rhs = self.get_prep_lookup()
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/related_lookups.py", line 166, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/models/fields/__init__.py", line 2055, in get_prep_value
    raise e.__class__(
ValueError: Field 'id' expected a number but got 'Customer Service'.
INFO 2025-07-16 06:47:50,486 basehttp 68714 6205026304 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:47:58,203 autoreload 68713 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:47:58,214 autoreload 68714 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:47:58,696 autoreload 69313 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:47:58,809 autoreload 69312 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:20,498 basehttp 69313 6129053696 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:48:26,729 autoreload 69312 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:26,769 autoreload 69313 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:27,136 autoreload 69333 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:27,172 autoreload 69332 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:50,515 basehttp 69333 6134099968 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:48:55,254 autoreload 69332 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:55,289 autoreload 69333 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/views.py changed, reloading.
INFO 2025-07-16 06:48:55,788 autoreload 69367 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:48:55,823 autoreload 69366 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 06:49:20,501 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:49:50,484 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:50:20,478 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:50:50,489 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:51:20,493 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:51:50,489 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:51:59,259 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 06:52:20,477 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:52:50,477 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:53:20,478 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:53:50,480 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:54:20,478 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:54:50,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:55:20,479 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:55:50,479 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:56:20,485 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:56:50,487 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:57:20,480 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:57:50,485 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:58:20,485 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:58:50,484 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:59:20,485 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 06:59:50,487 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:00:20,492 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:00:50,501 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:01:20,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:01:50,506 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:02:18,981 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:02:20,487 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:02:36,874 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:02:37,079 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:02:50,494 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:03:20,496 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:03:50,497 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:03:58,590 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:03:58,832 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:04:20,493 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:04:50,504 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 07:05:20,491 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:05:20,493 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:05:50,487 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:05:50,488 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:06:20,488 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:06:20,489 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:06:50,487 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:06:50,488 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:07:20,491 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:07:20,492 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:07:50,490 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:07:50,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:08:20,491 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:08:20,493 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:08:50,489 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:08:50,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:09:20,489 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:09:20,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:09:50,497 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:09:50,498 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:10:20,487 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:10:20,487 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:10:50,490 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:10:50,492 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:11:20,490 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:11:20,491 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:11:50,495 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:11:50,496 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:12:20,504 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:12:20,505 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:12:47,470 log 69367 6137196544 Unauthorized: /api/employees/
WARNING 2025-07-16 07:12:47,471 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 401 172
WARNING 2025-07-16 07:12:50,488 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:12:50,488 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:13:20,489 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:13:20,490 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:13:50,491 log 69367 6137196544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 07:13:50,492 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:14:11,579 log 69367 6137196544 Unauthorized: /api/auth/user/
WARNING 2025-07-16 07:14:11,580 basehttp 69367 6137196544 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:14:11,591 log 69367 6137196544 Unauthorized: /api/auth/user/
WARNING 2025-07-16 07:14:11,592 basehttp 69367 6137196544 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 07:14:11,607 basehttp 69367 6137196544 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
INFO 2025-07-16 07:14:11,608 middleware 69367 6154022912 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 07:14:11,609 basehttp 69367 6137196544 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
INFO 2025-07-16 07:14:11,609 middleware 69367 6137196544 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 07:14:11,620 auth_views 69367 6154022912 Token refresh error: type object 'OutstandingToken' has no attribute 'objects'
ERROR 2025-07-16 07:14:11,620 auth_views 69367 6137196544 Token refresh error: type object 'OutstandingToken' has no attribute 'objects'
ERROR 2025-07-16 07:14:11,620 log 69367 6154022912 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 07:14:11,620 log 69367 6137196544 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 07:14:11,620 basehttp 69367 6154022912 "POST /api/auth/refresh/ HTTP/1.1" 500 52
ERROR 2025-07-16 07:14:11,621 basehttp 69367 6137196544 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 07:14:11,622 basehttp 69367 6137196544 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 07:14:11,624 log 69367 6137196544 Unauthorized: /api/auth/logout/
INFO 2025-07-16 07:14:11,624 basehttp 69367 6154022912 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 07:14:11,624 basehttp 69367 6170849280 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 07:14:11,624 basehttp 69367 6137196544 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:14:11,628 log 69367 6170849280 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 07:14:11,629 basehttp 69367 6170849280 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 07:14:11,629 log 69367 6154022912 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 07:14:11,630 basehttp 69367 6154022912 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 07:14:11,631 basehttp 69367 6187675648 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 07:14:11,632 log 69367 6187675648 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 07:14:11,632 basehttp 69367 6187675648 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 07:20:45,474 basehttp 69367 6137196544 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 07:20:45,478 middleware 69367 6137196544 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 07:20:45,696 basehttp 69367 6137196544 "POST /api/auth/login/ HTTP/1.1" 200 959
INFO 2025-07-16 07:20:45,729 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:20:45,731 basehttp 69367 6137196544 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:20:45,735 basehttp 69367 6154022912 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:20:45,737 basehttp 69367 6170849280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:20:46,124 basehttp 69367 6170849280 "GET /api/dashboard-stats/ HTTP/1.1" 200 240
INFO 2025-07-16 07:20:56,842 basehttp 69367 6187675648 "GET /api/employees/ HTTP/1.1" 200 9345
INFO 2025-07-16 07:20:56,851 basehttp 69367 6204502016 "GET /api/employees/?search=john HTTP/1.1" 200 1308
INFO 2025-07-16 07:20:56,858 basehttp 69367 6221328384 "GET /api/employees/?search=admin HTTP/1.1" 200 697
INFO 2025-07-16 07:20:56,867 basehttp 69367 6238154752 "GET /api/employees/?search=ahmed HTTP/1.1" 200 710
INFO 2025-07-16 07:21:15,741 basehttp 69367 6170849280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:21:45,744 basehttp 69367 6170849280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:21:51,115 basehttp 69367 6170849280 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:21:51,125 basehttp 69367 6170849280 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:21:51,156 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:21:51,160 basehttp 69367 6170849280 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:21:51,165 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:21:51,168 basehttp 69367 6170849280 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:22:21,171 basehttp 69367 6170849280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:22:51,175 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:23:21,173 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:23:51,165 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:24:21,163 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:24:51,163 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:25:21,163 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:25:51,163 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:26:21,167 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:26:51,165 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:26:51,699 basehttp 69367 6137196544 "GET /api/dashboard-stats/ HTTP/1.1" 200 240
INFO 2025-07-16 07:27:21,162 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:27:51,164 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:28:21,169 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:28:31,607 basehttp 69367 6137196544 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:28:31,616 basehttp 69367 6137196544 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:28:31,655 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:28:31,659 basehttp 69367 6137196544 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:28:31,666 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:28:31,668 basehttp 69367 6137196544 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:28:46,298 basehttp 69367 6137196544 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:28:46,308 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=20&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:28:46,373 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:29:01,666 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:29:11,742 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:29:12,040 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortBy=undefined&sortOrder=asc&filters=%5Bobject+Object%5D HTTP/1.1" 200 9345
INFO 2025-07-16 07:29:31,661 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:30:01,666 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:30:31,662 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:31:01,676 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:31:14,498 basehttp 69367 6154022912 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:31:14,503 basehttp 69367 6154022912 "GET /api/auth/user/ HTTP/1.1" 200 468
INFO 2025-07-16 07:31:14,533 basehttp 69367 6137196544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:31:14,536 basehttp 69367 6154022912 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:31:14,541 basehttp 69367 6170849280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:31:14,543 basehttp 69367 6154022912 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 1976
INFO 2025-07-16 07:31:14,832 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=20&sortOrder=asc HTTP/1.1" 200 0
INFO 2025-07-16 07:31:14,833 basehttp 69367 6170849280 "OPTIONS /api/employees/?page=1&pageSize=20&sortOrder=asc HTTP/1.1" 200 0
INFO 2025-07-16 07:31:14,847 basehttp 69367 6170849280 "GET /api/employees/?page=1&pageSize=20&sortOrder=asc HTTP/1.1" 200 9345
INFO 2025-07-16 07:31:14,859 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=20&sortOrder=asc HTTP/1.1" 200 9345
INFO 2025-07-16 07:31:14,916 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=15&sortOrder=asc HTTP/1.1" 200 0
INFO 2025-07-16 07:31:14,927 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortOrder=asc HTTP/1.1" 200 9345
INFO 2025-07-16 07:31:44,543 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:32:14,563 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:32:44,524 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:33:14,518 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:33:44,533 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:34:14,533 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:34:44,525 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:34:46,998 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=15&sortOrder=asc&search=john HTTP/1.1" 200 0
INFO 2025-07-16 07:34:47,034 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortOrder=asc&search=john HTTP/1.1" 200 1308
INFO 2025-07-16 07:34:47,050 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=2&sortOrder=asc&search=john HTTP/1.1" 200 0
INFO 2025-07-16 07:34:47,058 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=2&sortOrder=asc&search=john HTTP/1.1" 200 1308
INFO 2025-07-16 07:34:47,364 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=2&sortOrder=asc&search=john HTTP/1.1" 200 1308
INFO 2025-07-16 07:35:14,513 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:35:44,522 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:36:14,519 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:36:44,521 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:36:53,079 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=2&sortOrder=asc&search=ahmed HTTP/1.1" 200 0
INFO 2025-07-16 07:36:53,110 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=2&sortOrder=asc&search=ahmed HTTP/1.1" 200 710
INFO 2025-07-16 07:36:53,125 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=1&sortOrder=asc&search=ahmed HTTP/1.1" 200 0
INFO 2025-07-16 07:36:53,132 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=1&sortOrder=asc&search=ahmed HTTP/1.1" 200 710
INFO 2025-07-16 07:36:53,440 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=1&sortOrder=asc&search=ahmed HTTP/1.1" 200 710
INFO 2025-07-16 07:37:14,535 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:37:33,283 basehttp 69367 6154022912 "OPTIONS /api/employees/?page=1&pageSize=1&sortOrder=asc HTTP/1.1" 200 0
INFO 2025-07-16 07:37:33,300 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=1&sortOrder=asc HTTP/1.1" 200 9345
INFO 2025-07-16 07:37:33,363 basehttp 69367 6154022912 "GET /api/employees/?page=1&pageSize=15&sortOrder=asc HTTP/1.1" 200 9345
INFO 2025-07-16 07:37:44,524 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 07:38:14,527 basehttp 69367 6154022912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
ERROR 2025-07-16 08:07:49,441 services 72653 8360664832 Failed to send email for notification 2a0721ee-5a68-4039-81f8-e08e86a6a046: [Errno 61] Connection refused
INFO 2025-07-16 08:07:49,451 services 72653 8360664832 Push notification marked as sent for notification 2a0721ee-5a68-4039-81f8-e08e86a6a046
INFO 2025-07-16 08:09:24,613 autoreload 72778 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 08:09:44,552 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:10:14,506 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:10:44,504 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 08:10:57,673 log 72778 6179663872 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 08:10:57,673 basehttp 72778 6179663872 "GET /api/dashboard-stats/ HTTP/1.1" 401 58
INFO 2025-07-16 08:11:14,504 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:11:44,504 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:12:14,498 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:12:44,506 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:13:14,501 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:13:44,502 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:14:14,504 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:14:44,508 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:15:14,518 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:15:44,502 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:16:14,514 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:16:44,505 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:17:14,516 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:17:44,498 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:18:14,499 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:18:24,404 basehttp 72778 6162837504 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 08:18:24,405 basehttp 72778 6179663872 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
WARNING 2025-07-16 08:18:24,411 log 72778 6162837504 Unauthorized: /api/auth/user/
WARNING 2025-07-16 08:18:24,412 basehttp 72778 6162837504 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:18:24,415 log 72778 6179663872 Unauthorized: /api/auth/user/
WARNING 2025-07-16 08:18:24,416 basehttp 72778 6179663872 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 08:18:24,418 basehttp 72778 6179663872 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
INFO 2025-07-16 08:18:24,418 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 08:18:24,422 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 08:18:24,427 auth_views 72778 6179663872 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 08:18:24,428 auth_views 72778 6162837504 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 08:18:24,428 log 72778 6179663872 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 08:18:24,428 log 72778 6162837504 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 08:18:24,428 basehttp 72778 6162837504 "POST /api/auth/refresh/ HTTP/1.1" 500 52
ERROR 2025-07-16 08:18:24,428 basehttp 72778 6179663872 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 08:18:24,437 basehttp 72778 6179663872 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 08:18:24,439 log 72778 6179663872 Unauthorized: /api/auth/logout/
INFO 2025-07-16 08:18:24,439 basehttp 72778 6162837504 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 08:18:24,439 basehttp 72778 6213316608 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 08:18:24,440 basehttp 72778 6196490240 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 08:18:24,444 log 72778 6162837504 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 08:18:24,444 basehttp 72778 6179663872 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:18:24,445 log 72778 6213316608 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 08:18:24,446 log 72778 6196490240 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 08:18:24,446 basehttp 72778 6162837504 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:18:24,447 basehttp 72778 6196490240 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:18:24,447 basehttp 72778 6213316608 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 08:18:32,576 basehttp 72778 6162837504 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-16 08:18:32,600 basehttp 72778 6162837504 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
WARNING 2025-07-16 08:18:32,630 log 72778 6162837504 Not Found: /static/admin/css/base.css
WARNING 2025-07-16 08:18:32,630 basehttp 72778 6162837504 "GET /static/admin/css/base.css HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,635 log 72778 6162837504 Not Found: /static/admin/css/dark_mode.css
WARNING 2025-07-16 08:18:32,636 log 72778 6179663872 Not Found: /static/admin/css/nav_sidebar.css
WARNING 2025-07-16 08:18:32,637 log 72778 6196490240 Not Found: /static/admin/css/login.css
WARNING 2025-07-16 08:18:32,637 basehttp 72778 6179663872 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,637 basehttp 72778 6162837504 "GET /static/admin/css/dark_mode.css HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,638 log 72778 6213316608 Not Found: /static/admin/css/responsive.css
WARNING 2025-07-16 08:18:32,638 basehttp 72778 6196490240 "GET /static/admin/css/login.css HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,639 log 72778 6230142976 Not Found: /static/admin/js/theme.js
INFO 2025-07-16 08:18:32,639 basehttp 72778 6196490240 - Broken pipe from ('127.0.0.1', 64577)
WARNING 2025-07-16 08:18:32,639 basehttp 72778 6213316608 "GET /static/admin/css/responsive.css HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,640 log 72778 6246969344 Not Found: /static/admin/js/nav_sidebar.js
WARNING 2025-07-16 08:18:32,640 basehttp 72778 6230142976 "GET /static/admin/js/theme.js HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,641 basehttp 72778 6246969344 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 404 179
WARNING 2025-07-16 08:18:32,663 log 72778 6246969344 Not Found: /favicon.ico
WARNING 2025-07-16 08:18:32,663 basehttp 72778 6246969344 "GET /favicon.ico HTTP/1.1" 404 179
INFO 2025-07-16 08:18:44,520 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:19:14,518 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:19:44,501 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:19:47,065 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 08:19:47,280 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 08:20:14,517 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 08:20:44,509 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 08:21:14,501 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:21:14,502 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:21:44,504 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:21:44,505 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:22:14,497 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:22:14,497 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:22:44,494 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:22:44,494 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:23:14,495 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:23:14,497 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:23:44,503 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:23:44,505 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:24:14,486 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:24:14,487 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:24:44,481 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:24:44,482 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:25:14,474 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:25:14,475 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:25:44,472 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:25:44,473 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:26:14,481 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:26:14,483 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:26:44,472 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:26:44,474 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:27:14,471 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:27:14,473 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:27:44,476 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:27:44,477 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:28:14,471 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:28:14,472 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:28:44,468 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:28:44,469 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:29:14,471 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:29:14,472 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:29:44,470 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:29:44,470 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:30:14,470 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:30:14,471 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:30:44,472 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:30:44,472 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:31:14,469 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:31:14,470 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:31:44,469 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:31:44,470 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:32:14,465 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:32:14,465 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:32:44,468 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:32:44,469 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:33:14,469 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:33:14,470 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:33:44,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:33:44,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:34:14,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:34:14,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:34:44,475 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:34:44,476 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:35:14,468 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:35:14,470 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:35:44,475 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:35:44,476 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:36:14,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:36:14,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:36:44,473 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:36:44,475 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:37:14,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:37:14,469 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:37:44,466 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:37:44,467 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:38:14,466 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:38:14,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:38:44,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:38:44,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:39:14,467 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:39:14,468 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:39:44,441 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:39:44,442 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 08:40:14,433 basehttp 72778 6162837504 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
WARNING 2025-07-16 08:40:14,437 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:40:14,438 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:40:44,434 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:40:44,436 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:41:14,433 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:41:14,435 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:41:44,432 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:41:44,436 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:42:14,440 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:42:14,442 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:42:44,433 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:42:44,435 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:43:14,439 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:43:14,441 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:43:44,429 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:43:44,430 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:44:14,428 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:44:14,429 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:44:44,431 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:44:44,433 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:45:14,436 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:45:14,437 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:45:44,433 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:45:44,434 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:46:14,428 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:46:14,432 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:46:44,438 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:46:44,439 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:47:14,430 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:47:14,432 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:47:44,428 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:47:44,429 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:48:14,431 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:48:14,432 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:48:44,432 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:48:44,432 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:49:14,429 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:49:14,430 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:49:44,430 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:49:44,431 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:50:14,430 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:50:14,431 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:50:44,430 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:50:44,430 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:51:14,430 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:51:14,431 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:51:44,431 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:51:44,433 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:52:14,427 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:52:14,428 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:52:44,427 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:52:44,429 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:53:14,426 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:53:14,427 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:53:44,439 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:53:44,440 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:54:14,426 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:54:14,428 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:54:44,409 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:54:44,410 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:55:14,403 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:55:14,405 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
ERROR 2025-07-16 08:55:40,056 log 72778 6162837504 Internal Server Error: /api/dashboard-stats/
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such table: cache_table

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/decorators.py", line 126, in _wrapper_view
    result = middleware.process_request(request)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/middleware/cache.py", line 158, in process_request
    cache_key = get_cache_key(request, self.key_prefix, "GET", cache=self.cache)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/utils/cache.py", line 391, in get_cache_key
    headerlist = cache.get(cache_key)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/cache/backends/db.py", line 52, in get
    return self.get_many([key], version).get(key, default)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/core/cache/backends/db.py", line 68, in get_many
    cursor.execute(
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/Users/<USER>/Desktop/EMS/backend_env/lib/python3.9/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such table: cache_table
ERROR 2025-07-16 08:55:40,072 basehttp 72778 6162837504 "GET /api/dashboard-stats/ HTTP/1.1" 500 145
WARNING 2025-07-16 08:55:44,404 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:55:44,404 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:56:14,402 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:56:14,403 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:56:44,403 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:56:44,405 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:57:14,403 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:57:14,404 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 08:57:27,789 basehttp 72778 6162837504 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
WARNING 2025-07-16 08:57:44,401 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:57:44,402 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:58:14,397 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:58:14,398 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:58:44,396 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:58:44,397 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 08:58:59,781 basehttp 72778 6162837504 "GET /api/employees/ HTTP/1.1" 200 52
WARNING 2025-07-16 08:59:14,397 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:59:14,398 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 08:59:44,395 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 08:59:44,396 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:00:14,408 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:00:14,408 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:00:44,396 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:00:44,398 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:01:14,396 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:01:14,398 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:01:44,454 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:01:44,455 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:02:14,431 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:02:14,437 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:02:44,407 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:02:44,408 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 09:03:01,153 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:01,401 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:01,424 basehttp 72778 6162837504 "GET /api/auth/user/ HTTP/1.1" 200 375
INFO 2025-07-16 09:03:01,452 basehttp 72778 6162837504 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:01,470 basehttp 72778 6162837504 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:03:01,476 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:01,764 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:01,775 basehttp 72778 6162837504 "GET /api/employees/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:03:01,790 log 72778 6162837504 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 09:03:01,790 basehttp 72778 6162837504 "GET /api/dashboard-stats/ HTTP/1.1" 401 58
WARNING 2025-07-16 09:03:01,798 log 72778 6162837504 Not Found: /api/nonexistent-endpoint/
WARNING 2025-07-16 09:03:01,799 basehttp 72778 6162837504 "GET /api/nonexistent-endpoint/ HTTP/1.1" 404 179
INFO 2025-07-16 09:03:01,806 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:02,060 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:02,723 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:02,948 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:02,966 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:03,187 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:03,191 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:03,192 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 09:03:03,194 auth_views 72778 6162837504 Token refresh error: type object 'OutstandingToken' has no attribute 'objects'
ERROR 2025-07-16 09:03:03,195 log 72778 6162837504 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 09:03:03,195 basehttp 72778 6162837504 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 09:03:03,419 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:03,442 basehttp 72778 6179663872 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:03:03,820 middleware 72778 6162837504 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:03:04,091 basehttp 72778 6162837504 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:03:04,141 basehttp 72778 6162837504 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,153 basehttp 72778 6230142976 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,155 basehttp 72778 6331101184 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,156 basehttp 72778 6263795712 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,157 basehttp 72778 6314274816 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,157 basehttp 72778 6280622080 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,157 basehttp 72778 6297448448 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,157 basehttp 72778 6246969344 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,158 basehttp 72778 6196490240 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:03:04,159 basehttp 72778 6213316608 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
WARNING 2025-07-16 09:03:14,419 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:03:14,421 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:03:44,397 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:03:44,399 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:04:14,391 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:04:14,391 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 09:04:39,020 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:39,022 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:39,028 middleware 72778 6213316608 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:39,040 middleware 72778 6230142976 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:39,241 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:39,260 basehttp 72778 6196490240 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:39,273 basehttp 72778 6179663872 "GET /api/auth/user/ HTTP/1.1" 200 375
INFO 2025-07-16 09:04:39,273 basehttp 72778 6230142976 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:39,275 basehttp 72778 6213316608 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:39,305 basehttp 72778 6230142976 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:04:39,306 basehttp 72778 6179663872 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:39,310 basehttp 72778 6213316608 "GET /api/employees/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:04:39,326 log 72778 6213316608 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 09:04:39,327 basehttp 72778 6213316608 "GET /api/dashboard-stats/ HTTP/1.1" 401 58
INFO 2025-07-16 09:04:39,332 basehttp 72778 6179663872 "GET /api/departments/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:04:39,341 log 72778 6213316608 Not Found: /api/nonexistent-endpoint/
WARNING 2025-07-16 09:04:39,341 basehttp 72778 6213316608 "GET /api/nonexistent-endpoint/ HTTP/1.1" 404 179
INFO 2025-07-16 09:04:40,246 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:40,701 basehttp 72778 6196490240 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:40,728 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:41,048 basehttp 72778 6196490240 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:41,053 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 09:04:41,060 auth_views 72778 6196490240 Token refresh error: type object 'OutstandingToken' has no attribute 'objects'
ERROR 2025-07-16 09:04:41,060 log 72778 6196490240 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 09:04:41,060 basehttp 72778 6196490240 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 09:04:41,644 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:04:41,872 basehttp 72778 6196490240 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:04:41,917 basehttp 72778 6196490240 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,925 basehttp 72778 6347927552 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,925 basehttp 72778 6280622080 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,926 basehttp 72778 6297448448 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,926 basehttp 72778 6381580288 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,926 basehttp 72778 6364753920 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,927 basehttp 72778 6263795712 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,927 basehttp 72778 6246969344 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,928 basehttp 72778 6331101184 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:04:41,929 basehttp 72778 6314274816 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
WARNING 2025-07-16 09:04:44,395 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:04:44,395 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:05:14,400 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:05:14,401 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:05:44,426 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:05:44,431 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 09:05:49,066 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:49,349 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:49,376 basehttp 72778 6179663872 "GET /api/auth/user/ HTTP/1.1" 200 375
INFO 2025-07-16 09:05:49,398 basehttp 72778 6179663872 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:49,418 basehttp 72778 6179663872 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:05:49,429 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:49,675 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:49,690 basehttp 72778 6179663872 "GET /api/employees/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:05:49,715 log 72778 6179663872 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 09:05:49,715 basehttp 72778 6179663872 "GET /api/dashboard-stats/ HTTP/1.1" 401 58
WARNING 2025-07-16 09:05:49,726 log 72778 6179663872 Not Found: /api/nonexistent-endpoint/
WARNING 2025-07-16 09:05:49,727 basehttp 72778 6179663872 "GET /api/nonexistent-endpoint/ HTTP/1.1" 404 179
INFO 2025-07-16 09:05:49,734 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:49,978 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:50,529 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:50,767 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:50,784 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:51,025 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:51,028 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 09:05:51,031 auth_views 72778 6179663872 Token refresh error: type object 'OutstandingToken' has no attribute 'objects'
ERROR 2025-07-16 09:05:51,032 log 72778 6179663872 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 09:05:51,032 basehttp 72778 6179663872 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 09:05:51,568 middleware 72778 6179663872 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:51,808 basehttp 72778 6179663872 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:51,840 basehttp 72778 6179663872 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,858 basehttp 72778 6230142976 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,860 basehttp 72778 6213316608 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,863 basehttp 72778 6246969344 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,864 basehttp 72778 6314274816 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,864 basehttp 72778 6331101184 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,865 basehttp 72778 6297448448 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,865 basehttp 72778 6280622080 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,866 basehttp 72778 6263795712 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,866 basehttp 72778 6196490240 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:05:51,884 middleware 72778 6196490240 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:05:52,129 basehttp 72778 6196490240 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:05:52,140 basehttp 72778 6196490240 "GET /api/departments/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:06:14,441 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:06:14,442 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:06:44,395 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:06:44,396 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:07:14,388 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:07:14,389 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:07:44,388 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:07:44,388 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:08:14,388 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:08:14,388 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:08:44,388 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:08:44,389 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:09:14,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:09:14,386 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:09:44,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:09:44,386 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:10:14,387 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:10:14,388 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:10:44,384 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:10:44,384 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:11:14,385 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:11:14,385 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:11:44,390 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:11:44,391 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:12:14,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:12:14,389 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:12:44,382 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:12:44,383 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:13:14,387 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:13:14,388 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:13:44,383 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:13:44,384 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:14:14,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:14:14,387 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:14:44,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:14:44,387 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:15:14,380 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:15:14,381 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:15:44,386 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:15:44,393 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:16:14,382 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:16:14,383 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:16:44,384 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:16:44,385 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:17:14,382 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:17:14,384 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:17:44,380 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:17:44,380 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 09:18:14,380 log 72778 6162837504 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 09:18:14,380 basehttp 72778 6162837504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 09:18:39,412 basehttp 72778 6162837504 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 09:18:39,417 log 72778 6162837504 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 09:18:39,418 basehttp 72778 6162837504 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 09:38:40,099 autoreload 72778 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 09:38:40,520 autoreload 76879 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 09:41:45,670 autoreload 76879 8360664832 /Users/<USER>/Desktop/EMS/backend/ems/auth_views.py changed, reloading.
INFO 2025-07-16 09:41:46,074 autoreload 76953 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 09:44:10,957 middleware 76953 6193360896 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 09:44:11,002 auth_views 76953 6193360896 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 09:44:11,003 log 76953 6193360896 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 09:44:11,003 basehttp 76953 6193360896 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 09:46:53,700 autoreload 76953 8360664832 /Users/<USER>/Desktop/EMS/backend/backend/settings.py changed, reloading.
INFO 2025-07-16 09:46:54,136 autoreload 77146 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 09:48:35,781 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:48:36,021 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:48:46,471 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 09:48:46,481 basehttp 77146 6197948416 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 09:53:20,387 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:20,634 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:20,649 basehttp 77146 6197948416 "GET /api/auth/user/ HTTP/1.1" 200 375
INFO 2025-07-16 09:53:20,699 basehttp 77146 6197948416 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:20,713 basehttp 77146 6197948416 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:53:20,725 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:21,087 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:21,109 basehttp 77146 6197948416 "GET /api/employees/ HTTP/1.1" 200 52
WARNING 2025-07-16 09:53:21,133 log 77146 6197948416 Unauthorized: /api/dashboard-stats/
WARNING 2025-07-16 09:53:21,138 basehttp 77146 6197948416 "GET /api/dashboard-stats/ HTTP/1.1" 401 58
WARNING 2025-07-16 09:53:21,162 log 77146 6197948416 Not Found: /api/nonexistent-endpoint/
WARNING 2025-07-16 09:53:21,163 basehttp 77146 6197948416 "GET /api/nonexistent-endpoint/ HTTP/1.1" 404 179
INFO 2025-07-16 09:53:21,171 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:21,392 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:21,922 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:22,137 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:22,152 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:22,371 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:22,378 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 09:53:22,381 basehttp 77146 6197948416 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 09:53:22,406 basehttp 77146 6197948416 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,465 basehttp 77146 6265253888 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,466 basehttp 77146 6298906624 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,467 basehttp 77146 6315732992 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,467 basehttp 77146 6349385728 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,469 basehttp 77146 6332559360 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,469 basehttp 77146 6214774784 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,469 basehttp 77146 6231601152 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,470 basehttp 77146 6282080256 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,472 basehttp 77146 6248427520 "GET /api/dashboard-stats/ HTTP/1.1" 200 238
INFO 2025-07-16 09:53:22,485 middleware 77146 6248427520 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:22,713 basehttp 77146 6248427520 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:22,727 basehttp 77146 6248427520 "GET /api/departments/ HTTP/1.1" 200 52
INFO 2025-07-16 09:53:25,236 basehttp 77146 6366212096 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:25,237 middleware 77146 6366212096 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 09:53:25,497 middleware 77146 6366212096 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 09:53:25,498 log 77146 6366212096 Unauthorized: /api/auth/login/
WARNING 2025-07-16 09:53:25,498 basehttp 77146 6366212096 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 09:53:26,585 basehttp 77146 6366212096 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:26,586 middleware 77146 6366212096 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:26,850 basehttp 77146 6366212096 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:26,879 basehttp 77146 6366212096 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:26,887 basehttp 77146 6383038464 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:26,916 basehttp 77146 6399864832 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:26,924 basehttp 77146 6416691200 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:26,934 basehttp 77146 6383038464 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:26,934 basehttp 77146 6366212096 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:26,948 basehttp 77146 6416691200 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:26,948 basehttp 77146 6383038464 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:31,482 basehttp 77146 6433517568 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:31,483 middleware 77146 6433517568 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:31,848 basehttp 77146 6433517568 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:31,876 basehttp 77146 6433517568 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:31,878 basehttp 77146 13035925504 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:31,884 basehttp 77146 13052751872 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:31,885 basehttp 77146 13069578240 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:31,915 basehttp 77146 6433517568 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:31,928 basehttp 77146 13035925504 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:31,941 basehttp 77146 6433517568 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:31,944 basehttp 77146 13069578240 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:33,604 basehttp 77146 6366212096 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:33,607 middleware 77146 6366212096 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:33,882 basehttp 77146 6366212096 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:33,910 basehttp 77146 6366212096 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:33,915 basehttp 77146 6383038464 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:33,921 basehttp 77146 6399864832 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:33,923 basehttp 77146 6416691200 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:33,946 basehttp 77146 6383038464 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:33,947 basehttp 77146 6366212096 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:33,969 basehttp 77146 6416691200 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:33,975 basehttp 77146 6383038464 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:38,697 basehttp 77146 6433517568 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:38,720 middleware 77146 6433517568 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:53:39,182 basehttp 77146 6433517568 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 09:53:39,221 basehttp 77146 6383038464 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:39,222 basehttp 77146 6399864832 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:39,223 basehttp 77146 6366212096 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 09:53:39,248 basehttp 77146 6433517568 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 09:53:39,257 basehttp 77146 6399864832 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:39,272 basehttp 77146 6383038464 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:53:39,275 basehttp 77146 6433517568 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 09:53:39,289 basehttp 77146 6399864832 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 765
INFO 2025-07-16 09:54:48,907 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 09:54:49,118 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 866
INFO 2025-07-16 10:09:58,978 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:09:59,184 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 10:17:38,522 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 10:17:38,538 basehttp 77146 6197948416 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 10:20:12,716 basehttp 77146 6197948416 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 10:20:12,717 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:20:12,940 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 10:20:12,988 basehttp 77146 6197948416 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 10:20:12,990 basehttp 77146 6214774784 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 10:20:12,992 basehttp 77146 6231601152 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 10:20:12,998 basehttp 77146 6214774784 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:20:13,008 basehttp 77146 6197948416 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:20:13,013 basehttp 77146 6214774784 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:20:13,019 basehttp 77146 6197948416 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:20:38,540 basehttp 77146 6197948416 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
ERROR 2025-07-16 10:20:38,554 auth_views 77146 6197948416 Logout error: 'RefreshToken' object has no attribute 'blacklist'
ERROR 2025-07-16 10:20:38,554 log 77146 6197948416 Internal Server Error: /api/auth/logout/
ERROR 2025-07-16 10:20:38,554 basehttp 77146 6197948416 "POST /api/auth/logout/ HTTP/1.1" 500 45
INFO 2025-07-16 10:20:47,756 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 10:20:47,967 middleware 77146 6197948416 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 10:20:47,967 log 77146 6197948416 Unauthorized: /api/auth/login/
WARNING 2025-07-16 10:20:47,967 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 10:20:54,052 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 10:20:54,266 middleware 77146 6197948416 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 10:20:54,266 log 77146 6197948416 Unauthorized: /api/auth/login/
WARNING 2025-07-16 10:20:54,266 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 10:22:09,692 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 10:22:09,911 middleware 77146 6197948416 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 10:22:09,912 log 77146 6197948416 Unauthorized: /api/auth/login/
WARNING 2025-07-16 10:22:09,912 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 10:22:16,050 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 10:22:16,267 middleware 77146 6197948416 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 10:22:16,267 log 77146 6197948416 Unauthorized: /api/auth/login/
WARNING 2025-07-16 10:22:16,267 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 10:25:01,199 basehttp 77146 6248427520 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 10:25:01,201 middleware 77146 6248427520 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 10:25:01,415 middleware 77146 6248427520 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 10:25:01,416 log 77146 6248427520 Unauthorized: /api/auth/login/
WARNING 2025-07-16 10:25:01,416 basehttp 77146 6248427520 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 10:25:14,030 middleware 77146 6248427520 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:25:14,275 basehttp 77146 6248427520 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 10:25:14,317 basehttp 77146 6248427520 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 10:25:14,320 basehttp 77146 6265253888 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 10:25:14,323 basehttp 77146 6282080256 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 10:25:14,323 basehttp 77146 6248427520 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 10:25:14,327 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:25:14,330 basehttp 77146 6298906624 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:25:14,339 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:25:14,339 basehttp 77146 6248427520 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:25:44,336 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:26:14,839 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:26:44,827 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:27:04,749 basehttp 77146 6265253888 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 10:27:04,760 basehttp 77146 6248427520 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 10:27:04,784 basehttp 77146 6265253888 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:27:04,795 basehttp 77146 6265253888 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:27:04,832 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:27:04,832 basehttp 77146 6265253888 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:27:04,851 basehttp 77146 6248427520 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:27:04,852 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:27:19,637 basehttp 77146 6265253888 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:27:19,642 basehttp 77146 6265253888 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:27:19,669 basehttp 77146 6265253888 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:27:19,670 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:27:19,681 basehttp 77146 6265253888 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:27:19,681 basehttp 77146 6248427520 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:27:49,891 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:28:09,256 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:28:09,452 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 10:28:19,880 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:28:26,366 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 10:28:26,369 basehttp 77146 6197948416 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 10:28:49,876 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:29:19,889 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:29:49,897 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:30:19,885 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:31:40,899 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:32:40,901 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:33:40,896 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:34:40,927 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:35:40,908 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:36:25,486 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:36:34,858 basehttp 77146 6197948416 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:36:34,859 basehttp 77146 6248427520 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 766
INFO 2025-07-16 10:36:37,572 basehttp 77146 6248427520 "OPTIONS /api/notifications/notifications/2a0721ee-5a68-4039-81f8-e08e86a6a046/mark_read/ HTTP/1.1" 200 0
INFO 2025-07-16 10:36:37,597 basehttp 77146 6248427520 "POST /api/notifications/notifications/2a0721ee-5a68-4039-81f8-e08e86a6a046/mark_read/ HTTP/1.1" 200 27
INFO 2025-07-16 10:36:37,599 basehttp 77146 6214774784 - Broken pipe from ('127.0.0.1', 55766)
INFO 2025-07-16 10:36:37,602 basehttp 77146 6197948416 - Broken pipe from ('127.0.0.1', 55760)
INFO 2025-07-16 10:36:37,955 basehttp 77146 6248427520 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:36:37,963 basehttp 77146 6248427520 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 10:36:38,003 basehttp 77146 6197948416 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:36:38,003 basehttp 77146 6248427520 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 10:36:38,013 basehttp 77146 6197948416 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 10:36:38,013 basehttp 77146 6248427520 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 10:36:46,709 basehttp 77146 6248427520 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
ERROR 2025-07-16 10:36:46,726 auth_views 77146 6248427520 Logout error: 'RefreshToken' object has no attribute 'blacklist'
ERROR 2025-07-16 10:36:46,726 log 77146 6248427520 Internal Server Error: /api/auth/logout/
ERROR 2025-07-16 10:36:46,727 basehttp 77146 6248427520 "POST /api/auth/logout/ HTTP/1.1" 500 45
INFO 2025-07-16 10:40:16,412 middleware 77146 6214774784 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:40:16,612 basehttp 77146 6214774784 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 10:52:22,225 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 10:52:22,434 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 11:12:59,114 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 11:12:59,318 middleware 77146 6197948416 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 11:12:59,318 log 77146 6197948416 Unauthorized: /api/auth/login/
WARNING 2025-07-16 11:12:59,318 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 11:15:58,554 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 11:15:58,764 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 12:35:33,986 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 12:35:34,200 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 12:49:56,307 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 12:49:56,518 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 13:02:50,997 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 13:02:51,217 basehttp 77146 6197948416 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 14:25:19,120 basehttp 77146 6197948416 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 14:25:19,123 basehttp 77146 6214774784 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
WARNING 2025-07-16 14:25:19,173 log 77146 6197948416 Unauthorized: /api/auth/user/
WARNING 2025-07-16 14:25:19,174 basehttp 77146 6197948416 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 14:25:19,178 log 77146 6214774784 Unauthorized: /api/auth/user/
INFO 2025-07-16 14:25:19,179 basehttp 77146 6197948416 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
WARNING 2025-07-16 14:25:19,180 basehttp 77146 6214774784 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 14:25:19,181 middleware 77146 6197948416 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 14:25:19,181 middleware 77146 6214774784 Auth attempt from 127.0.0.1 to /api/auth/refresh/
ERROR 2025-07-16 14:25:19,185 auth_views 77146 6197948416 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 14:25:19,187 auth_views 77146 6214774784 Token refresh error: {'detail': ErrorDetail(string='Token is invalid', code='token_not_valid'), 'code': ErrorDetail(string='token_not_valid', code='token_not_valid')}
ERROR 2025-07-16 14:25:19,188 log 77146 6197948416 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 14:25:19,188 log 77146 6214774784 Internal Server Error: /api/auth/refresh/
ERROR 2025-07-16 14:25:19,188 basehttp 77146 6214774784 "POST /api/auth/refresh/ HTTP/1.1" 500 52
ERROR 2025-07-16 14:25:19,188 basehttp 77146 6197948416 "POST /api/auth/refresh/ HTTP/1.1" 500 52
INFO 2025-07-16 14:25:19,192 basehttp 77146 6197948416 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
INFO 2025-07-16 14:25:19,192 basehttp 77146 6214774784 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 14:25:19,193 log 77146 6197948416 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 14:25:19,194 log 77146 6214774784 Unauthorized: /api/auth/logout/
INFO 2025-07-16 14:25:19,194 basehttp 77146 6231601152 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 14:25:19,195 basehttp 77146 6197948416 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 14:25:19,199 log 77146 6231601152 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 14:25:19,199 basehttp 77146 6214774784 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 14:25:19,199 basehttp 77146 6248427520 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-07-16 14:25:19,200 basehttp 77146 6231601152 "POST /api/auth/logout/ HTTP/1.1" 401 172
WARNING 2025-07-16 14:25:19,200 log 77146 6248427520 Unauthorized: /api/auth/logout/
WARNING 2025-07-16 14:25:19,201 basehttp 77146 6248427520 "POST /api/auth/logout/ HTTP/1.1" 401 172
INFO 2025-07-16 14:37:11,607 autoreload 93355 8360664832 Watching for file changes with StatReloader
INFO 2025-07-16 14:38:43,054 basehttp 93355 6196588544 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-16 14:38:43,055 middleware 93355 6196588544 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-16 14:38:43,266 middleware 93355 6196588544 Failed login attempt from 127.0.0.1
WARNING 2025-07-16 14:38:43,267 log 93355 6196588544 Unauthorized: /api/auth/login/
WARNING 2025-07-16 14:38:43,267 basehttp 93355 6196588544 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-16 14:39:05,471 middleware 93355 6196588544 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-16 14:39:05,710 basehttp 93355 6196588544 "POST /api/auth/login/ HTTP/1.1" 200 1202
INFO 2025-07-16 14:39:05,741 basehttp 93355 6196588544 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 14:39:05,743 basehttp 93355 6213414912 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 14:39:05,747 basehttp 93355 6196588544 "OPTIONS /api/notifications/notifications/?limit=10 HTTP/1.1" 200 0
INFO 2025-07-16 14:39:05,747 basehttp 93355 6230241280 "OPTIONS /api/notifications/notifications/unread_count/ HTTP/1.1" 200 0
INFO 2025-07-16 14:39:05,759 basehttp 93355 6213414912 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:39:05,767 basehttp 93355 6247067648 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 14:39:05,771 basehttp 93355 6230241280 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:39:05,781 basehttp 93355 6247067648 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 14:39:35,783 basehttp 93355 6247067648 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:40:05,758 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:40:35,759 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:41:05,757 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:41:35,760 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:42:05,758 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:42:35,757 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:43:05,755 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:43:35,775 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:44:05,802 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:44:35,812 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:45:05,807 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:45:35,809 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:46:05,803 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:46:35,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:47:05,805 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:47:35,808 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:48:05,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:48:35,809 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:49:05,805 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:49:35,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:50:05,808 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:50:35,811 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:51:05,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:51:35,812 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:52:05,813 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:52:35,813 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:53:05,814 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:53:35,814 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:54:05,817 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:54:35,811 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:55:05,813 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:55:35,816 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:56:05,816 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:56:35,819 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:57:05,814 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:57:35,814 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:58:05,820 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:58:35,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:59:05,787 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 14:59:35,791 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:00:05,785 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:00:35,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:01:05,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:01:35,785 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:02:05,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:02:35,790 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:03:05,787 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:03:35,792 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:04:05,794 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:04:35,794 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:05:05,792 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:05:35,794 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:06:05,793 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:06:35,793 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:07:05,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:07:35,794 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:08:05,792 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:08:35,795 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:09:05,791 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:09:35,795 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:10:05,791 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:10:35,796 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:11:05,796 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:11:35,795 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:12:05,796 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:12:35,795 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:13:05,790 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:13:35,792 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:14:05,777 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:14:35,781 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:15:05,776 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:15:35,782 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:16:05,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:16:35,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:17:05,786 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:17:35,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:18:05,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:18:35,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:19:05,784 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:19:35,782 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:20:05,783 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:20:35,785 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:21:05,784 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:21:35,785 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:22:05,786 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:22:35,786 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:23:05,781 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:23:35,787 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:24:05,782 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:24:35,787 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:25:05,782 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:25:35,788 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:26:05,786 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:26:35,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:27:05,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:27:35,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:28:05,791 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:28:35,789 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:29:05,821 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:29:35,830 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:30:05,831 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:30:35,836 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:31:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:31:35,838 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:32:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:32:35,839 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:33:05,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:33:35,841 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:34:05,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:34:35,841 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:35:05,841 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:35:35,841 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:36:05,843 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:36:35,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:37:05,843 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:37:35,843 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:38:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:38:35,843 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 15:39:05,836 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:39:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:39:35,834 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:39:35,836 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:40:05,834 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:40:05,835 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:40:35,835 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:40:35,836 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:41:05,836 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:41:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:41:35,836 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:41:35,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:42:05,836 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:42:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:42:35,837 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:42:35,838 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:43:05,836 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:43:05,837 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:43:35,837 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:43:35,839 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:44:05,837 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:44:05,838 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:44:35,838 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:44:35,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:45:05,839 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:45:05,839 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:45:35,839 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:45:35,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:46:05,840 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:46:05,841 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:46:35,840 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:46:35,840 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:47:05,854 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:47:05,855 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:47:35,841 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:47:35,843 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:48:05,843 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:48:05,844 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:48:35,853 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 15:48:35,854 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-16 15:48:46,351 basehttp 93355 6196588544 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
INFO 2025-07-16 15:48:46,352 basehttp 93355 6213414912 "OPTIONS /api/auth/user/ HTTP/1.1" 200 0
WARNING 2025-07-16 15:48:46,376 log 93355 6196588544 Unauthorized: /api/auth/user/
WARNING 2025-07-16 15:48:46,377 basehttp 93355 6196588544 "GET /api/auth/user/ HTTP/1.1" 401 172
WARNING 2025-07-16 15:48:46,378 log 93355 6213414912 Unauthorized: /api/auth/user/
INFO 2025-07-16 15:48:46,379 basehttp 93355 6196588544 "OPTIONS /api/auth/refresh/ HTTP/1.1" 200 0
WARNING 2025-07-16 15:48:46,379 basehttp 93355 6213414912 "GET /api/auth/user/ HTTP/1.1" 401 172
INFO 2025-07-16 15:48:46,380 middleware 93355 6196588544 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 15:48:46,380 middleware 93355 6213414912 Auth attempt from 127.0.0.1 to /api/auth/refresh/
INFO 2025-07-16 15:48:46,392 basehttp 93355 6196588544 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 15:48:46,392 basehttp 93355 6213414912 "POST /api/auth/refresh/ HTTP/1.1" 200 241
INFO 2025-07-16 15:48:46,399 basehttp 93355 6213414912 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 15:48:46,403 basehttp 93355 6213414912 "GET /api/auth/user/ HTTP/1.1" 200 711
INFO 2025-07-16 15:48:46,430 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:48:46,434 basehttp 93355 6213414912 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 15:48:46,436 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 15:48:46,439 basehttp 93355 6213414912 "GET /api/notifications/notifications/?limit=10 HTTP/1.1" 200 790
INFO 2025-07-16 16:04:17,226 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
INFO 2025-07-16 16:34:49,698 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 200 18
WARNING 2025-07-16 16:49:15,826 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 16:49:15,827 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 17:10:12,695 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 17:10:12,697 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 17:25:38,595 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 17:25:38,595 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 17:29:00,577 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 17:29:00,578 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 17:34:59,387 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 17:34:59,388 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 17:55:40,500 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 17:55:40,501 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 18:11:11,282 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 18:11:11,282 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 18:19:55,242 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 18:19:55,243 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 18:26:08,856 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 18:26:08,857 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 18:33:34,978 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 18:33:34,979 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 18:49:06,567 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 18:49:06,568 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 19:12:06,197 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 19:12:06,198 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 19:27:37,321 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 19:27:37,322 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 19:43:08,032 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 19:43:08,033 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 19:57:52,294 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 19:57:52,295 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 20:13:23,907 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 20:13:23,908 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 20:28:55,600 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 20:28:55,601 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 20:44:26,809 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 20:44:26,810 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 21:14:05,977 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 21:14:05,978 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 21:29:37,694 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 21:29:37,696 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 21:45:08,415 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 21:45:08,416 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 21:59:48,095 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 21:59:48,096 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 22:15:18,938 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 22:15:18,940 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 22:30:50,644 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 22:30:50,646 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 22:46:21,427 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 22:46:21,428 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 23:00:57,155 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 23:00:57,156 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 23:16:32,117 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 23:16:32,118 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-16 23:47:04,410 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-16 23:47:04,411 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 00:01:43,418 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 00:01:43,419 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 00:17:15,007 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 00:17:15,007 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 00:32:45,700 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 00:32:45,702 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 00:48:17,407 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 00:48:17,408 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 01:02:53,381 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 01:02:53,382 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 01:18:24,083 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 01:18:24,085 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 01:48:55,933 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 01:48:55,934 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 02:03:43,197 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 02:03:43,198 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 02:19:13,825 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 02:19:13,826 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 02:34:45,472 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 02:34:45,474 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 02:50:17,202 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 02:50:17,203 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:04:42,284 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:04:42,285 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:20:14,004 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:20:14,005 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:35:45,071 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:35:45,071 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:39:38,252 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:39:38,253 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:40:08,248 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:40:08,249 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:40:38,250 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:40:38,251 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:41:08,257 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:41:08,258 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:41:38,272 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:41:38,273 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:42:08,290 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:42:08,292 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:42:38,293 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:42:38,294 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:43:08,289 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:43:08,290 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:43:38,296 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:43:38,297 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:44:05,447 log 93355 6196588544 Not Found: /
WARNING 2025-07-17 03:44:05,448 basehttp 93355 6196588544 "GET / HTTP/1.1" 404 179
WARNING 2025-07-17 03:44:08,292 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:44:08,292 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:44:38,299 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:44:38,299 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:45:08,297 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:45:08,298 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:45:38,297 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:45:38,298 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:46:08,300 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:46:08,300 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:46:38,295 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:46:38,296 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:47:08,301 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:47:08,302 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:47:38,300 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:47:38,300 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:48:08,303 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:48:08,303 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:48:38,301 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:48:38,301 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:49:08,300 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:49:08,300 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:49:38,301 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:49:38,302 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:50:08,297 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:50:08,298 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:50:38,301 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:50:38,301 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:51:08,295 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:51:08,297 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:51:38,302 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:51:38,303 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:52:08,302 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:52:08,304 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:52:38,297 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:52:38,298 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:53:08,303 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:53:08,304 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:53:38,303 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:53:38,304 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:54:08,295 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:54:08,296 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:54:38,310 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:54:38,311 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:55:08,305 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:55:08,306 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:55:38,304 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:55:38,305 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 03:55:57,833 autoreload 4562 8360664832 Watching for file changes with StatReloader
WARNING 2025-07-17 03:56:08,303 log 93355 6196588544 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:56:08,303 basehttp 93355 6196588544 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 03:56:21,615 autoreload 4663 8360664832 Watching for file changes with StatReloader
WARNING 2025-07-17 03:56:38,354 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:56:38,355 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:57:08,305 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:57:08,305 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:57:38,334 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:57:38,335 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:58:08,300 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:58:08,301 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:58:38,304 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:58:38,305 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:59:08,302 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:59:08,303 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 03:59:38,305 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 03:59:38,306 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 03:59:54,283 basehttp 4663 6222327808 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-17 03:59:54,284 middleware 4663 6222327808 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-17 03:59:54,543 middleware 4663 6222327808 Failed login attempt from 127.0.0.1
WARNING 2025-07-17 03:59:54,543 log 4663 6222327808 Unauthorized: /api/auth/login/
WARNING 2025-07-17 03:59:54,543 basehttp 4663 6222327808 "POST /api/auth/login/ HTTP/1.1" 401 33
WARNING 2025-07-17 04:00:08,325 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:00:08,326 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:00:38,302 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:00:38,303 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
ERROR 2025-07-17 04:00:43,950 services 4891 8360664832 Failed to send email for notification a522f5fa-eed0-498c-a7b8-3ef372f01e49: [Errno 61] Connection refused
INFO 2025-07-17 04:00:43,952 services 4891 8360664832 Push notification marked as sent for notification a522f5fa-eed0-498c-a7b8-3ef372f01e49
INFO 2025-07-17 04:00:58,173 middleware 4663 6222327808 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-17 04:00:58,381 middleware 4663 6222327808 Failed login attempt from 127.0.0.1
WARNING 2025-07-17 04:00:58,382 log 4663 6222327808 Unauthorized: /api/auth/login/
WARNING 2025-07-17 04:00:58,382 basehttp 4663 6222327808 "POST /api/auth/login/ HTTP/1.1" 401 33
WARNING 2025-07-17 04:01:08,330 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:01:08,330 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:01:38,305 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:01:38,307 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
ERROR 2025-07-17 04:01:51,930 services 4995 8360664832 Failed to send email for notification 087872da-139a-426b-82bf-720d45f0eb0d: [Errno 61] Connection refused
INFO 2025-07-17 04:01:51,932 services 4995 8360664832 Push notification marked as sent for notification 087872da-139a-426b-82bf-720d45f0eb0d
ERROR 2025-07-17 04:01:52,158 services 4995 8360664832 Failed to send email for notification 8a46c6a0-7800-4902-8ad2-c73407df5cce: [Errno 61] Connection refused
INFO 2025-07-17 04:01:52,159 services 4995 8360664832 Push notification marked as sent for notification 8a46c6a0-7800-4902-8ad2-c73407df5cce
ERROR 2025-07-17 04:01:52,364 services 4995 8360664832 Failed to send email for notification 5c96ad64-c261-4cdd-a95c-7088b5cd1553: [Errno 61] Connection refused
INFO 2025-07-17 04:01:52,365 services 4995 8360664832 Push notification marked as sent for notification 5c96ad64-c261-4cdd-a95c-7088b5cd1553
ERROR 2025-07-17 04:01:52,573 services 4995 8360664832 Failed to send email for notification 4cedd59a-14b4-4fa1-84bc-06d6105b09f9: [Errno 61] Connection refused
INFO 2025-07-17 04:01:52,573 services 4995 8360664832 Push notification marked as sent for notification 4cedd59a-14b4-4fa1-84bc-06d6105b09f9
ERROR 2025-07-17 04:01:52,891 services 4995 8360664832 Failed to send email for notification 4da307d0-4d20-4b56-a4ce-f7531c6b3f39: [Errno 61] Connection refused
INFO 2025-07-17 04:01:52,891 services 4995 8360664832 Push notification marked as sent for notification 4da307d0-4d20-4b56-a4ce-f7531c6b3f39
ERROR 2025-07-17 04:01:53,183 services 4995 8360664832 Failed to send email for notification 6af74e9a-c120-4d18-a58d-a97c58b78c79: [Errno 61] Connection refused
INFO 2025-07-17 04:01:53,184 services 4995 8360664832 Push notification marked as sent for notification 6af74e9a-c120-4d18-a58d-a97c58b78c79
ERROR 2025-07-17 04:01:53,422 services 4995 8360664832 Failed to send email for notification 8bfb80b9-2bb4-4964-a369-308fedd7a19e: [Errno 61] Connection refused
INFO 2025-07-17 04:01:53,422 services 4995 8360664832 Push notification marked as sent for notification 8bfb80b9-2bb4-4964-a369-308fedd7a19e
ERROR 2025-07-17 04:01:53,651 services 4995 8360664832 Failed to send email for notification 04490300-40b7-47a7-b0ff-a40b5a195fef: [Errno 61] Connection refused
INFO 2025-07-17 04:01:53,652 services 4995 8360664832 Push notification marked as sent for notification 04490300-40b7-47a7-b0ff-a40b5a195fef
ERROR 2025-07-17 04:01:53,872 services 4995 8360664832 Failed to send email for notification 22e83737-6a9f-47a5-9629-4dc76869f2b1: [Errno 61] Connection refused
INFO 2025-07-17 04:01:53,873 services 4995 8360664832 Push notification marked as sent for notification 22e83737-6a9f-47a5-9629-4dc76869f2b1
INFO 2025-07-17 04:01:54,355 services 4995 8360664832 Push notification marked as sent for notification 56d50cce-bbb2-425a-88fd-550e2167561c
INFO 2025-07-17 04:01:54,358 services 4995 8360664832 Push notification marked as sent for notification e2a4172f-94c6-4b60-a081-f3bb27f1cc0c
INFO 2025-07-17 04:01:54,361 services 4995 8360664832 Push notification marked as sent for notification 2c6f320e-d873-47b8-aac1-3450851e379d
INFO 2025-07-17 04:01:54,364 services 4995 8360664832 Push notification marked as sent for notification 9d308864-008a-4c8d-84f7-5376f6575082
INFO 2025-07-17 04:01:54,368 services 4995 8360664832 Push notification marked as sent for notification e94c72d0-1927-43b1-a9e3-f5caff3f1bb9
INFO 2025-07-17 04:01:54,371 services 4995 8360664832 Push notification marked as sent for notification 855d0791-b586-4ee1-9e4d-7aab69e94269
INFO 2025-07-17 04:01:54,374 services 4995 8360664832 Push notification marked as sent for notification 68997e04-9117-4149-971e-a20a3fc892b1
INFO 2025-07-17 04:01:54,378 services 4995 8360664832 Push notification marked as sent for notification 5528e9dc-4f8f-471e-ac04-7811bc13b228
INFO 2025-07-17 04:01:54,382 services 4995 8360664832 Push notification marked as sent for notification 83d1e081-1c3c-4d56-bef5-b3a917209f4d
INFO 2025-07-17 04:01:54,385 services 4995 8360664832 Push notification marked as sent for notification 17e4cedf-7d92-4978-8ec1-d212742bc367
WARNING 2025-07-17 04:02:08,305 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:02:08,306 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 04:02:10,118 middleware 4663 6222327808 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-17 04:02:10,395 middleware 4663 6222327808 Failed login attempt from 127.0.0.1
WARNING 2025-07-17 04:02:10,396 log 4663 6222327808 Unauthorized: /api/auth/login/
WARNING 2025-07-17 04:02:10,396 basehttp 4663 6222327808 "POST /api/auth/login/ HTTP/1.1" 401 33
INFO 2025-07-17 04:02:36,619 middleware 4663 6205501440 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-17 04:02:36,822 basehttp 4663 6205501440 "POST /api/auth/login/ HTTP/1.1" 200 1258
WARNING 2025-07-17 04:02:38,303 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:02:38,303 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:03:08,307 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:03:08,307 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 04:03:18,154 middleware 4663 6205501440 Auth attempt from 127.0.0.1 to /api/auth/login/
INFO 2025-07-17 04:03:18,369 basehttp 4663 6205501440 "POST /api/auth/login/ HTTP/1.1" 200 1258
WARNING 2025-07-17 04:03:38,317 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:03:38,318 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:04:08,308 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:04:08,309 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 04:04:17,226 middleware 4663 6205501440 Auth attempt from 127.0.0.1 to /api/auth/login/
WARNING 2025-07-17 04:04:17,512 middleware 4663 6205501440 Failed login attempt from 127.0.0.1
WARNING 2025-07-17 04:04:17,512 log 4663 6205501440 Unauthorized: /api/auth/login/
WARNING 2025-07-17 04:04:17,513 basehttp 4663 6205501440 "POST /api/auth/login/ HTTP/1.1" 401 33
WARNING 2025-07-17 04:04:38,310 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:04:38,311 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:05:08,308 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:05:08,309 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:05:38,313 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:05:38,313 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:06:08,308 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:06:08,309 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
WARNING 2025-07-17 04:06:38,309 log 4663 6205501440 Unauthorized: /api/notifications/notifications/unread_count/
WARNING 2025-07-17 04:06:38,310 basehttp 4663 6205501440 "GET /api/notifications/notifications/unread_count/ HTTP/1.1" 401 172
INFO 2025-07-17 05:28:47,226 autoreload 9798 8360664832 Watching for file changes with StatReloader
